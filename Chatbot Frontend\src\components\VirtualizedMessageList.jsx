import React, { useState, useRef, useMemo } from 'react';
import { Check, CheckCheck, Paperclip } from 'lucide-react';

/**
 * Virtualized Message List Component
 * Optimizes performance for large message lists by only rendering visible messages
 */
export default function VirtualizedMessageList({ 
  messages, 
  renderMessage, 
  itemHeight = 80, 
  containerHeight = 400,
  onScroll,
  className = ""
}) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef(null);
  
  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      messages.length
    );
    
    return { startIndex: Math.max(0, startIndex), endIndex };
  }, [scrollTop, itemHeight, containerHeight, messages.length]);
  
  // Get visible messages
  const visibleMessages = useMemo(() => {
    return messages.slice(visibleRange.startIndex, visibleRange.endIndex);
  }, [messages, visibleRange]);
  
  // Handle scroll
  const handleScroll = (e) => {
    const newScrollTop = e.target.scrollTop;
    setScrollTop(newScrollTop);
    
    if (onScroll) {
      onScroll(e);
    }
  };
  
  // Total height of all messages
  const totalHeight = messages.length * itemHeight;
  
  // Offset for visible messages
  const offsetY = visibleRange.startIndex * itemHeight;
  
  return (
    <div
      ref={containerRef}
      className={`overflow-y-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      {/* Total height container */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* Visible messages container */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleMessages.map((message, index) => (
            <div
              key={message.id || visibleRange.startIndex + index}
              style={{ height: itemHeight }}
            >
              {renderMessage(message, visibleRange.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Hook for optimized message rendering
 * Memoizes message components to prevent unnecessary re-renders
 */
export function useOptimizedMessages(messages, renderFunction) {
  return useMemo(() => {
    return messages.map((message, index) => ({
      ...message,
      component: renderFunction(message, index)
    }));
  }, [messages, renderFunction]);
}

/**
 * Message Component with React.memo for performance
 */
export const MemoizedMessage = React.memo(({ message, isFromHuman, formatTime, messageStatuses }) => {

  // Handle both mobile and desktop data formats
  const hasMedia = message.hasMedia ||
                  message.message_type === "image" ||
                  message.message_type === "document" ||
                  message.message_type === "video" ||
                  message.message_type === "audio";

  const mediaType = message.mediaInfo?.mediaType || message.message_type;
  const mediaUrl = message.mediaInfo?.fileUrl || message.file_url;
  const fileName = message.mediaInfo?.fileName || message.file_name;

  return (
    <div className={`flex ${isFromHuman ? "justify-end" : "justify-start"} message-bubble-in`}>
      <div
        className={`max-w-xs lg:max-w-md rounded-lg overflow-hidden ${
          isFromHuman
            ? "bg-blue-600 text-white"
            : "bg-white text-gray-900 border border-gray-200"
        }`}
      >
        {/* Media content */}
        {hasMedia && mediaUrl && (
          <div className="relative">
            {mediaType === "image" ? (
              <img
                src={mediaUrl}
                alt="Shared image"
                className="w-full h-auto max-h-64 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => window.open(mediaUrl, '_blank')}
                onError={(e) => {
                  console.error('Failed to load image:', mediaUrl);
                  e.target.style.display = 'none';
                }}
              />
            ) : mediaType === "video" ? (
              <video
                controls
                className="w-full h-auto max-h-64 rounded-lg"
                src={mediaUrl}
              >
                Your browser does not support the video tag.
              </video>
            ) : mediaType === "audio" ? (
              <audio
                controls
                className="w-full"
                src={mediaUrl}
              >
                Your browser does not support the audio tag.
              </audio>
            ) : (
              <div className="p-3 flex items-center gap-2 bg-gray-50">
                <Paperclip className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700 truncate">
                  {fileName || "Document"}
                </span>
              </div>
            )}
          </div>
        )}
        
        {/* Message text */}
        {message.message && (
          <div className="px-3 py-2">
            <p className="text-sm whitespace-pre-wrap break-words">
              {message.message}
            </p>
          </div>
        )}
        
        {/* Timestamp and Status */}
        <div className="px-3 pb-2">
          <div className="flex items-center justify-between">
            <p
              className={`text-xs ${
                isFromHuman ? "text-blue-100" : "text-gray-500"
              }`}
            >
              {formatTime(message.timestamp)}
            </p>
            
            {/* Message status for human messages */}
            {isFromHuman && (
              <div className="flex items-center gap-1">
                {messageStatuses[message.id] === "sent" && (
                  <Check className="w-3 h-3 text-blue-200" />
                )}
                {messageStatuses[message.id] === "delivered" && (
                  <CheckCheck className="w-3 h-3 text-blue-200" />
                )}
                {messageStatuses[message.id] === "read" && (
                  <CheckCheck className="w-3 h-3 text-green-300" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

MemoizedMessage.displayName = 'MemoizedMessage';
