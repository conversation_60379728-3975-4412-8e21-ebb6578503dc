import React, { useState } from 'react';
import Icon from './ui/icon';

export default function SimplifiedChatbotConfig({ settings, onSettingsChange, onSave, saving }) {
  const [configMode, setConfigMode] = useState('simple'); // 'simple' or 'advanced'
  
  // Simple configuration state
  const [simpleConfig, setSimpleConfig] = useState({
    businessName: '',
    businessType: 'retail',
    tone: 'friendly',
    language: 'english',
    features: {
      showPrices: true,
      showImages: true,
      takeOrders: false,
      multiLanguage: true
    }
  });

  const businessTypes = [
    { value: 'retail', label: 'Retail Store', icon: '🛍️' },
    { value: 'restaurant', label: 'Restaurant/Cafe', icon: '🍽️' },
    { value: 'service', label: 'Service Business', icon: '🔧' },
    { value: 'ecommerce', label: 'E-commerce', icon: '📦' },
    { value: 'healthcare', label: 'Healthcare', icon: '🏥' },
    { value: 'education', label: 'Education', icon: '📚' }
  ];

  const toneOptions = [
    { value: 'friendly', label: 'Friendly & Casual', description: 'Warm, approachable, like talking to a friend' },
    { value: 'professional', label: 'Professional', description: 'Polite, formal, business-appropriate' },
    { value: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic, excited about products/services' },
    { value: 'helpful', label: 'Helpful & Informative', description: 'Focus on providing detailed, useful information' }
  ];

  const languageOptions = [
    { value: 'english', label: 'English Only' },
    { value: 'multilingual', label: 'Multi-language (English, Malay, Chinese)' },
    { value: 'malay', label: 'Bahasa Malaysia Primary' },
    { value: 'chinese', label: 'Chinese Primary' }
  ];

  const generatePromptFromSimpleConfig = () => {
    const { businessName, businessType, tone, language, features } = simpleConfig;
    
    let toneInstructions = '';
    switch (tone) {
      case 'friendly':
        toneInstructions = 'Be warm, casual, and friendly. Use Malaysian expressions like "ya", "lah" naturally.';
        break;
      case 'professional':
        toneInstructions = 'Maintain a professional, polite tone. Be helpful but formal.';
        break;
      case 'enthusiastic':
        toneInstructions = 'Be energetic and excited about products/services. Use emojis and positive language.';
        break;
      case 'helpful':
        toneInstructions = 'Focus on being informative and helpful. Provide complete details.';
        break;
    }

    let languageInstructions = '';
    switch (language) {
      case 'english':
        languageInstructions = 'Respond in English only.';
        break;
      case 'multilingual':
        languageInstructions = 'Match customer language (English/Malay/Chinese/mix). Default: English.';
        break;
      case 'malay':
        languageInstructions = 'Respond primarily in Bahasa Malaysia. Use English if customer prefers.';
        break;
      case 'chinese':
        languageInstructions = 'Respond primarily in Chinese. Use English if customer prefers.';
        break;
    }

    const businessContext = businessName ? `${businessName} ` : '';
    const typeContext = businessTypes.find(t => t.value === businessType)?.label || 'business';

    return `${businessContext}${typeContext} assistant. ${toneInstructions}

LANGUAGE: ${languageInstructions}
KNOWLEDGE: Use provided knowledge base. If unsure: "Sorry, not sure about that 😅"
STYLE: Give complete info directly. No follow-ups or "want more?" questions.
${features.showImages ? 'IMAGES: Mention "I can show you pictures" when products have images available.' : ''}
${features.showPrices ? 'PRICING: Always include prices when discussing products.' : ''}

EXAMPLES: "Hi" → "Hi there! How can I help you today? 😊"`;
  };

  const handleSimpleConfigChange = (field, value) => {
    const newConfig = { ...simpleConfig, [field]: value };
    setSimpleConfig(newConfig);
    
    // Auto-generate prompt from simple config
    const generatedPrompt = generatePromptFromSimpleConfig();
    onSettingsChange({
      ...settings,
      system_prompt: generatedPrompt
    });
  };

  const handleFeatureChange = (feature, enabled) => {
    const newFeatures = { ...simpleConfig.features, [feature]: enabled };
    const newConfig = { ...simpleConfig, features: newFeatures };
    setSimpleConfig(newConfig);
    
    // Auto-generate prompt
    const generatedPrompt = generatePromptFromSimpleConfig();
    onSettingsChange({
      ...settings,
      system_prompt: generatedPrompt
    });
  };

  return (
    <div className="space-y-6">
      {/* Configuration Mode Toggle */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Chatbot Configuration</h3>
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setConfigMode('simple')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              configMode === 'simple'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Icon name="Zap" className="w-4 h-4 inline mr-2" />
            Simple Setup
          </button>
          <button
            onClick={() => setConfigMode('advanced')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              configMode === 'advanced'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Icon name="Settings" className="w-4 h-4 inline mr-2" />
            Advanced
          </button>
        </div>
      </div>

      {configMode === 'simple' ? (
        <div className="space-y-6">
          {/* Business Information */}
          <div className="card">
            <h4 className="font-semibold text-gray-900 mb-4">Business Information</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Name (Optional)
                </label>
                <input
                  type="text"
                  value={simpleConfig.businessName}
                  onChange={(e) => handleSimpleConfigChange('businessName', e.target.value)}
                  placeholder="e.g., ABC Coffee Shop"
                  className="input-field"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Type
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {businessTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => handleSimpleConfigChange('businessType', type.value)}
                      className={`p-3 rounded-lg border text-left transition-all ${
                        simpleConfig.businessType === type.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{type.icon}</span>
                        <span className="text-sm font-medium">{type.label}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Personality & Tone */}
          <div className="card">
            <h4 className="font-semibold text-gray-900 mb-4">Personality & Tone</h4>
            
            <div className="space-y-3">
              {toneOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-start gap-3 p-3 border rounded-lg cursor-pointer transition-all ${
                    simpleConfig.tone === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="tone"
                    value={option.value}
                    checked={simpleConfig.tone === option.value}
                    onChange={(e) => handleSimpleConfigChange('tone', e.target.value)}
                    className="mt-1"
                  />
                  <div>
                    <p className="font-medium text-gray-900">{option.label}</p>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Language Settings */}
          <div className="card">
            <h4 className="font-semibold text-gray-900 mb-4">Language Settings</h4>
            
            <div className="space-y-3">
              {languageOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-all ${
                    simpleConfig.language === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="language"
                    value={option.value}
                    checked={simpleConfig.language === option.value}
                    onChange={(e) => handleSimpleConfigChange('language', e.target.value)}
                  />
                  <span className="font-medium text-gray-900">{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Features */}
          <div className="card">
            <h4 className="font-semibold text-gray-900 mb-4">Features</h4>
            
            <div className="space-y-4">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={simpleConfig.features.showPrices}
                  onChange={(e) => handleFeatureChange('showPrices', e.target.checked)}
                  className="rounded"
                />
                <div>
                  <p className="font-medium text-gray-900">Show Prices</p>
                  <p className="text-sm text-gray-600">Always include pricing information when discussing products</p>
                </div>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={simpleConfig.features.showImages}
                  onChange={(e) => handleFeatureChange('showImages', e.target.checked)}
                  className="rounded"
                />
                <div>
                  <p className="font-medium text-gray-900">Product Images</p>
                  <p className="text-sm text-gray-600">Mention when product photos are available to show</p>
                </div>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={simpleConfig.features.takeOrders}
                  onChange={(e) => handleFeatureChange('takeOrders', e.target.checked)}
                  className="rounded"
                />
                <div>
                  <p className="font-medium text-gray-900">Order Processing</p>
                  <p className="text-sm text-gray-600">Enable chatbot to take and process customer orders</p>
                </div>
              </label>
            </div>
          </div>

          {/* Generated Prompt Preview */}
          <div className="card">
            <h4 className="font-semibold text-gray-900 mb-4">Generated Prompt Preview</h4>
            <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-700 font-mono">
              {settings.system_prompt || 'Configure your settings above to see the generated prompt...'}
            </div>
          </div>
        </div>
      ) : (
        <div className="card">
          <h4 className="font-semibold text-gray-900 mb-4">Advanced Configuration</h4>
          <p className="text-sm text-gray-600 mb-4">
            Edit the system prompt directly for full control over your chatbot's behavior.
          </p>
          <textarea
            rows="12"
            className="input-field resize-none font-mono text-sm"
            value={settings.system_prompt}
            onChange={(e) => onSettingsChange({ ...settings, system_prompt: e.target.value })}
            placeholder="Enter your custom system prompt..."
          />
        </div>
      )}

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={onSave}
          className="btn-primary flex items-center gap-2 px-6 py-2.5"
          disabled={saving}
        >
          {saving ? (
            <>
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Icon name="Save" className="w-4 h-4" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
