import React from "react";

export default function Privacy() {
  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-blue-100/50 min-h-screen flex flex-col justify-between">
      <section className="relative overflow-hidden py-20 lg:py-32 flex-1 flex items-center">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="relative container mx-auto px-6 flex flex-col items-center justify-center w-full">
          <div className="max-w-2xl w-full fade-in">
            <h1 className="text-4xl font-extrabold text-gray-900 mb-6 text-center">Privacy <span className="gradient-text">Policy</span></h1>
            <div className="text-gray-700 text-base leading-relaxed space-y-8">
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">1. Introduction</h2>
                <p>This Privacy Policy explains how Chilbee collects, uses, and protects your personal data in accordance with the Personal Data Protection Act 2010 (PDPA) of Malaysia and international best practices. By using our services, you consent to this Policy.</p>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">2. Information We Collect</h2>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Personal information (e.g., name, email, phone number, business details) provided during registration or use of our services.</li>
                  <li>Usage data (e.g., log files, device information, browser type, access times).</li>
                  <li><strong>We do NOT collect or store any payment or billing information.</strong></li>
                  <li>Any other information you voluntarily provide to us.</li>
                </ul>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">3. How We Use Your Information</h2>
                <ul className="list-disc pl-6 space-y-2">
                  <li>To provide, operate, and improve our SaaS platform and services.</li>
                  <li>To communicate with you regarding your account, updates, and support.</li>
                  <li>To comply with legal obligations under Malaysian law.</li>
                  <li>For analytics, research, and service improvement (data is anonymized where possible).</li>
                </ul>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">4. Data Sharing & Disclosure</h2>
                <ul className="list-disc pl-6 space-y-2">
                  <li>We do not sell your personal data to third parties.</li>
                  <li>We may share data with trusted service providers (e.g., cloud hosting) who are contractually bound to protect your data.</li>
                  <li>We may disclose data if required by Malaysian law or to protect our rights and users.</li>
                </ul>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">5. Data Security</h2>
                <ul className="list-disc pl-6 space-y-2">
                  <li>We use industry-standard security measures to protect your data from unauthorized access, disclosure, or loss.</li>
                  <li>Access to your data is restricted to authorized personnel only.</li>
                </ul>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">6. Your Rights</h2>
                <ul className="list-disc pl-6 space-y-2">
                  <li>You may request access to, correction, or deletion of your personal data at any time.</li>
                  <li>To exercise your rights, please <a href="/contact" className="text-blue-600 hover:underline">contact us</a>.</li>
                </ul>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">7. Cookies & Tracking</h2>
                <p>We use cookies and similar technologies to enhance your experience. You may disable cookies in your browser, but some features may not work properly.</p>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">8. International Transfers</h2>
                <p>Your data may be stored or processed outside Malaysia. We ensure adequate protection for all cross-border data transfers.</p>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">9. Changes to This Policy</h2>
                <p>We may update this Privacy Policy from time to time. We will notify users of material changes via email or platform notice. Continued use of Chilbee means you accept the updated Policy.</p>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">10. Contact</h2>
                <p>For privacy-related questions or requests, please <a href="/contact" className="text-blue-600 hover:underline">contact us</a> via WhatsApp or email.</p>
              </section>
              <section>
                <h2 className="font-bold text-lg mb-2 text-blue-700">11. For Meta/Facebook Users</h2>
                <p>Chilbee does not collect, store, or process any payment or billing information. We only collect basic contact and usage information necessary to provide our chatbot and automation services. If you access Chilbee via Facebook or Meta platforms, your data is handled in accordance with this policy and Meta's own privacy requirements. For any privacy concerns, please contact us directly.</p>
              </section>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 