import React from "react";
import Icon from "../components/ui/icon";

const screenshots = [
  {
    img: null, // Replace with image import or URL in the future
    title: "Dashboard Overview",
    description: "Get a quick summary of your business performance, orders, and AI activity in one glance.",
    icon: "BarChart2",
    color: "from-blue-500 to-blue-600",
    bg: "bg-blue-100/40",
  },
  {
    img: null,
    title: "AI Chat Automation",
    description: "Automate customer replies, order taking, and FAQs with our smart WhatsApp AI interface.",
    icon: "Bot",
    color: "from-green-500 to-green-600",
    bg: "bg-green-100/40",
  },
  {
    img: null,
    title: "Product & Menu Management",
    description: "Easily manage your menu, products, and pricing. Update anytime, anywhere.",
    icon: "Package",
    color: "from-purple-500 to-purple-600",
    bg: "bg-purple-100/40",
  },
  {
    img: null,
    title: "Analytics & Insights",
    description: "Track sales, customer engagement, and AI usage with beautiful analytics dashboards.",
    icon: "BarChart2",
    color: "from-indigo-500 to-indigo-600",
    bg: "bg-indigo-100/40",
  },
  {
    img: null,
    title: "WhatsApp Integration",
    description: "Seamlessly connect your business WhatsApp for direct customer engagement.",
    icon: "MessageCircle",
    color: "from-orange-500 to-orange-600",
    bg: "bg-orange-100/40",
  },
];

export default function Showcase() {
  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-blue-100/50 min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 lg:py-32">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="relative container mx-auto px-6 text-center">
          <h1 className="text-5xl lg:text-6xl font-extrabold text-gray-900 mb-8 leading-tight fade-in">
            <span className="gradient-text">Showcase</span> & System Features
          </h1>
          <p className="text-xl lg:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed fade-in delay-100">
            Explore our system's capabilities. Browse screenshots and feature highlights to see how our platform can help your business.
          </p>
        </div>
      </section>

      {/* Feature Sections */}
      <div className="space-y-24">
        {screenshots.map((shot, idx) => (
          <section
            key={shot.title}
            className={`w-full py-12 md:py-20 ${shot.bg} fade-in`}
            style={{ animationDelay: `${idx * 100}ms` }}
          >
            <div className={`container mx-auto px-6 flex flex-col md:flex-row items-center gap-12 ${idx % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
              {/* Image/Icon */}
              <div className="flex-1 flex items-center justify-center mb-8 md:mb-0">
                {shot.img ? (
                  <img src={shot.img} alt={shot.title} className="rounded-2xl shadow-xl w-full max-w-md object-cover" />
                ) : (
                  <div className={`w-72 h-48 rounded-2xl bg-gradient-to-br ${shot.color} flex items-center justify-center shadow-xl animate-pulse`}>
                    <Icon name={shot.icon} className="w-20 h-20 text-white opacity-80" />
                  </div>
                )}
              </div>
              {/* Description */}
              <div className="flex-1 text-center md:text-left">
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">{shot.title}</h2>
                <p className="text-lg text-gray-700 mb-2 max-w-xl mx-auto md:mx-0">{shot.description}</p>
              </div>
            </div>
          </section>
        ))}
      </div>

      {/* Media Placeholder Section */}
      <section className="container mx-auto px-6 py-20 text-center">
        <div className="bg-white/80 rounded-3xl shadow-xl p-10 border border-blue-100 flex flex-col items-center justify-center fade-in">
          <Icon name="Image" className="w-16 h-16 text-blue-400 mb-4 animate-pulse" />
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Add Your Own Screenshots & Videos</h2>
          <p className="text-lg text-gray-600 mb-6 max-w-xl mx-auto">
            Soon you'll be able to upload images and videos to showcase your business, menu, or customer experience. Stay tuned!
          </p>
          <div className="w-full flex flex-wrap justify-center gap-6 opacity-60">
            {/* Placeholder images */}
            <div className="w-40 h-28 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl animate-pulse"></div>
            <div className="w-40 h-28 bg-gradient-to-br from-green-100 to-green-200 rounded-xl animate-pulse"></div>
            <div className="w-40 h-28 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl animate-pulse"></div>
          </div>
        </div>
      </section>
    </div>
  );
} 