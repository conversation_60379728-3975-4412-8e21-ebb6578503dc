import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { aiService } from "../services/ai";
import { api } from "../services/api";
import { useAlertMigration } from "../hooks/useAlertMigration";
import Icon from "./ui/icon";
import { Switch } from "./ui/switch";
import { Button } from "./ui/button";

/**
 * Enhanced contact tag manager with AI auto-tagging and follow-up controls
 */
export default function ContactTagManager({ contact, onContactUpdate, onClose }) {
  const { user } = useAuth();
  const { showError, showSuccess } = useAlertMigration();

  // State
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [, setManualTags] = useState("");
  const [editingAutoTags, setEditingAutoTags] = useState(false);
  const [selectedAutoTags, setSelectedAutoTags] = useState([]);
  const [selectedManualTags, setSelectedManualTags] = useState([]);
  const [followUpEnabled, setFollowUpEnabled] = useState(false);
  const [followUpSchedules, setFollowUpSchedules] = useState([]);

  // Available AI auto tags organized by category (matching backend AUTO_TAGS exactly)
  const autoTagCategories = {
    "Lead Quality": ["hot_lead", "warm_lead", "cold_lead", "high_value_prospect"],
    "Customer Status": ["customer", "repeat_customer", "just_browsing"],
    "Buying Behavior": ["ready_to_buy", "impulse_buyer", "comparison_shopper", "research_phase"],
    "Decision Making": ["decision_maker", "needs_approval"],
    "Price Sensitivity": ["price_sensitive", "budget_conscious"],
    "Engagement": ["needs_follow_up", "follow_up_responsive", "urgent_inquiry"],
    "Support Needs": ["technical_questions", "objection_handling"]
  };

  // Get tag color based on category
  const getTagColor = (tag) => {
    for (const [category, tags] of Object.entries(autoTagCategories)) {
      if (tags.includes(tag)) {
        switch (category) {
          case "Lead Quality": return "bg-green-100 text-green-800 border-green-200";
          case "Customer Status": return "bg-blue-100 text-blue-800 border-blue-200";
          case "Buying Behavior": return "bg-purple-100 text-purple-800 border-purple-200";
          case "Decision Making": return "bg-orange-100 text-orange-800 border-orange-200";
          case "Price Sensitivity": return "bg-yellow-100 text-yellow-800 border-yellow-200";
          case "Engagement": return "bg-red-100 text-red-800 border-red-200";
          case "Support Needs": return "bg-indigo-100 text-indigo-800 border-indigo-200";
          default: return "bg-gray-100 text-gray-800 border-gray-200";
        }
      }
    }
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  // Available AI intent categories (from backend)
  const availableIntentCategories = [
    { value: "interested", label: "Interested", color: "green" },
    { value: "placed_order", label: "Placed Order", color: "blue" },
    { value: "booking", label: "Booking", color: "purple" },
    { value: "support_inquiry", label: "Support Inquiry", color: "orange" },
    { value: "price_inquiry", label: "Price Inquiry", color: "yellow" },
    { value: "product_research", label: "Product Research", color: "indigo" },
    { value: "comparison_shopping", label: "Comparison Shopping", color: "pink" },
    { value: "ready_to_purchase", label: "Ready to Purchase", color: "emerald" },
    { value: "not_interested", label: "Not Interested", color: "red" },
    { value: "no_follow_up", label: "No Follow-up", color: "gray" }
  ];

  const availableManualTags = [
    "vip", "customer", "prospect", "support", "complaint", "refund",
    "wholesale", "retail", "premium", "basic", "loyal", "new", "returning",
    "corporate", "individual", "priority", "regular"
  ];

  // Initialize state from contact
  useEffect(() => {
    if (contact) {
      setManualTags(contact.tags ? contact.tags.join(", ") : "");
      setSelectedAutoTags(contact.auto_tags || []);
      setSelectedManualTags(contact.tags || []);
      setFollowUpEnabled(contact.follow_up_enabled || false);
      fetchFollowUpSchedules();
    }
  }, [contact]);

  const getUserId = () => user?.user?.id || user?.profile?.id || user?.id;



  // Fetch follow-up schedules
  const fetchFollowUpSchedules = async () => {
    try {
      const response = await api.get(
        `/api/ai/contacts/${contact.id}/follow-ups?authId=${getUserId()}`
      );
      const data = response.data;
      if (data.success) {
        setFollowUpSchedules(data.data);
      }
    } catch (error) {
      showError("Failed to fetch follow-up schedules", error);
    }
  };

  // Trigger AI analysis
  const triggerAnalysis = async () => {
    setAnalyzing(true);
    try {
      const response = await api.post(`/api/ai/contacts/${contact.id}/analyze`, {
        authId: getUserId(),
        triggeredBy: "manual_trigger"
      });

      const data = response.data;
      if (data.success) {
        showSuccess("AI analysis completed successfully!");
        await fetchFollowUpSchedules();

        // Update contact with new AI data (simplified - only auto_tags)
        if (onContactUpdate) {
          onContactUpdate({
            ...contact,
            auto_tags: data.data.analysis.auto_tags
          });
        }
        if (onClose) onClose();
      } else {
        showError(data.error || "Failed to analyze contact");
      }
    } catch (error) {
      showError("Failed to trigger AI analysis", error);
    } finally {
      setAnalyzing(false);
    }
  };

  // Toggle follow-up
  const toggleFollowUp = async (enabled) => {
    setLoading(true);
    try {
      const response = await api.post(`/api/ai/contacts/${contact.id}/toggle-follow-up`, {
        authId: getUserId(),
        enabled
      });

      const data = response.data;
      if (data.success) {
        setFollowUpEnabled(enabled);
        showSuccess(`Follow-ups ${enabled ? 'enabled' : 'disabled'} successfully!`);

        if (onContactUpdate) {
          onContactUpdate(data.data);
        }
        if (onClose) onClose();
      } else {
        showError(data.error || "Failed to update follow-up setting");
      }
    } catch (error) {
      showError("Failed to update follow-up setting", error);
    } finally {
      setLoading(false);
    }
  };

  // Save manual tags
  const saveManualTags = async () => {
    setLoading(true);
    try {
      const data = await aiService.updateContact(contact.id, {
        authId: getUserId(),
        tags: selectedManualTags,
        name: contact.name,
        notes: contact.notes,
        isActive: contact.is_active
      });

      if (data.success) {
        showSuccess("Manual tags updated successfully!");
        if (onContactUpdate) {
          onContactUpdate(data.data);
        }
        if (onClose) onClose();
      }
    } catch (error) {
      showError("Failed to update manual tags", error);
    } finally {
      setLoading(false);
    }
  };

  // Save auto tags
  const saveAutoTags = async () => {
    setLoading(true);
    try {
      const response = await api.post(`/api/ai/contacts/${contact.id}/update-auto-tags`, {
        authId: getUserId(),
        autoTags: selectedAutoTags
      });

      const data = response.data;
      if (data.success) {
        showSuccess("AI tags updated successfully!");
        setEditingAutoTags(false);
        if (onContactUpdate) {
          onContactUpdate({
            ...contact,
            auto_tags: selectedAutoTags
          });
        }
        if (onClose) onClose();
      } else {
        showError(data.error || "Failed to update AI tags");
      }
    } catch (error) {
      showError("Failed to update AI tags", error);
    } finally {
      setLoading(false);
    }
  };

  // Toggle functions for tags
  const toggleAutoTag = (tag) => {
    setSelectedAutoTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const toggleManualTag = (tag) => {
    setSelectedManualTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  // Get engagement score color
  const getEngagementColor = (score) => {
    if (score >= 0.7) return "text-green-600";
    if (score >= 0.4) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <Icon name="User" className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">Contact Management</h3>
            <p className="text-sm text-gray-600">{contact?.name || contact?.phone_number || 'Unknown Contact'}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg p-2"
        >
          <Icon name="X" className="w-5 h-5" />
        </Button>
      </div>

      {/* AI Analysis Section */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-6 shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Icon name="Brain" className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">AI Analysis</h4>
              <p className="text-sm text-gray-600">Automated customer insights</p>
            </div>
          </div>
          <Button
            onClick={triggerAnalysis}
            disabled={analyzing}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
          >
            {analyzing ? (
              <>
                <Icon name="Loader2" className="w-4 h-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Icon name="RefreshCw" className="w-4 h-4 mr-2" />
                Analyze Now
              </>
            )}
          </Button>
        </div>

        {/* AI Generated Tags */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-semibold text-gray-800">AI Generated Tags</span>
            </div>
            <Button
              onClick={() => setEditingAutoTags(!editingAutoTags)}
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 text-xs font-medium px-3 py-1 h-auto"
            >
              <Icon name={editingAutoTags ? "X" : "Edit2"} className="w-3 h-3 mr-1" />
              {editingAutoTags ? "Cancel" : "Edit"}
            </Button>
          </div>

          {editingAutoTags ? (
            <div className="space-y-4">
              {/* Organized AI tags by category */}
              {Object.entries(autoTagCategories).map(([category, tags]) => (
                <div key={category} className="space-y-2">
                  <h5 className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                    {category}
                  </h5>
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                    {tags.map((tag) => (
                      <button
                        key={tag}
                        onClick={() => toggleAutoTag(tag)}
                        className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 border-2 text-center ${
                          selectedAutoTags.includes(tag)
                            ? 'bg-blue-600 text-white border-blue-600 shadow-lg shadow-blue-200 transform scale-102'
                            : 'bg-white text-gray-700 border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md'
                        }`}
                        title={tag.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                      >
                        {tag.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                      </button>
                    ))}
                  </div>
                </div>
              ))}

              {/* Helper text */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-xs text-blue-700 flex items-center">
                  <Icon name="Info" className="w-4 h-4 mr-2" />
                  Select tags that best describe this customer's behavior and characteristics. These help improve follow-up targeting and personalization.
                </p>
              </div>

              {/* Save/Cancel buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={saveAutoTags}
                  disabled={loading}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
                >
                  {loading ? (
                    <Icon name="Loader2" className="w-3 h-3 animate-spin" />
                  ) : (
                    "Save Changes"
                  )}
                </Button>
                <Button
                  onClick={() => {
                    setEditingAutoTags(false);
                    setSelectedAutoTags(contact.auto_tags || []);
                  }}
                  variant="outline"
                  size="sm"
                  className="bg-white text-blue-700 border-blue-200 shadow-md hover:shadow-lg transition-all duration-200"
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {contact.auto_tags && contact.auto_tags.length > 0 ? (
                <>
                  <div className="flex flex-wrap gap-2">
                    {contact.auto_tags.slice(0, 8).map((tag, index) => (
                      <span
                        key={index}
                        className={`inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium border ${getTagColor(tag)}`}
                      >
                        <Icon name="Zap" className="w-3 h-3 mr-1" />
                        {tag.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                      </span>
                    ))}
                  </div>
                  {contact.auto_tags.length > 8 && (
                    <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                      <p className="text-xs text-gray-600 mb-2 font-medium">Additional tags ({contact.auto_tags.length - 8} more):</p>
                      <div className="flex flex-wrap gap-1">
                        {contact.auto_tags.slice(8).map((tag, index) => (
                          <span
                            key={index + 8}
                            className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${getTagColor(tag)}`}
                          >
                            {tag.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-3 text-gray-500 rounded-lg mx-auto">
                  <Icon name="Brain" className="w-6 h-6 mx-auto mb-1 text-gray-400" />
                  <p className="text-sm">No AI tags yet</p>
                  <p className="text-xs">Click "Analyze Now" to generate tags</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Intent Category & Engagement Score */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
          {contact.ai_intent_category && (
            <div className="bg-white rounded-xl p-4 border-2 border-gray-100 shadow-sm">
              <p className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <Icon name="Target" className="w-4 h-4 mr-2 text-gray-500" />
                Intent Category
              </p>
              {(() => {
                const intentCategory = availableIntentCategories.find(cat => cat.value === contact.ai_intent_category);
                if (!intentCategory) return null;

                return (
                  <span className={`inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium border ${
                    intentCategory.color === 'green' ? 'bg-green-50 text-green-700 border-green-200' :
                    intentCategory.color === 'blue' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                    intentCategory.color === 'purple' ? 'bg-purple-50 text-purple-700 border-purple-200' :
                    intentCategory.color === 'red' ? 'bg-red-50 text-red-700 border-red-200' :
                    'bg-gray-50 text-gray-700 border-gray-200'
                  }`}>
                    {intentCategory.label}
                  </span>
                );
              })()}
            </div>
          )}

          {contact.ai_engagement_score !== undefined && contact.ai_engagement_score !== null && (
            <div className="bg-white rounded-xl p-4 border-2 border-gray-100 shadow-sm">
              <p className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <Icon name="TrendingUp" className="w-4 h-4 mr-2 text-gray-500" />
                Engagement Score
              </p>
              <div className="flex items-center gap-3">
                <span className={`text-xl font-bold ${getEngagementColor(contact.ai_engagement_score)}`}>
                  {(contact.ai_engagement_score * 100).toFixed(0)}%
                </span>
                <div className="flex-1 bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${
                      contact.ai_engagement_score >= 0.7 ? 'bg-green-500' :
                      contact.ai_engagement_score >= 0.4 ? 'bg-yellow-500' :
                      'bg-red-500'
                    }`}
                    style={{ width: `${contact.ai_engagement_score * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Follow-up Control */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border-2 border-blue-200 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <Icon name="MessageCircle" className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">Automated Follow-up</h4>
              <p className="text-sm text-gray-600 mt-1">
                Smart follow-ups based on AI analysis
              </p>
            </div>
          </div>
          <Switch
            checked={followUpEnabled}
            onCheckedChange={toggleFollowUp}
            disabled={loading}
            className="data-[state=checked]:bg-green-600"
          />
        </div>

        {/* Follow-up Status */}
        {followUpSchedules.length > 0 && (
          <div className="mt-3 pt-3 border-t border-green-200">
            <p className="text-sm text-gray-600 mb-2">Scheduled Follow-ups:</p>
            <div className="space-y-1">
              {followUpSchedules.slice(0, 3).map((schedule, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <span className="text-gray-700">
                    {schedule.follow_up_type.replace('_', ' ')}
                  </span>
                  <span className={`px-2 py-1 rounded ${
                    schedule.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    schedule.status === 'executed' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {schedule.status}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Manual Tags */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border-2 border-blue-200 p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icon name="Tag" className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">Manual Tags</h4>
            <p className="text-sm text-gray-600">Custom tags for contact organization</p>
          </div>
        </div>

        {/* Toggle buttons for manual tags */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 mb-6">
          {availableManualTags.map((tag) => (
            <button
              key={tag}
              onClick={() => toggleManualTag(tag)}
              className={`px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 border-2 ${
                selectedManualTags.includes(tag)
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg shadow-blue-200 transform scale-102'
                  : 'bg-white text-gray-700 border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md'
              }`}
            >
              {tag.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
            </button>
          ))}
        </div>

        {/* Save button */}
        <div className="mb-4">
          <Button
            onClick={saveManualTags}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 py-3 text-base font-semibold"
          >
            {loading ? (
              <>
                <Icon name="Loader2" className="w-5 h-5 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                Save Manual Tags
              </>
            )}
          </Button>
        </div>

        {/* Display current manual tags */}
        <div className="bg-white rounded-xl p-4 border-2 border-gray-100 shadow-sm">
          <p className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
            <Icon name="Check" className="w-4 h-4 mr-2 text-blue-600" />
            Selected Tags
          </p>
          <div className="flex flex-wrap gap-2">
            {selectedManualTags.length > 0 ? (
              selectedManualTags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium border border-blue-200 shadow-sm"
                >
                  <Icon name="Tag" className="w-3 h-3 mr-1" />
                  {tag.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                </span>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500 w-full">
                <Icon name="Tag" className="w-6 h-6 mx-auto mb-2 text-gray-400" />
                <p className="text-sm">No manual tags selected</p>
                <p className="text-xs text-gray-400 mt-1">Click tags above to select them</p>
              </div>
            )}
          </div>

          <p className="text-xs text-gray-500 mt-2 bg-white rounded p-2">
            💡 Click tags above to add/remove them for this contact
          </p>
        </div>
      </div>


    </div>
  );
}
