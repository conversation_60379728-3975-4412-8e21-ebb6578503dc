// Shared API client configuration
import axios from "axios";

// Configure the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:3000";

// Token management utility
export const tokenManager = {
  getToken: () => localStorage.getItem("access_token"),
  setToken: (token) => localStorage.setItem("access_token", token),
  removeToken: () => localStorage.removeItem("access_token"),
  getAdminId: () => localStorage.getItem("admin_id"),
  setAdminId: (adminId) => localStorage.setItem("admin_id", adminId),
  removeAdminId: () => localStorage.removeItem("admin_id"),
  getUserToken: () => localStorage.getItem("access_token"),
};

// Create a shared axios instance with base configuration
export const createApiClient = (config = {}) => {
  const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
    ...config,
  });

  return api;
};

// Create the main API client for general use
export const api = createApiClient();

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.getToken();
    if (token) {
      // Use Authorization header for most endpoints
      if (
        config.url?.includes("/api/ai") ||
        config.url?.includes("/api/auth/change")
      ) {
        config.headers.Authorization = `Bearer ${token}`;
      } else {
        // Use access_token header for legacy endpoints
        config.headers.access_token = token;
      }
    }
    return config;
  },
  (error) => Promise.reject(error),
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      tokenManager.removeToken();
      window.location.href = "/login";
    }
    return Promise.reject(error);
  },
);

// Create admin API client with special headers
export const createAdminApiClient = () => {
  const adminApi = createApiClient();

  adminApi.interceptors.request.use((config) => {
    const adminToken = import.meta.env.VITE_ADMIN_TOKEN;
    const userToken = tokenManager.getUserToken();
    const adminId =
      tokenManager.getAdminId() || import.meta.env.VITE_ADMIN_ID || "admin";

    // Add admin headers for admin endpoints
    if (adminToken && config.url?.includes("/api/admin")) {
      config.headers.adminid = adminId;
      config.headers.admintoken = adminToken;
    }

    // Add Bearer token for auth endpoints (like suspend user)
    if (userToken && config.url?.includes("/api/auth")) {
      config.headers.Authorization = `Bearer ${userToken}`;
    }

    return config;
  });

  return adminApi;
};

export default api;
