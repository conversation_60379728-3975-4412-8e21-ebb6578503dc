import { useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useAlertMigration } from "../hooks/useAlertMigration";
import { InlineLoader } from "./ui/loader";
import { Bot, Lock } from "lucide-react";
import Icon from "./ui/icon";

export default function AuthForm({ type, onSuccess }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [phone, setPhone] = useState("");
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  const { login, register } = useAuth();
  const { showError } = useAlertMigration();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      let response;
      if (type === "register") {
        response = await register(email, password, phone, name);
      } else if (type === "login") {
        response = await login(email, password);
      }
      if (onSuccess) onSuccess(response);
    } catch (err) {
      showError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <Bot className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          {type === "login" ? "Welcome back" : "Create your account"}
        </h2>
        <p className="text-gray-600">
          {type === "login"
            ? "Sign in to your Chilbee account"
            : "Start your AI journey today!"}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="card space-y-6">
        {type === "register" && (
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-semibold text-gray-700 mb-2"
            >
              Name
            </label>
            <div className="relative">
              <input
                id="name"
                type="text"
                className="input-field"
                style={{ paddingLeft: "3rem" }}
                placeholder="Enter your name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
              <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                <Icon name="User" className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>
        )}

        {/* Email Input */}
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-semibold text-gray-700 mb-2"
          >
            Email address
          </label>
          <div className="relative">
            <input
              id="email"
              type="email"
              className="input-field"
              style={{ paddingLeft: "3rem" }}
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
              <Icon name="Mail" className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Phone Input (Register only) */}
        {type === "register" && (
          <div>
            <label
              htmlFor="phone"
              className="block text-sm font-semibold text-gray-700 mb-2"
            >
              Phone number <span className="text-gray-400 font-normal"></span>
            </label>
            <div className="relative">
              <input
                id="phone"
                type="tel"
                className="input-field"
                style={{ paddingLeft: "3rem" }}
                placeholder="Enter your phone number"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
              />
              <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                <Icon name="Phone" className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>
        )}

        {/* Password Input */}
        <div>
          <label
            htmlFor="password"
            className="block text-sm font-semibold text-gray-700 mb-2"
          >
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              type="password"
              className="input-field"
              style={{ paddingLeft: "3rem" }}
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
              <Icon name="Lock" className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="btn-primary w-full text-lg py-4"
          disabled={loading}
        >
          {loading ? (
            <div className="flex items-center justify-center gap-3">
              <InlineLoader size="sm" />
              <span>
                {type === "login" ? "Signing in..." : "Creating account..."}
              </span>
            </div>
          ) : (
            <span>{type === "login" ? "Sign In" : "Create Account"}</span>
          )}
        </button>

        {/* Additional Info */}
        {type === "register" && (
          <div className="text-center">
            <p className="text-sm text-gray-500">
              By creating an account, you agree to our{" "}
              <a
                href="#"
                className="text-blue-600 hover:text-blue-700 font-semibold"
              >
                Terms of Service
              </a>{" "}
              and{" "}
              <a
                href="#"
                className="text-blue-600 hover:text-blue-700 font-semibold"
              >
                Privacy Policy
              </a>
            </p>
          </div>
        )}
      </form>
    </div>
  );
}
