import express from "express";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

//Link files
import aiRouter from "./routes/ai.js";
import authRouter from "./routes/auth.js";
import productsRouter from "./routes/products.js";
import adminRouter from "./routes/admin.js";

// Import follow-up processor to start background processing
import "./utils/followUpProcessor.js";

const app = express();

// CORS middleware - must be before other middleware
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "PUT, GET, POST, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, access_token, admintoken, adminid, adminToken, adminId, x-auth-id");

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
const PORT = process.env.PORT || 3000;

//Routes
app.use("/api/ai", aiRouter);
app.use("/api/auth", authRouter);
app.use("/api/products", productsRouter);
app.use("/api/admin", adminRouter);

//Start server
app.listen(PORT, () => {
  console.log("Server Listening on PORT:", PORT);
});