import React from 'react';
import { useMobileAlert } from '../contexts/MobileAlertContext';

/**
 * Mobile Alert Demo Component
 * For testing and showcasing the mobile alert system
 */
export default function MobileAlertDemo() {
  const { 
    success, 
    error, 
    warning, 
    info, 
    toast, 
    quickSuccess, 
    quickError, 
    quickInfo, 
    persistent,
    clearAllAlerts 
  } = useMobileAlert();

  const demoAlerts = [
    {
      name: 'Success Alert',
      action: () => success('Operation completed successfully!', { title: 'Success' }),
      color: 'bg-green-500'
    },
    {
      name: 'Error Alert',
      action: () => error('Something went wrong. Please try again.', { title: 'Error' }),
      color: 'bg-red-500'
    },
    {
      name: 'Warning Alert',
      action: () => warning('Please check your internet connection.', { title: 'Warning' }),
      color: 'bg-yellow-500'
    },
    {
      name: 'Info Alert',
      action: () => info('New features are available in settings.', { title: 'Info' }),
      color: 'bg-blue-500'
    },
    {
      name: 'Quick Success',
      action: () => quickSuccess('Saved!'),
      color: 'bg-green-400'
    },
    {
      name: 'Quick Error',
      action: () => quickError('Failed to save'),
      color: 'bg-red-400'
    },
    {
      name: 'Quick Info',
      action: () => quickInfo('Processing...'),
      color: 'bg-blue-400'
    },
    {
      name: 'Toast Message',
      action: () => toast('This is a toast message', 'info'),
      color: 'bg-gray-500'
    },
    {
      name: 'Persistent Alert',
      action: () => persistent('This alert stays until dismissed', 'warning', 'Important'),
      color: 'bg-orange-500'
    },
    {
      name: 'Top Position',
      action: () => success('Alert from the top!', { position: 'top' }),
      color: 'bg-purple-500'
    },
    {
      name: 'Center Position',
      action: () => warning('Centered alert', { position: 'center', title: 'Attention' }),
      color: 'bg-indigo-500'
    }
  ];

  return (
    <div className="p-4 space-y-4">
      <div className="text-center mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2">Mobile Alert System Demo</h2>
        <p className="text-sm text-gray-600">
          Test different types of mobile-optimized alerts
        </p>
      </div>

      <div className="grid grid-cols-2 gap-3">
        {demoAlerts.map((demo, index) => (
          <button
            key={index}
            onClick={demo.action}
            className={`${demo.color} text-white px-4 py-3 rounded-lg font-medium text-sm shadow-sm hover:opacity-90 transition-opacity active:scale-95 transform`}
          >
            {demo.name}
          </button>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <button
          onClick={clearAllAlerts}
          className="w-full bg-gray-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors"
        >
          Clear All Alerts
        </button>
      </div>

      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Features:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Swipe to dismiss (up for bottom alerts, down for top alerts)</li>
          <li>• Auto-dismiss with progress bar</li>
          <li>• Touch-friendly design</li>
          <li>• Multiple positions (top, center, bottom)</li>
          <li>• Smooth animations</li>
          <li>• Maximum 3 alerts at once</li>
          <li>• Persistent alerts for important messages</li>
        </ul>
      </div>
    </div>
  );
}
