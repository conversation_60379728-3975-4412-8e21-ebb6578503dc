import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { aiService } from "../../services/ai";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { formatAnalyticsDate } from "../../utils/dateFormatter";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { MessageCircle, Users, BarChart2 } from "lucide-react";
import Icon from "../../components/ui/icon";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement,
  Filler,
} from "chart.js";
import { Bar, Line } from "react-chartjs-2";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement,
  Filler,
);

export default function Analytics() {
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);
  const [selectedPeriod, setSelectedPeriod] = useState("30days");
  const { showError } = useAlertMigration();

  // Real data states
  const [analyticsData, setAnalyticsData] = useState({
    overview: null,
    dailyStats: [],
    monthlyStats: [],
    messageStats: [],
    modelUsage: [],
  });

  // Fetch analytics data from API
  const fetchAnalyticsData = async () => {
    const userId = user?.user?.id || user?.profile?.id || user?.id;
    if (!userId) {
      showError("User authentication required");
      setLoading(false);
      return;
    }

    try {
      // Fetch all data in parallel using AI service
      const [overviewData, dailyData, monthlyData, messagesData] =
        await Promise.all([
          aiService.getStatisticsOverview(userId),
          aiService.getDailyStatistics({ authId: userId, limit: 30 }),
          aiService.getMonthlyStatistics({ authId: userId, limit: 12 }),
          aiService.getMessageStatistics({ authId: userId, limit: 100 }),
        ]);

      // Check for API errors
      if (!overviewData.success)
        throw new Error(overviewData.error || "Failed to fetch overview data");
      if (!dailyData.success)
        throw new Error(dailyData.error || "Failed to fetch daily data");
      if (!monthlyData.success)
        throw new Error(monthlyData.error || "Failed to fetch monthly data");
      if (!messagesData.success)
        throw new Error(messagesData.error || "Failed to fetch messages data");

      // Process model usage from message statistics
      const modelUsage = {};
      messagesData.data.forEach((stat) => {
        if (stat.model_used && stat.message_type === "outgoing") {
          modelUsage[stat.model_used] = (modelUsage[stat.model_used] || 0) + 1;
        }
      });

      const modelUsageArray = Object.entries(modelUsage)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([model, count]) => ({ model, count }));

      setAnalyticsData({
        overview: overviewData.data,
        dailyStats: dailyData.data || [],
        monthlyStats: monthlyData.data || [],
        messageStats: messagesData.data || [],
        modelUsage: modelUsageArray,
      });
    } catch (error) {
      showError(error.message || "Failed to fetch analytics data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchAnalyticsData();
    }
  }, [user]);

  // Refresh data when period changes
  useEffect(() => {
    if (user && !loading) {
      setLoading(true);
      fetchAnalyticsData();
    }
  }, [selectedPeriod]);

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num?.toString() || "0";
  };

  // Prepare chart data
  const prepareChartData = () => {
    if (!dailyStats.length) return null;

    const sortedStats = dailyStats
      .slice()
      .sort((a, b) => new Date(a.date) - new Date(b.date))
      .slice(-14); // Last 14 days

    const labels = sortedStats.map((stat) => formatAnalyticsDate(stat.date));
    const data = sortedStats.map((stat) => stat.total_messages);

    return {
      labels,
      datasets: [
        {
          label: "Messages",
          data,
          backgroundColor: "rgba(59, 130, 246, 0.8)",
          borderColor: "rgba(59, 130, 246, 1)",
          borderWidth: 2,
          borderRadius: 4,
          borderSkipped: false,
        },
      ],
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(59, 130, 246, 1)",
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function (context) {
            return `${context.dataset.label}: ${formatNumber(context.parsed.y)}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 11,
          },
          maxRotation: 45,
        },
      },
      y: {
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 11,
          },
          callback: function (value) {
            return formatNumber(value);
          },
        },
        beginAtZero: true,
      },
    },
    interaction: {
      intersect: false,
      mode: "index",
    },
  };

  const MetricCard = ({ title, value, icon, color, change, subtitle }) => (
    <div className="card group">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-3xl font-bold text-${color}-600 mb-2`}>
            {typeof value === "string" ? value : formatNumber(value)}
          </p>
          <div className="flex items-center gap-2">
            <p className="text-xs text-gray-500">{subtitle}</p>
            {change !== undefined && change !== null && (
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  change >= 0
                    ? "bg-green-100 text-green-700"
                    : "bg-red-100 text-red-700"
                }`}
              >
                {change >= 0 ? "+" : ""}
                {change}%
              </span>
            )}
          </div>
        </div>
        <div
          className={`w-12 h-12 bg-${color}-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}
        >
          {icon}
        </div>
      </div>
    </div>
  );

  const ProgressBar = ({ label, value, maxValue, color = "blue" }) => {
    const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
    return (
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm text-gray-600 min-w-0 flex-1">{label}</span>
        <div className="flex items-center gap-3 flex-shrink-0">
          <div className="w-24 bg-gray-200 rounded-full h-2.5">
            <div
              className={`bg-${color}-500 h-2.5 rounded-full transition-all duration-500`}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium text-gray-900 w-12 text-right">
            {formatNumber(value)}
          </span>
        </div>
      </div>
    );
  };

  if (showLoadingState) {
    return <PageSkeleton />;
  }

  const { overview, dailyStats, monthlyStats, modelUsage } = analyticsData;
  const chartData = prepareChartData();

  // If no data available, show friendly message
  if (!overview) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Analytics & Insights
            </h1>
            <p className="text-gray-600">
              Track your chatbot's performance and usage patterns in real-time.
            </p>
          </div>
        </div>

        <div className="text-center py-16">
          <Icon
            name="BarChart2"
            className="w-16 h-16 text-gray-400 mx-auto mb-4"
          />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Analytics Data Yet
          </h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Start using your chatbot to see detailed analytics and insights
            about your conversations.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 relative">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Analytics & Insights
          </h1>
          <p className="text-gray-600">
            Track your chatbot's performance and usage patterns in real-time.
          </p>
        </div>

        <div className="flex gap-3 flex-shrink-0 relative">
          <div className="relative">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-[150px] h-10 bg-white border border-gray-200">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">Last 7 Days</SelectItem>
                <SelectItem value="30days">Last 30 Days</SelectItem>
                <SelectItem value="90days">Last 90 Days</SelectItem>
                <SelectItem value="1year">Last Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Error messages now handled by global AlertContainer */}

      {overview && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <MetricCard
              title="Total Messages"
              value={overview.thisMonth?.total_messages || 0}
              icon={<MessageCircle className="w-6 h-6 text-blue-600" />}
              color="blue"
              subtitle="This month"
            />

            <MetricCard
              title="Unique Contacts"
              value={overview.thisMonth?.unique_contacts || 0}
              icon={<Users className="w-6 h-6 text-green-600" />}
              color="green"
              subtitle="Active users"
            />

            <MetricCard
              title="Response Rate"
              value={overview.today?.avg_response_time_ms || 0}
              icon={
                <Icon name="BarChart2" className="w-6 h-6 text-indigo-600" />
              }
              color="indigo"
              subtitle="Avg today's response time"
            />
          </div>

          {/* Performance Metrics & Model Usage */}
          <div className="grid lg:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Performance Metrics
              </h3>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Knowledge Hit Rate
                    </span>
                    <span className="text-sm font-bold text-blue-600">
                      {overview.performance?.knowledgeHitRate || 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-500 h-3 rounded-full transition-all duration-700"
                      style={{
                        width: `${overview.performance?.knowledgeHitRate || 0}%`,
                      }}
                    ></div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">
                      {overview.performance?.avgTokensPerMessage || 0}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Avg Tokens/Message
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">
                      {overview.thisMonth?.new_contacts || 0}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">New Contacts</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Model Usage Distribution
              </h3>
              <div className="space-y-4">
                {modelUsage.length > 0 ? (
                  modelUsage.map((model, index) => (
                    <ProgressBar
                      key={model.model}
                      label={model.model}
                      value={model.count}
                      maxValue={modelUsage[0].count}
                      color={["blue", "green", "purple", "orange"][index % 4]}
                    />
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      No model usage data available
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Daily Activity Chart */}
          <div className="card">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6 relative">
              <h3 className="text-xl font-semibold text-gray-900 flex-1 min-w-0">
                Daily Activity Trends
              </h3>
            </div>

            {chartData && dailyStats.length > 0 ? (
              <div className="h-80">
                <Bar data={chartData} options={chartOptions} />
              </div>
            ) : (
              <div className="text-center py-12 rounded-xl">
                <Icon
                  name="BarChart2"
                  className="w-12 h-12 text-gray-400 mx-auto mb-4"
                />
                <p className="text-gray-500 text-lg">
                  No activity data available
                </p>
              </div>
            )}
          </div>

          {/* Monthly Overview Table */}
          <div className="card">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              Monthly Overview
            </h3>
            {monthlyStats.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b-2 border-gray-100">
                      <th className="text-left text-sm font-semibold text-gray-700 pb-4 px-2">
                        Month
                      </th>
                      <th className="text-right text-sm font-semibold text-gray-700 pb-4 px-2">
                        Messages
                      </th>
                      <th className="text-right text-sm font-semibold text-gray-700 pb-4 px-2">
                        Contacts
                      </th>
                      <th className="text-right text-sm font-semibold text-gray-700 pb-4 px-2">
                        New Contacts
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {monthlyStats.map((stat, index) => (
                      <tr
                        key={`${stat.year}-${stat.month}`}
                        className="border-b border-gray-50 hover:bg-gray-50 transition-colors"
                      >
                        <td className="py-4 px-2 text-sm font-medium text-gray-900">
                          {new Date(
                            stat.year,
                            stat.month - 1,
                          ).toLocaleDateString("en-US", {
                            month: "long",
                            year: "numeric",
                          })}
                          {index === 0 && (
                            <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                              Current
                            </span>
                          )}
                        </td>
                        <td className="py-4 px-2 text-sm text-gray-600 text-right font-medium">
                          {formatNumber(stat.total_messages)}
                        </td>
                        <td className="py-4 px-2 text-sm text-gray-600 text-right font-medium">
                          {formatNumber(stat.unique_contacts)}
                        </td>
                        <td className="py-4 px-2 text-sm text-gray-600 text-right font-medium">
                          {formatNumber(stat.new_contacts)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12 rounded-xl">
                <Icon
                  name="BarChart2"
                  className="w-12 h-12 text-gray-400 mx-auto mb-4"
                />
                <p className="text-gray-500 text-lg">
                  No monthly data available
                </p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
