import { createClient } from "@supabase/supabase-js";
import { OpenAI } from "openai";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname } from "path";

dotenv.config();

// Initialize clients
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY,
);

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// ⚠️ IMPORTANT: Replace this with your actual auth ID for testing
const TEST_AUTH_ID = "1ec4f30c-188b-42fb-9879-62c119e6f00f";

// Dummy Knowledge Base Data
const knowledgeBaseEntries = [
  {
    title: "Store Hours and Contact Information",
    content: `Our store is open Monday to Friday from 8:00 AM to 8:00 PM, and weekends from 9:00 AM to 6:00 PM. We are closed on public holidays. You can contact us at +60123456789 or email <NAME_EMAIL>. Our store is located at 123 Main Street, Kuala Lumpur, Malaysia 50000.`,
  },
  {
    title: "Coffee Bean Origins and Quality",
    content: `We source our coffee beans from premium farms around the world. Our Arabica beans come from Colombia, Ethiopia, and Guatemala, known for their smooth and rich flavors. Our Robusta beans are sourced from Vietnam and Brazil, offering a stronger, more intense taste. All our beans are freshly roasted in-house weekly to ensure maximum freshness and flavor.`,
  },
  {
    title: "Brewing Methods and Equipment",
    content: `We offer various brewing methods including espresso, French press, pour-over, and cold brew. Our espresso machine is a professional-grade La Marzocco, ensuring consistent quality. For home brewing, we recommend a 1:15 coffee to water ratio for drip coffee and 1:2 for espresso. Water temperature should be between 195-205°F (90-96°C) for optimal extraction.`,
  },
  {
    title: "Milk Alternatives and Dietary Options",
    content: `We offer various milk alternatives including oat milk, almond milk, soy milk, and coconut milk. All our milk alternatives are organic and unsweetened. We also have sugar-free syrups (vanilla, caramel, hazelnut) and can accommodate dietary restrictions. Our pastries include gluten-free and vegan options clearly marked on our menu.`,
  },
  {
    title: "Loyalty Program and Discounts",
    content: `Join our Coffee Lovers Club to earn points with every purchase! Earn 1 point for every RM1 spent. Collect 100 points to get a free coffee. Members get 10% discount on coffee beans, early access to seasonal drinks, and a free birthday drink. Students get 15% discount with valid student ID. Senior citizens (60+) get 10% discount.`,
  },
  {
    title: "Delivery and Pickup Options",
    content: `We offer delivery within 10km radius for orders above RM25. Delivery fee is RM5 and takes 30-45 minutes. Free delivery for orders above RM50. We also offer curbside pickup - just call us when you arrive. Online orders can be placed through our website or WhatsApp. Payment methods include cash, card, e-wallet (GrabPay, Touch n Go, Boost).`,
  },
  {
    title: "Seasonal Drinks and Specials",
    content: `Our seasonal menu changes quarterly. Current specials include Pumpkin Spice Latte (October-December), Peppermint Mocha (December-February), and Iced Matcha Latte (March-May). We also have weekly specials every Monday - buy one get one 50% off on all frappuccinos. Happy hour is 3-5 PM with 20% off all cold drinks.`,
  },
  {
    title: "Coffee Subscription Service",
    content: `Subscribe to our monthly coffee delivery service! Choose from our signature blends or single-origin options. Subscriptions start at RM45/month for 250g of coffee beans. Free delivery included. You can pause, modify, or cancel anytime. Subscribers get exclusive access to limited edition roasts and 15% discount on additional purchases.`,
  },
  {
    title: "Barista Training and Coffee Education",
    content: `We offer barista training workshops every Saturday at 10 AM. Learn latte art, proper espresso extraction, and milk steaming techniques. Workshop fee is RM80 per person, includes materials and coffee. We also host coffee cupping sessions monthly to taste and compare different origins. Private group sessions available for corporate events.`,
  },
  {
    title: "Equipment Sales and Maintenance",
    content: `We sell home brewing equipment including French presses (RM45-85), pour-over sets (RM35-65), and espresso machines (RM800-3500). All equipment comes with a 1-year warranty. We provide equipment maintenance and repair services. Coffee grinder cleaning and calibration available for RM25. We also rent equipment for events.`,
  },
];

// Dummy Product Data
const products = [
  {
    name: "Ethiopian Single Origin",
    description:
      "Premium single-origin coffee from Ethiopian highlands. Notes of blueberry, chocolate, and wine. Light to medium roast.",
    price: 28.0,
    category: "Coffee Beans",
    stock_quantity: 50,
    is_active: true,
  },
  {
    name: "Colombian Supremo",
    description:
      "Full-bodied Colombian coffee with caramel sweetness and nutty undertones. Medium roast, perfect for espresso.",
    price: 25.0,
    category: "Coffee Beans",
    stock_quantity: 75,
    is_active: true,
  },
  {
    name: "Guatemala Antigua",
    description:
      "Volcanic soil-grown coffee with smoky complexity and spicy finish. Medium-dark roast.",
    price: 30.0,
    category: "Coffee Beans",
    stock_quantity: 40,
    is_active: true,
  },
  {
    name: "House Blend Espresso",
    description:
      "Our signature espresso blend combining Brazilian and Colombian beans. Rich crema and balanced flavor.",
    price: 22.0,
    category: "Coffee Beans",
    stock_quantity: 100,
    is_active: true,
  },
  {
    name: "Decaf Swiss Water Process",
    description:
      "Chemical-free decaffeinated coffee using Swiss Water Process. Maintains full flavor without caffeine.",
    price: 26.0,
    category: "Coffee Beans",
    stock_quantity: 30,
    is_active: true,
  },
  {
    name: "Cold Brew Blend",
    description:
      "Specially roasted blend for cold brewing. Smooth, low-acid, naturally sweet. Coarse ground available.",
    price: 24.0,
    category: "Coffee Beans",
    stock_quantity: 60,
    is_active: true,
  },
  {
    name: "Premium Americano",
    description:
      "Rich espresso shots with hot water. Choose from our single-origin or house blend.",
    price: 12.0,
    category: "Hot Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "Cappuccino",
    description:
      "Perfect balance of espresso, steamed milk, and milk foam. Available in regular or large size.",
    price: 14.0,
    category: "Hot Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "Caffe Latte",
    description:
      "Smooth espresso with steamed milk and light foam. Choice of milk alternatives available.",
    price: 15.0,
    category: "Hot Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "Mocha Deluxe",
    description:
      "Rich espresso with premium chocolate, steamed milk, and whipped cream. A chocolate lover's dream.",
    price: 17.0,
    category: "Hot Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "Iced Coffee",
    description:
      "Refreshing cold coffee served over ice. Choose your preferred brewing method and strength.",
    price: 11.0,
    category: "Cold Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "Frappuccino",
    description:
      "Blended ice coffee drink with milk and flavor syrup. Topped with whipped cream.",
    price: 16.0,
    category: "Cold Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "Cold Brew Coffee",
    description:
      "Smooth, slow-steeped cold coffee with low acidity. Served over ice with optional milk.",
    price: 13.0,
    category: "Cold Drinks",
    stock_quantity: null,
    is_active: true,
  },
  {
    name: "French Press (350ml)",
    description:
      "Classic French press coffee maker. Stainless steel construction with heat-resistant glass.",
    price: 65.0,
    category: "Equipment",
    stock_quantity: 25,
    is_active: true,
  },
  {
    name: "Pour Over Set",
    description:
      "Complete pour-over kit with dripper, filters, and measuring cup. Perfect for home brewing.",
    price: 45.0,
    category: "Equipment",
    stock_quantity: 35,
    is_active: true,
  },
  {
    name: "Coffee Grinder Manual",
    description:
      "Premium manual coffee grinder with ceramic burrs. Adjustable grind settings.",
    price: 85.0,
    category: "Equipment",
    stock_quantity: 20,
    is_active: true,
  },
  {
    name: "Croissant",
    description:
      "Buttery, flaky French croissant baked fresh daily. Perfect with your morning coffee.",
    price: 8.0,
    category: "Pastries",
    stock_quantity: 50,
    is_active: true,
  },
  {
    name: "Chocolate Muffin",
    description:
      "Rich chocolate muffin with chocolate chips. Moist and decadent.",
    price: 9.0,
    category: "Pastries",
    stock_quantity: 40,
    is_active: true,
  },
  {
    name: "Blueberry Scone",
    description:
      "Traditional British scone with fresh blueberries. Served with clotted cream and jam.",
    price: 12.0,
    category: "Pastries",
    stock_quantity: 30,
    is_active: true,
  },
  {
    name: "Avocado Toast",
    description:
      "Artisanal sourdough bread with smashed avocado, cherry tomatoes, and everything seasoning.",
    price: 18.0,
    category: "Food",
    stock_quantity: 25,
    is_active: true,
  },
];

// Function to add knowledge base entries
async function addKnowledgeBaseEntries() {
  console.log("🧠 Adding knowledge base entries...");

  for (let i = 0; i < knowledgeBaseEntries.length; i++) {
    const entry = knowledgeBaseEntries[i];
    console.log(`📝 Adding: ${entry.title}`);

    try {
      // Generate embedding for the content
      const embeddingResponse = await openai.embeddings.create({
        model: "text-embedding-3-small",
        input: entry.content,
      });

      const embedding = embeddingResponse.data[0].embedding;

      // Store in knowledge base
      const { data, error } = await supabase
        .from("knowledge_base")
        .insert({
          auth_id: TEST_AUTH_ID,
          content: entry.content,
          title: entry.title,
          embedding: embedding,
        })
        .select("id, title");

      if (error) {
        console.error(`❌ Error adding ${entry.title}:`, error.message);
      } else {
        console.log(`✅ Added: ${entry.title} (ID: ${data[0].id})`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${entry.title}:`, error.message);
    }

    // Small delay to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 100));
  }
}

// Function to add products
async function addProducts() {
  console.log("\n🛍️ Adding products...");

  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    console.log(`🏷️ Adding: ${product.name}`);

    try {
      const { data, error } = await supabase
        .from("product_catalog")
        .insert({
          auth_id: TEST_AUTH_ID,
          name: product.name,
          description: product.description,
          price: product.price,
          category: product.category,
          stock_quantity: product.stock_quantity,
          is_active: product.is_active,
        })
        .select("id, name, price");

      if (error) {
        console.error(`❌ Error adding ${product.name}:`, error.message);
      } else {
        console.log(
          `✅ Added: ${product.name} - RM${product.price} (ID: ${data[0].id})`,
        );
      }
    } catch (error) {
      console.error(`❌ Error processing ${product.name}:`, error.message);
    }
  }
}

// Function to create default chatbot settings
async function createDefaultSettings() {
  console.log("\n⚙️ Creating default chatbot settings...");

  try {
    const { data, error } = await supabase
      .from("chatbot_settings")
      .upsert({
        auth_id: TEST_AUTH_ID,
        system_prompt: `Friendly coffee shop assistant. Help with products, orders, store info, brewing tips, promotions.

LANGUAGE: Match customer (English/Malay/Chinese). Be enthusiastic about coffee.
ORDERS: Guide step by step when customers want to order.
STYLE: Natural, knowledgeable, friendly responses.`,
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 500,
        chat_history_limit: 10,
        similarity_threshold: 0.8,
        match_count: 3,
      })
      .select("id");

    if (error) {
      console.error("❌ Error creating chatbot settings:", error.message);
    } else {
      console.log("✅ Default chatbot settings created");
    }
  } catch (error) {
    console.error("❌ Error processing chatbot settings:", error.message);
  }
}

// Function to display summary
async function displaySummary() {
  console.log("\n📊 Summary of added data:");

  try {
    // Count knowledge base entries
    const { count: kbCount, error: kbError } = await supabase
      .from("knowledge_base")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", TEST_AUTH_ID);

    if (!kbError) {
      console.log(`📚 Knowledge Base Entries: ${kbCount}`);
    }

    // Count products
    const { count: productCount, error: productError } = await supabase
      .from("product_catalog")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", TEST_AUTH_ID);

    if (!productError) {
      console.log(`🛍️ Products: ${productCount}`);
    }

    // Group products by category
    const { data: categories, error: catError } = await supabase
      .from("product_catalog")
      .select("category")
      .eq("auth_id", TEST_AUTH_ID);

    if (!catError && categories) {
      const categoryCount = categories.reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1;
        return acc;
      }, {});

      console.log("\n📂 Products by category:");
      Object.entries(categoryCount).forEach(([category, count]) => {
        console.log(`  • ${category}: ${count} items`);
      });
    }

    console.log(`\n🎯 Test Auth ID used: ${TEST_AUTH_ID}`);
    console.log(`\n💡 You can now test the chatbot with this data!`);
    console.log(
      `🔧 Remember to update TEST_AUTH_ID in this script to match your actual auth ID.`,
    );
  } catch (error) {
    console.error("❌ Error getting summary:", error.message);
  }
}

// Function to clean existing data (optional)
async function cleanExistingData() {
  console.log("🧹 Cleaning existing test data...");

  try {
    // Clean knowledge base
    const { error: kbError } = await supabase
      .from("knowledge_base")
      .delete()
      .eq("auth_id", TEST_AUTH_ID);

    if (kbError) {
      console.log("⚠️ Could not clean knowledge base:", kbError.message);
    } else {
      console.log("✅ Cleaned existing knowledge base entries");
    }

    // Clean products
    const { error: productError } = await supabase
      .from("product_catalog")
      .delete()
      .eq("auth_id", TEST_AUTH_ID);

    if (productError) {
      console.log("⚠️ Could not clean products:", productError.message);
    } else {
      console.log("✅ Cleaned existing products");
    }
  } catch (error) {
    console.log("⚠️ Error cleaning data:", error.message);
  }
}

// Main execution function
async function main() {
  console.log("🚀 Starting dummy data population script...\n");
  console.log(`🎯 Using Auth ID: ${TEST_AUTH_ID}`);
  console.log("⚠️ Make sure to update TEST_AUTH_ID to your actual auth ID!\n");

  // Validate environment variables
  if (
    !process.env.SUPABASE_URL ||
    !process.env.SUPABASE_ANON_KEY ||
    !process.env.OPENAI_API_KEY
  ) {
    console.error("❌ Missing required environment variables:");
    console.error("   - SUPABASE_URL");
    console.error("   - SUPABASE_ANON_KEY");
    console.error("   - OPENAI_API_KEY");
    console.error("\n💡 Make sure your .env file is properly configured.");
    process.exit(1);
  }

  try {
    // Test database connection
    const { data, error } = await supabase
      .from("knowledge_base")
      .select("count", { count: "exact", head: true })
      .limit(1);

    if (error) {
      console.error("❌ Database connection failed:", error.message);
      process.exit(1);
    }

    console.log("✅ Database connection successful\n");

    // Ask if user wants to clean existing data
    const args = process.argv.slice(2);
    if (args.includes("--clean")) {
      await cleanExistingData();
      console.log();
    }

    // Add data
    await createDefaultSettings();
    await addKnowledgeBaseEntries();
    await addProducts();
    await displaySummary();

    console.log("\n🎉 Dummy data population completed successfully!");
    console.log("\n📖 Next steps:");
    console.log("1. Update TEST_AUTH_ID to your actual auth ID");
    console.log('2. Test the chatbot with: "Hello, I want to order coffee"');
    console.log(
      "3. Check the knowledge base with questions about store hours or products",
    );
    console.log("4. Try placing an order through WhatsApp");
    console.log(
      "\n💡 Run with --clean flag to clean existing data first: node populate-dummy-data.js --clean",
    );
  } catch (error) {
    console.error("❌ Script failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Export functions for module use
export {
  addKnowledgeBaseEntries,
  addProducts,
  createDefaultSettings,
  cleanExistingData,
  main,
};

// Get current file path for ES module
const __filename = fileURLToPath(import.meta.url);

// Run the script if called directly
if (process.argv[1] === __filename) {
  main().catch(console.error);
}
