import { Link } from "react-router-dom";
import { useEffect } from "react";
import Icon from "../components/ui/icon";

export default function EmailVerified() {
  useEffect(() => {
    // Check if there's an access token in the URL hash (from Supabase)
    const hash = window.location.hash;
    if (hash && hash.includes("access_token=")) {
      // Extract the access token
      const params = new URLSearchParams(hash.substring(1));
      const accessToken = params.get("access_token");

      if (accessToken) {
        // Store the token and update auth state
        localStorage.setItem("access_token", accessToken);

        // You might want to fetch user data and update the auth context here
        // For now, we'll just mark them as authenticated in a simple way

        // Clear the hash from URL for cleaner appearance
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname,
        );
      }
    }
  }, []);
  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8 fade-in">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Icon name="CheckCircle" className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Email verified successfully!
          </h1>
          <p className="text-gray-600">
            Your account is now active and ready to use
          </p>
        </div>

        {/* Success Card */}
        <div className="card space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <Icon
                name="CheckCircle"
                className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5"
              />
              <div>
                <h3 className="text-sm font-semibold text-green-900 mb-1">
                  Welcome to Chilbee!
                </h3>
                <p className="text-sm text-green-700">
                  Your email has been verified successfully. You can now access
                  all features and start setting up your AI chatbot.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-gray-700">
              What's next:
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                <span>Set up your business profile</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                <span>Configure your AI chatbot</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                <span>Connect your WhatsApp number</span>
              </li>
            </ul>
          </div>

          {/* Continue Button */}
          <div className="text-center pt-6 border-t border-gray-200">
            <Link to="/dashboard" className="btn-primary w-full">
              Continue to Dashboard
            </Link>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Need help getting started? Check our{" "}
            <a
              href="#"
              className="text-blue-600 hover:text-blue-700 font-semibold"
            >
              setup guide
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
