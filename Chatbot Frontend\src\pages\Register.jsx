import AuthForm from "../components/AuthForm";
import { Link, useNavigate } from "react-router-dom";
import Icon from "../components/ui/icon";

export default function Register() {
  const navigate = useNavigate();

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}

      <div className="container mx-auto px-2 sm:px-4 pt-2 lg:pt-10">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 lg:gap-40 items-center">
            {/* Left Side - Content */}
            <div className="space-y-6 hidden lg:block">
              {/* Hero Section */}
              <div className="text-center lg:text-left">
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 leading-tight">
                  Join <span className="text-blue-600">10,000+</span> Malaysian
                  businesses
                  <span className="block">using Chilbee</span>
                </h1>

                <p className="text-base sm:text-lg text-gray-600 mb-6 leading-relaxed">
                  Perfect for hawkers, kopitiams, restaurants, and small
                  businesses. Start automating your customer service today.
                </p>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-2 sm:gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      5 min
                    </div>
                    <div className="text-sm text-gray-600">Setup time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      24/7
                    </div>
                    <div className="text-sm text-gray-600">Support</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      RM0
                    </div>
                    <div className="text-sm text-gray-600">Setup cost</div>
                  </div>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-3">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">
                  Why businesses love us:
                </h3>
                <div className="space-y-2">
                  {[
                    "Understands Bahasa Malaysia, English, and Chinese",
                    "Replies to customers' messages 24/7",
                    "Works with your existing WhatsApp number",
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                        <Icon name="Check" className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Side - Form */}
            <div className="relative overflow-hidden">
              <div className="relative bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 border border-gray-100 my-4">
                <AuthForm
                  type="register"
                  onSuccess={() => {
                    navigate("/verify-email");
                  }}
                />

                {/* Sign In Link */}
                <div className="mt-6 text-center">
                  <p className="text-sm sm:text-base text-gray-600">
                    Already have an account?{" "}
                    <Link
                      to="/login"
                      className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200"
                    >
                      Sign in here
                    </Link>
                  </p>
                </div>

                {/* Trust Indicators */}
                <div className="mt-8 pt-6 border-t border-gray-100">
                  <div className="flex flex-col sm:flex-row items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-4">
                    <Icon
                      name="ShieldCheck"
                      className="w-4 h-4 text-green-500"
                    />
                    <span>Trusted by 500+ Malaysian businesses</span>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-3 sm:p-4 text-center">
                      <div className="text-lg sm:text-xl font-bold text-blue-700 mb-1">
                        99.9%
                      </div>
                      <div className="text-xs text-blue-600">Uptime</div>
                    </div>
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-3 sm:p-4 text-center">
                      <div className="text-lg sm:text-xl font-bold text-green-700 mb-1">
                        4.9 ★
                      </div>
                      <div className="text-xs text-green-600">Rating</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
