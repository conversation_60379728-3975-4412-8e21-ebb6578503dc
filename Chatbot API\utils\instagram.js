import axios from "axios";
import logger from "./logger.js";

/**
 * Send a text message via Instagram Messaging API
 */
export async function sendInstagramTextMessage(recipientId, message, accessToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/me/messages`;
    const data = {
      recipient: {
        id: recipientId,
      },
      message: {
        text: message,
      },
      messaging_type: "RESPONSE",
    };

    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error sending Instagram text message:", error);
    throw error;
  }
}

/**
 * Download media from Instagram Messaging API
 */
export async function downloadInstagramMedia(mediaId, accessToken) {
  try {
    // First get the media URL
    const mediaInfoUrl = `https://graph.facebook.com/v18.0/${mediaId}`;
    const mediaInfoResponse = await axios.get(mediaInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    const mediaUrl = mediaInfoResponse.data.url;

    // Download the actual media
    const mediaResponse = await axios.get(mediaUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      responseType: "arraybuffer",
      timeout: 60000,
    });

    return {
      data: mediaResponse.data,
      contentType: mediaResponse.headers["content-type"],
      size: mediaResponse.data.length,
    };
  } catch (error) {
    logger.error("Error downloading Instagram media:", error);
    throw error;
  }
}

/**
 * Validate Instagram webhook signature
 */
export function validateInstagramSignature(payload, signature, appSecret) {
  try {
    const crypto = require("crypto");
    const expectedSignature = crypto
      .createHmac("sha256", appSecret)
      .update(payload, "utf8")
      .digest("hex");

    return signature === `sha256=${expectedSignature}`;
  } catch (error) {
    logger.error("Error validating Instagram signature:", error);
    return false;
  }
}

/**
 * Validate Instagram access token and get account info
 */
export async function validateInstagramCredentials(accessToken, instagramAccountId) {
  try {
    const url = `https://graph.facebook.com/v18.0/${instagramAccountId}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: "id,username,name,profile_picture_url",
      },
      timeout: 30000,
    });

    return {
      isValid: true,
      accountInfo: response.data,
    };
  } catch (error) {
    logger.error("Error validating Instagram credentials:", error);
    return {
      isValid: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Set up Instagram webhook subscription
 */
export async function setupInstagramWebhook(accessToken, instagramAccountId, webhookUrl, verifyToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/${instagramAccountId}/subscribed_apps`;
    const response = await axios.post(
      url,
      {
        subscribed_fields: ["messages"],
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        timeout: 30000,
      }
    );

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    logger.error("Error setting up Instagram webhook:", error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Process Instagram message content based on type
 */
export function processInstagramMessage(message) {
  let messageContent = "";
  let messageType = "text";

  if (message.text) {
    messageContent = message.text;
    messageType = "text";
  } else if (message.attachments && message.attachments.length > 0) {
    const attachment = message.attachments[0];
    
    switch (attachment.type) {
      case "image":
        messageContent = attachment.payload?.url || "";
        messageType = "image";
        break;
      case "audio":
        messageContent = attachment.payload?.url || "";
        messageType = "audio";
        break;
      case "video":
        messageContent = attachment.payload?.url || "";
        messageType = "video";
        break;
      case "file":
        messageContent = attachment.payload?.url || "";
        messageType = "document";
        break;
      default:
        messageContent = `Unsupported attachment type: ${attachment.type}`;
        messageType = "text";
    }
  } else if (message.story_mention) {
    messageContent = "Story mention received";
    messageType = "story_mention";
  } else {
    messageContent = "Unsupported message type";
    messageType = "text";
  }

  return {
    content: messageContent,
    type: messageType,
  };
}

/**
 * Get Instagram user profile information
 */
export async function getInstagramUserProfile(userId, accessToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/${userId}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: "name,username,profile_picture_url",
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error getting Instagram user profile:", error);
    return null;
  }
}

/**
 * Check if Instagram account has messaging permissions
 */
export async function checkInstagramMessagingPermissions(accessToken, instagramAccountId) {
  try {
    const url = `https://graph.facebook.com/v18.0/${instagramAccountId}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: "messaging_feature_status",
      },
      timeout: 30000,
    });

    return {
      hasPermissions: response.data.messaging_feature_status?.messaging === "ENABLED",
      status: response.data.messaging_feature_status,
    };
  } catch (error) {
    logger.error("Error checking Instagram messaging permissions:", error);
    return {
      hasPermissions: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}
