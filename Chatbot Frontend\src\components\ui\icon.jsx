import * as LucideIcons from "lucide-react";

/**
 * Shared Icon component for consistent icon usage
 * @param {string} name - The Lucide icon name (e.g., 'Bo<PERSON>', 'Home')
 * @param {string|number} size - Icon size (default: 20)
 * @param {string} color - Icon color (optional)
 * @param {string} className - Additional class names (optional)
 * @param {object} rest - Other props
 */
export default function Icon({
  name,
  size = 20,
  color,
  className = "",
  ...rest
}) {
  const LucideIcon = LucideIcons[name];
  if (!LucideIcon) return null;
  return (
    <LucideIcon size={size} color={color} className={className} {...rest} />
  );
}
