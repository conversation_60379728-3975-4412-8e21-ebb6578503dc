import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import {
  CheckCircle,
  X,
  MessageSquare,
  Settings,

  Phone,
  AlertCircle,
  ExternalLink,
  Copy,
  Eye,
  EyeOff,
  TestTube,
  Webhook,
  Zap,
  Link2,
  XCircle,
  Info,
  ArrowRight,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { aiService } from "../../services/ai";
import Icon from "../../components/ui/icon";

export default function WhatsAppIntegration() {
  const { user } = useAuth();
  const { showSuccess, showError } = useAlertMigration();
  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);
  const [saving, setSaving] = useState(false);
  const [customerConfig, setCustomerConfig] = useState(null);
  const [showDeactivateDialog, setShowDeactivateDialog] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [showSystemToken, setShowSystemToken] = useState(false);
  const [webhookInfo, setWebhookInfo] = useState(null);
  const [showWebhookModal, setShowWebhookModal] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    whatsappBusinessAccountId: "",
    whatsappPhoneNumberId: "",
    systemAccessToken: "",
  });

  useEffect(() => {
    if (user?.user?.id) {
      fetchCustomerConfig();
    }
  }, [user]);

  const fetchCustomerConfig = async () => {
    try {
      setLoading(true);
      const data = await aiService.getCustomerConfig(user.user.id);

      if (data.success) {
        setCustomerConfig(data.data);
        setFormData({
          whatsappBusinessAccountId:
            data.data.whatsapp_business_account_id || "",
          whatsappPhoneNumberId: data.data.whatsapp_phone_number_id || "",
          systemAccessToken: "", // Never populate for security
        });
      } else if (data.notFound) {
        // Customer config not found - this is expected for new users
        setCustomerConfig(null);
      } else {
        showError(data.error || "Failed to load configuration");
      }
    } catch (error) {
      console.error("Error fetching config:", error);
      showError(error.message || "Failed to load configuration");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      let data;

      if (customerConfig) {
        // Update existing configuration
        const updateData = {
          authId: user.user.id,
          ...(formData.whatsappBusinessAccountId && {
            whatsappBusinessAccountId: formData.whatsappBusinessAccountId,
          }),
          ...(formData.whatsappPhoneNumberId && {
            whatsappPhoneNumberId: formData.whatsappPhoneNumberId,
          }),
          ...(formData.systemAccessToken && {
            systemAccessToken: formData.systemAccessToken,
          }),
        };

        data = await aiService.updateCustomerConfig(updateData);

        if (data.success) {
          showSuccess("Configuration updated successfully!");
          // Clear sensitive fields
          setFormData((prev) => ({ ...prev, systemAccessToken: "" }));
          fetchCustomerConfig();
        } else {
          showError(data.error || "Failed to update configuration");
        }
      } else {
        // Register new customer
        const registerData = {
          authId: user.user.id,
          whatsappBusinessAccountId: formData.whatsappBusinessAccountId,
          whatsappPhoneNumberId: formData.whatsappPhoneNumberId,
          systemAccessToken: formData.systemAccessToken,
        };

        data = await aiService.registerCustomer(registerData);

        if (data.success) {
          showSuccess("WhatsApp Business account registered successfully!");
          // Clear sensitive fields
          setFormData((prev) => ({ ...prev, systemAccessToken: "" }));
          fetchCustomerConfig();
        } else {
          showError(
            data.error || "Failed to register WhatsApp Business account",
          );
        }
      }
    } catch (error) {
      console.error("Error saving configuration:", error);
      showError(error.message || "Failed to save configuration");
    } finally {
      setSaving(false);
    }
  };



  const testWhatsAppConnection = async (testPhoneNumber = null) => {
    try {
      setSaving(true);

      const testData = {
        authId: user.user.id,
        ...(testPhoneNumber && { testPhoneNumber }),
      };

      const data = await aiService.testWhatsApp(testData);

      if (data.success) {
        showSuccess(
          testPhoneNumber
            ? `Test message sent successfully to ${testPhoneNumber}!`
            : "WhatsApp connection is working correctly!",
        );
      } else {
        showError(data.error || "WhatsApp connection test failed");
      }
    } catch (error) {
      console.error("Error testing WhatsApp:", error);
      showError(error.message || "Failed to test WhatsApp connection");
    } finally {
      setSaving(false);
    }
  };

  const getWebhookInfo = async () => {
    try {
      const data = await aiService.getWebhookInfo(user.user.id);

      if (data.success) {
        setWebhookInfo(data.data);
        setShowWebhookModal(true);
      } else {
        showError(data.error || "Failed to get webhook information");
      }
    } catch (error) {
      console.error("Error getting webhook info:", error);
      showError(error.message || "Failed to get webhook information");
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    showSuccess("Copied to clipboard!");
  };

  const deactivateWhatsApp = async () => {
    try {
      setSaving(true);

      const data = await aiService.deactivateIntegration(user.user.id);

      if (data.success) {
        showSuccess("WhatsApp integration deactivated successfully!");
        setCustomerConfig(null);
        setFormData({
          whatsappBusinessAccountId: "",
          whatsappPhoneNumberId: "",
          systemAccessToken: "",
        });
      } else {
        showError(data.error || "Failed to deactivate WhatsApp integration");
      }
    } catch (error) {
      console.error("Error deactivating WhatsApp:", error);
      showError(error.message || "Failed to deactivate WhatsApp integration");
    } finally {
      setSaving(false);
      setShowDeactivateDialog(false);
    }
  };

  if (showLoadingState) {
    return <PageSkeleton cardCount={2} />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          WhatsApp Business Integration
        </h2>
        <p className="text-gray-600">
          Connect your WhatsApp Business account to enable automated customer service and order processing.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card flex flex-col justify-between">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
              <Icon name="MessageSquare" className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Connection Status</p>
              <p className="text-base font-semibold text-green-700">
                {customerConfig ? "Connected" : "Not Connected"}
              </p>
            </div>
          </div>
        </div>
        <div className="card flex flex-col justify-between">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <Icon name="Link2" className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Business Account ID</p>
              <p className="text-base font-mono text-gray-700 truncate">
                {customerConfig?.whatsapp_business_account_id || "-"}
              </p>
            </div>
          </div>
        </div>
        <div className="card flex flex-col justify-between">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
              <Icon name="Phone" className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Phone Number ID</p>
              <p className="text-base font-mono text-gray-700 truncate">
                {customerConfig?.whatsapp_phone_number_id || "-"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Setup Instructions */}
      {!customerConfig && (
        <div className="card bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                <Icon name="Info" className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900">
                  Setup Instructions
                </h3>
                <p className="text-sm text-blue-700">
                  Follow these steps to get your WhatsApp Business credentials
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowInstructions(!showInstructions)}
              className="text-blue-600 hover:text-blue-800"
            >
              {showInstructions ? (
                <ChevronDown className="h-5 w-5" />
              ) : (
                <ChevronRight className="h-5 w-5" />
              )}
            </button>
          </div>

          {showInstructions && (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                    <span className="bg-blue-600 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center">
                      1
                    </span>
                    Get Business Account ID & Phone Number ID
                  </h4>
                  <div className="space-y-2 text-sm text-blue-800">
                    <p>
                      • Go to{" "}
                      <a
                        href="https://developers.facebook.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline hover:text-blue-900"
                      >
                        Facebook Developers
                      </a>
                    </p>
                    <p>• Navigate to your WhatsApp Business App</p>
                    <p>• Go to WhatsApp → Configuration</p>
                    <p>• Copy your Business Account ID</p>
                    <p>• Add a phone number and copy the Phone Number ID</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                    <span className="bg-blue-600 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center">
                      2
                    </span>
                    Create System User Access Token
                  </h4>
                  <div className="space-y-2 text-sm text-blue-800">
                    <p>• Go to Business Settings → System Users</p>
                    <p>• Create a new System User</p>
                    <p>• Assign WhatsApp Business Management permissions</p>
                    <p>• Generate an access token with these permissions:</p>
                    <div className="ml-4 space-y-1">
                      <p>- whatsapp_business_management</p>
                      <p>- whatsapp_business_messaging</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t border-blue-200 pt-4">
                <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                  <span className="bg-blue-600 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center">
                    3
                  </span>
                  Configure Webhook (After Registration)
                </h4>
                <p className="text-sm text-blue-800">
                  Once you register your WhatsApp Business account below, click
                  "Webhook Info" to get your webhook URL and verification token
                  for setting up message delivery.
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* WhatsApp Configuration Form Section */}
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-y-6">
        {/* Actions Card (narrow, buttons vertical) */}
        {customerConfig && (
          <div className="card flex flex-col lg:col-span-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                <Icon name="CircleCheck" className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Actions</h3>
                <p className="text-sm text-gray-600">Manage your WhatsApp integration</p>
              </div>
            </div>
            <div className="flex flex-row gap-3 w-full">
              <button
                onClick={() => testWhatsAppConnection()}
                disabled={saving}
                className="btn-secondary gap-2 w-full"
              >
                <Icon name="Zap" className="w-5 h-5 text-blue-700" />
                Test Connection
              </button>
              <button
                onClick={getWebhookInfo}
                disabled={saving}
                className="btn-secondary gap-2 w-full"
              >
                <Icon name="Link2" className="w-5 h-5 text-blue-700" />
                Webhook Info
              </button>
              <button
                onClick={() => setShowDeactivateDialog(true)}
                disabled={saving}
                className="btn-secondary gap-2 w-full"
              >
                <Icon name="XCircle" className="w-5 h-5 text-blue-700" />
                Deactivate
              </button>
            </div>
          </div>
        )}
        <div className="card lg:col-span-4">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <Icon name="Settings" className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {customerConfig ? "Update WhatsApp Settings" : "WhatsApp Business Setup"}
              </h3>
              <p className="text-sm text-gray-600">
                {customerConfig ? "Modify your WhatsApp integration settings" : "Connect your WhatsApp Business account"}
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4 w-full">
            {/* Business Account ID */}
            <div>
              <label
                htmlFor="businessAccountId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                WhatsApp Business Account ID {!customerConfig && "*"}
              </label>
              <input
                id="businessAccountId"
                type="text"
                required={!customerConfig}
                value={formData.whatsappBusinessAccountId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    whatsappBusinessAccountId: e.target.value,
                  }))
                }
                placeholder="Enter your Business Account ID"
                className="input-field disabled:opacity-60"
                disabled={saving}
              />
              <p className="text-xs text-gray-500 mt-1">
                Found in Facebook Developers → WhatsApp → Configuration
              </p>
            </div>

            {/* Phone Number ID */}
            <div>
              <label
                htmlFor="phoneNumberId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                WhatsApp Phone Number ID {!customerConfig && "*"}
              </label>
              <input
                id="phoneNumberId"
                type="text"
                required={!customerConfig}
                value={formData.whatsappPhoneNumberId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    whatsappPhoneNumberId: e.target.value,
                  }))
                }
                placeholder="Enter your Phone Number ID"
                className="input-field disabled:opacity-60"
                disabled={saving}
              />
              <p className="text-xs text-gray-500 mt-1">
                Found when you add a phone number in Facebook Developers
              </p>
            </div>

            {/* System Access Token */}
            <div>
              <label
                htmlFor="systemAccessToken"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                System User Access Token {!customerConfig && "*"}
              </label>
              <div className="relative">
                <input
                  id="systemAccessToken"
                  type={showSystemToken ? "text" : "password"}
                  required={!customerConfig}
                  value={formData.systemAccessToken}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      systemAccessToken: e.target.value,
                    }))
                  }
                  placeholder={
                    customerConfig
                      ? "Enter new token to update"
                      : "Enter your system access token"
                  }
                  className="input-field pr-10 disabled:opacity-60"
                  disabled={saving}
                />
                <button
                  type="button"
                  onClick={() => setShowSystemToken(!showSystemToken)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showSystemToken ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Generate from Business Settings → System Users with WhatsApp
                permissions
              </p>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              disabled={saving}
            >
              {saving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  {customerConfig ? "Updating..." : "Registering..."}
                </>
              ) : (
                <>
                  <Icon name="Settings" className="h-4 w-4" />
                  {customerConfig
                    ? "Update Configuration"
                    : "Register WhatsApp Business"}
                </>
              )}
            </button>
          </form>
        </div>
      </div>

      {/* Webhook Information Modal */}
      {showWebhookModal && webhookInfo && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => setShowWebhookModal(false)}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-2xl w-full shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Webhook Configuration
                </h3>
                <button
                  onClick={() => setShowWebhookModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Icon name="X" className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Webhook URL
                  </label>
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={webhookInfo.webhookUrl}
                      readOnly
                      className="flex-1 input-field bg-gray-50"
                    />
                    <button
                      onClick={() => copyToClipboard(webhookInfo.webhookUrl)}
                      className="flex items-center gap-1 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100"
                    >
                      <Icon name="Copy" className="h-4 w-4" />
                      Copy
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Verify Token
                  </label>
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={webhookInfo.verifyToken}
                      readOnly
                      className="flex-1 input-field bg-gray-50"
                    />
                    <button
                      onClick={() => copyToClipboard(webhookInfo.verifyToken)}
                      className="flex items-center gap-1 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100"
                    >
                      <Icon name="Copy" className="h-4 w-4" />
                      Copy
                    </button>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                  <h4 className="font-medium text-yellow-900 mb-2">
                    Setup Instructions:
                  </h4>
                  <ol className="text-sm text-yellow-800 space-y-1 list-decimal list-inside">
                    <li>
                      Go to Facebook Developers → Your App → WhatsApp →
                      Configuration
                    </li>
                    <li>
                      In the Webhooks section, click "Edit" next to your phone
                      number
                    </li>
                    <li>Enter the Webhook URL and Verify Token above</li>
                    <li>Subscribe to "messages" events</li>
                    <li>Click "Verify and Save"</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Deactivate Confirmation Modal */}
      {showDeactivateDialog && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!saving) {
                setShowDeactivateDialog(false);
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-lg w-full shadow-xl transform transition-all">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Deactivate WhatsApp Integration
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to deactivate WhatsApp integration? This
                will stop all automated responses and disconnect your WhatsApp
                Business account.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={deactivateWhatsApp}
                  className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Deactivating...
                    </>
                  ) : (
                    <>
                      <Icon name="X" className="h-4 w-4" />
                      Deactivate Integration
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowDeactivateDialog(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={saving}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
