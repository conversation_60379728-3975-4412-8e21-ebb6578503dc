import supabase from "./supabase.js";
import logger from "./logger.js";
import { INTENT_CATEGORIES, URGENCY_LEVELS, AUTO_TAGS } from "./aiTagging.js";

/**
 * Advanced Follow-up Template System
 * Provides context-aware, personalized templates with A/B testing capabilities
 */

// Template categories for A/B testing
export const TEMPLATE_VARIANTS = {
  CASUAL: 'casual',
  PROFESSIONAL: 'professional',
  URGENT: 'urgent',
  FRIENDLY: 'friendly',
  DIRECT: 'direct'
};

// Template performance tracking
const templatePerformance = new Map();

/**
 * Get smart follow-up template with A/B testing and personalization
 * @param {Object} aiAnalysis - Enhanced AI analysis results
 * @param {Object} contact - Contact information
 * @param {string} authId - Business owner's auth ID
 * @returns {Object} Template with variant info for tracking
 */
export async function getSmartTemplate(aiAnalysis, contact, authId) {
  try {
    // Get user's template preferences and A/B test settings
    const { data: templateSettings } = await supabase
      .from("template_settings")
      .select("*")
      .eq("auth_id", authId)
      .single();

    // Determine the best template variant based on performance and context
    const templateVariant = await selectOptimalVariant(aiAnalysis, contact, authId);
    
    // Generate personalized template
    const template = generatePersonalizedTemplate(aiAnalysis, contact, templateVariant);
    
    // Add dynamic content based on conversation history
    const enhancedTemplate = await addDynamicContent(template, aiAnalysis, contact, authId);
    
    return {
      message: enhancedTemplate,
      variant: templateVariant,
      templateId: generateTemplateId(aiAnalysis.intent_category, templateVariant),
      personalizationLevel: calculatePersonalizationLevel(aiAnalysis, contact)
    };

  } catch (error) {
    logger.error("Error generating smart template:", error);
    // Fallback to basic template
    return {
      message: getBasicTemplate(aiAnalysis, contact),
      variant: TEMPLATE_VARIANTS.FRIENDLY,
      templateId: 'fallback',
      personalizationLevel: 'basic'
    };
  }
}

/**
 * Select optimal template variant based on performance and context
 * @param {Object} aiAnalysis - AI analysis results
 * @param {Object} contact - Contact information
 * @param {string} authId - Business owner's auth ID
 * @returns {string} Selected template variant
 */
async function selectOptimalVariant(aiAnalysis, contact, authId) {
  // Get historical performance data for this contact type
  const { data: performanceData } = await supabase
    .from("template_performance")
    .select("variant, response_rate, conversion_rate")
    .eq("auth_id", authId)
    .eq("intent_category", aiAnalysis.intent_category)
    .order("response_rate", { ascending: false });

  // Context-based variant selection
  if (aiAnalysis.urgency_level === URGENCY_LEVELS.IMMEDIATE) {
    return TEMPLATE_VARIANTS.URGENT;
  }
  
  if (aiAnalysis.engagement_score >= 0.8) {
    return TEMPLATE_VARIANTS.DIRECT;
  }
  
  if (aiAnalysis.sentiment_score >= 0.5) {
    return TEMPLATE_VARIANTS.FRIENDLY;
  }
  
  if (contact.total_messages >= 10) {
    return TEMPLATE_VARIANTS.CASUAL;
  }

  // Use best performing variant if available
  if (performanceData && performanceData.length > 0) {
    return performanceData[0].variant;
  }

  // Default to professional for new contacts
  return TEMPLATE_VARIANTS.PROFESSIONAL;
}

/**
 * Generate personalized template based on variant and context
 * @param {Object} aiAnalysis - AI analysis results
 * @param {Object} contact - Contact information
 * @param {string} variant - Template variant
 * @returns {string} Personalized template
 */
function generatePersonalizedTemplate(aiAnalysis, contact, variant) {
  const customerName = contact.name || 'there';
  const timeOfDay = getTimeOfDayGreeting();
  
  // Base templates by intent and variant
  const templates = {
    [INTENT_CATEGORIES.READY_TO_PURCHASE]: {
      [TEMPLATE_VARIANTS.URGENT]: `🚨 Hi {name}! I see you're ready to purchase. Let's get this done quickly - I'm standing by to help you complete your order right now!`,
      [TEMPLATE_VARIANTS.DIRECT]: `Hi {name}! Ready to move forward with your purchase? I can help you complete everything in just a few minutes.`,
      [TEMPLATE_VARIANTS.FRIENDLY]: `{timeOfDay} {name}! 😊 Exciting that you're ready to purchase! I'm here to make the process super smooth for you.`,
      [TEMPLATE_VARIANTS.PROFESSIONAL]: `Good {timeOfDay}, {name}. I understand you're ready to proceed with your purchase. I'm available to assist you with the next steps.`,
      [TEMPLATE_VARIANTS.CASUAL]: `Hey {name}! 👋 Saw you're ready to buy - awesome! Let me know if you need any help wrapping this up.`
    },
    [INTENT_CATEGORIES.PRICE_INQUIRY]: {
      [TEMPLATE_VARIANTS.URGENT]: `💰 Hi {name}! Quick update on pricing - I have some time-sensitive offers that might interest you!`,
      [TEMPLATE_VARIANTS.DIRECT]: `Hi {name}! Here's the pricing info you requested, plus some options that could save you money.`,
      [TEMPLATE_VARIANTS.FRIENDLY]: `{timeOfDay} {name}! 😊 Hope you're doing well! I have some great pricing options to share with you.`,
      [TEMPLATE_VARIANTS.PROFESSIONAL]: `Good {timeOfDay}, {name}. Following up on your pricing inquiry with detailed information and available options.`,
      [TEMPLATE_VARIANTS.CASUAL]: `Hey {name}! Got some pricing info for you - think you'll like what I found! 💰`
    },
    [INTENT_CATEGORIES.INTERESTED]: {
      [TEMPLATE_VARIANTS.URGENT]: `⚡ Hi {name}! Don't want you to miss out - I have some exciting updates about what we discussed!`,
      [TEMPLATE_VARIANTS.DIRECT]: `Hi {name}! Following up on your interest - I have answers to help you decide.`,
      [TEMPLATE_VARIANTS.FRIENDLY]: `{timeOfDay} {name}! 😊 Hope you're having a great day! Just wanted to continue our conversation.`,
      [TEMPLATE_VARIANTS.PROFESSIONAL]: `Good {timeOfDay}, {name}. I wanted to follow up on your inquiry and provide additional information.`,
      [TEMPLATE_VARIANTS.CASUAL]: `Hey {name}! 👋 Just checking in - any other questions about what we talked about?`
    }
  };

  // Get template for intent and variant, with fallback
  const intentTemplates = templates[aiAnalysis.intent_category] || templates[INTENT_CATEGORIES.INTERESTED];
  let template = intentTemplates[variant] || intentTemplates[TEMPLATE_VARIANTS.FRIENDLY];

  // Replace placeholders
  template = template.replace(/{name}/g, customerName);
  template = template.replace(/{timeOfDay}/g, timeOfDay);

  return template;
}

/**
 * Add dynamic content based on conversation history and context
 * @param {string} baseTemplate - Base template message
 * @param {Object} aiAnalysis - AI analysis results
 * @param {Object} contact - Contact information
 * @param {string} authId - Business owner's auth ID
 * @returns {string} Enhanced template with dynamic content
 */
async function addDynamicContent(baseTemplate, aiAnalysis, contact, authId) {
  let enhancedTemplate = baseTemplate;

  // Add engagement-specific content
  if (aiAnalysis.engagement_score >= 0.8) {
    enhancedTemplate += '\n\nI can see you\'re really engaged with this - I\'d love to give you my full attention. When works best for you?';
  }

  // Add urgency-based content
  if (aiAnalysis.urgency_level === URGENCY_LEVELS.IMMEDIATE) {
    enhancedTemplate += '\n\n⏰ I\'m available right now if you\'d like to move forward immediately!';
  } else if (aiAnalysis.urgency_level === URGENCY_LEVELS.HIGH) {
    enhancedTemplate += '\n\n⏰ I have some time today if you\'d like to discuss this further!';
  }

  // Add tag-specific content
  if (aiAnalysis.auto_tags?.includes(AUTO_TAGS.PRICE_SENSITIVE)) {
    enhancedTemplate += '\n\n💡 I also have some budget-friendly options that might interest you!';
  }

  if (aiAnalysis.auto_tags?.includes(AUTO_TAGS.COMPARISON_SHOPPER)) {
    enhancedTemplate += '\n\n📊 Happy to show you how we compare to other options you\'re considering!';
  }

  // Add previous order context if available
  const { data: recentOrder } = await supabase
    .from("orders")
    .select("items, created_at")
    .eq("auth_id", authId)
    .eq("phone_number", contact.phone_number)
    .order("created_at", { ascending: false })
    .limit(1)
    .single();

  if (recentOrder) {
    const daysSinceOrder = Math.floor((new Date() - new Date(recentOrder.created_at)) / (1000 * 60 * 60 * 24));
    if (daysSinceOrder < 30) {
      enhancedTemplate += '\n\n🛍️ Hope you\'re enjoying your recent purchase! Let me know if you need anything else.';
    }
  }

  return enhancedTemplate;
}

/**
 * Get time-appropriate greeting
 * @returns {string} Time of day greeting
 */
function getTimeOfDayGreeting() {
  const hour = new Date().getHours();
  if (hour < 12) return 'morning';
  if (hour < 17) return 'afternoon';
  return 'evening';
}

/**
 * Calculate personalization level for analytics
 * @param {Object} aiAnalysis - AI analysis results
 * @param {Object} contact - Contact information
 * @returns {string} Personalization level
 */
function calculatePersonalizationLevel(aiAnalysis, contact) {
  let score = 0;
  
  if (contact.name) score += 1;
  if (aiAnalysis.auto_tags?.length > 0) score += 1;
  if (aiAnalysis.sentiment_score !== undefined) score += 1;
  if (aiAnalysis.urgency_level) score += 1;
  if (contact.total_messages > 5) score += 1;

  if (score >= 4) return 'high';
  if (score >= 2) return 'medium';
  return 'basic';
}

/**
 * Generate template ID for tracking
 * @param {string} intentCategory - Intent category
 * @param {string} variant - Template variant
 * @returns {string} Template ID
 */
function generateTemplateId(intentCategory, variant) {
  return `${intentCategory}_${variant}_${Date.now()}`;
}

/**
 * Basic fallback template
 * @param {Object} aiAnalysis - AI analysis results
 * @param {Object} contact - Contact information
 * @returns {string} Basic template
 */
function getBasicTemplate(aiAnalysis, contact) {
  const customerName = contact.name || 'there';
  return `Hi ${customerName}! 👋 Just following up on our conversation. I'm here to help with any questions you might have! 😊`;
}

/**
 * Track template performance for A/B testing
 * @param {string} templateId - Template ID
 * @param {string} authId - Business owner's auth ID
 * @param {string} outcome - 'sent', 'responded', 'converted'
 */
export async function trackTemplatePerformance(templateId, authId, outcome) {
  try {
    await supabase.rpc("track_template_performance", {
      p_template_id: templateId,
      p_auth_id: authId,
      p_outcome: outcome,
      p_timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error("Error tracking template performance:", error);
  }
}
