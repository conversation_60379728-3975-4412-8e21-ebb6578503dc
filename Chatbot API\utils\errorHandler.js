/**
 * Shared error handling utilities
 */

/**
 * Handle standard API errors with consistent response format
 * @param {Error} error - The error object
 * @param {Object} res - Express response object
 * @param {string} defaultMessage - Default error message
 * @param {number} defaultStatus - Default HTTP status code
 */
export function handleApiError(error, res, defaultMessage = "Internal server error", defaultStatus = 500) {
  console.error("API Error:", error);
  
  // Handle specific error types
  if (error.message === "Customer configuration not found") {
    return res.status(404).json({ error: error.message });
  }
  
  if (error.message === "User profile not found") {
    return res.status(404).json({ error: error.message });
  }
  
  // Handle Supabase errors
  if (error.code === "PGRST116") {
    return res.status(404).json({ error: "Resource not found" });
  }
  
  // Handle validation errors
  if (error.name === "ValidationError") {
    return res.status(400).json({ error: error.message });
  }
  
  // Default error response
  return res.status(defaultStatus).json({ 
    error: error.message || defaultMessage 
  });
}

/**
 * Validate required fields in request body
 * @param {Object} body - Request body
 * @param {Array} requiredFields - Array of required field names
 * @throws {Error} If any required field is missing
 */
export function validateRequiredFields(body, requiredFields) {
  const missingFields = requiredFields.filter(field => !body[field]);
  
  if (missingFields.length > 0) {
    const error = new Error(`Missing required fields: ${missingFields.join(", ")}`);
    error.name = "ValidationError";
    throw error;
  }
}

/**
 * Handle async route errors with try-catch wrapper
 * @param {Function} fn - Async route handler function
 * @returns {Function} Wrapped function with error handling
 */
export function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Standard success response format
 * @param {Object} res - Express response object
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @param {number} status - HTTP status code
 */
export function sendSuccess(res, data = null, message = "Success", status = 200) {
  const response = { success: true };
  
  if (message !== "Success") {
    response.message = message;
  }
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(status).json(response);
}

/**
 * Standard error response format
 * @param {Object} res - Express response object
 * @param {string} error - Error message
 * @param {number} status - HTTP status code
 */
export function sendError(res, error, status = 500) {
  return res.status(status).json({ error });
}
