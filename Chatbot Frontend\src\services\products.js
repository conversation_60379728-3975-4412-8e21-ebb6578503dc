import { api, tokenManager } from "./api.js";

// Products service
export const productsService = {
  // Get products list with pagination
  async getProductsList(params = {}) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const queryParams = new URLSearchParams();

      // Pagination parameters
      queryParams.append("page", params.page || "1");
      queryParams.append("limit", params.limit || "10");

      // Search parameter
      if (params.search) {
        queryParams.append("search", params.search);
      }

      // Filter parameters
      if (params.category) {
        queryParams.append("category", params.category);
      }
      if (params.isActive !== undefined) {
        queryParams.append("isActive", params.isActive);
      }

      // Sorting parameters
      if (params.sortBy) {
        queryParams.append("sortBy", params.sortBy);
      }
      if (params.sortOrder) {
        queryParams.append("sortOrder", params.sortOrder);
      }

      // User ID parameter (required)
      if (params.authId) {
        queryParams.append("authId", params.authId);
      }

      const response = await api.get(
        `/api/products/list?${queryParams.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get products list",
      );
    }
  },

  // Get orders with pagination (redirects to AI service for unified API)
  async getOrders(params = {}) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const queryParams = new URLSearchParams();

      // Pagination parameters
      queryParams.append("page", params.page || "1");
      queryParams.append("limit", params.limit || "10");

      // Search parameter
      if (params.search) {
        queryParams.append("search", params.search);
      }

      // Filter parameters
      if (params.customerPhone) {
        queryParams.append("customerPhone", params.customerPhone);
      }
      if (params.status) {
        queryParams.append("status", params.status);
      }

      // Sorting parameters
      if (params.sortBy) {
        queryParams.append("sortBy", params.sortBy);
      }
      if (params.sortOrder) {
        queryParams.append("sortOrder", params.sortOrder);
      }

      // User ID parameter (required)
      if (params.authId) {
        queryParams.append("authId", params.authId);
      }

      // Use AI service endpoint for unified order management
      const response = await api.get(
        `/api/ai/orders?${queryParams.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to get orders");
    }
  },

  // Add product
  async addProduct(data) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.post("/api/products/add-product", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to add product");
    }
  },

  // Update product
  async updateProduct(data) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.put("/api/products/update", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update product",
      );
    }
  },

  // Delete product
  async deleteProduct(data) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.delete("/api/products/delete", {
        data,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to delete product",
      );
    }
  },

  // Get product by ID
  async getProductById(productId, authId) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.get(
        `/api/products/${productId}?authId=${authId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to get product");
    }
  },

  // Get categories
  async getCategories(authId) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.get(
        `/api/products/categories?authId=${authId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get categories",
      );
    }
  },

  // Create order
  async createOrder(data) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.post("/api/products/orders", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to create order");
    }
  },

  // Update order
  async updateOrder(orderId, data) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.put(`/api/products/orders/${orderId}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to update order");
    }
  },

  // Update order status (redirects to AI service for unified API)
  async updateOrderStatus(orderId, data) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      // Use AI service endpoint for unified order status management
      const response = await api.put(`/api/ai/orders/${orderId}/status`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update order status",
      );
    }
  },

  // Delete order
  async deleteOrder(orderId, authId) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.delete(`/api/products/orders/${orderId}`, {
        data: { authId },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to delete order");
    }
  },

  // Get order statistics
  async getOrderStatistics(params = {}) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const queryParams = new URLSearchParams();

      if (params.authId) {
        queryParams.append("authId", params.authId);
      }
      if (params.startDate) {
        queryParams.append("startDate", params.startDate);
      }
      if (params.endDate) {
        queryParams.append("endDate", params.endDate);
      }
      if (params.period) {
        queryParams.append("period", params.period);
      }

      const response = await api.get(
        `/api/products/statistics?${queryParams.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get order statistics",
      );
    }
  },

  // Upload product image
  async uploadProductImage(file, authId) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const formData = new FormData();
      formData.append('image', file);
      formData.append('authId', authId);

      const response = await api.post("/api/products/upload-image", formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to upload product image",
      );
    }
  },

  // Delete product image
  async deleteProductImage(productId, authId, storagePath) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.delete("/api/products/delete-image", {
        data: {
          productId,
          authId,
          storagePath
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to delete product image",
      );
    }
  },
};

export default productsService;
