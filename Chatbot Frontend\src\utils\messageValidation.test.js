/**
 * Test file for message validation utilities
 */

import { checkMessageGapRestriction, formatTimeSinceLastMessage } from './messageValidation';

// Mock data for testing
const createMockMessage = (hoursAgo) => {
  const timestamp = new Date();
  timestamp.setHours(timestamp.getHours() - hoursAgo);
  return {
    id: Math.random().toString(),
    timestamp: timestamp.toISOString(),
    content: 'Test message',
    sender: 'user'
  };
};

// Test cases
console.log('Testing Message Gap Restriction Logic...\n');

// Test 1: No messages (should not be blocked)
console.log('Test 1: No messages');
const result1 = checkMessageGapRestriction([]);
console.log('Result:', result1);
console.log('Expected: isBlocked = false\n');

// Test 2: Recent message (within 24 hours - should not be blocked)
console.log('Test 2: Recent message (12 hours ago)');
const recentMessages = [createMockMessage(12)];
const result2 = checkMessageGapRestriction(recentMessages);
console.log('Result:', result2);
console.log('Expected: isBlocked = false\n');

// Test 3: Old message (more than 24 hours - should be blocked)
console.log('Test 3: Old message (30 hours ago)');
const oldMessages = [createMockMessage(30)];
const result3 = checkMessageGapRestriction(oldMessages);
console.log('Result:', result3);
console.log('Expected: isBlocked = true\n');

// Test 4: Multiple messages with most recent being old
console.log('Test 4: Multiple messages, most recent is old (48 hours ago)');
const mixedMessages = [
  createMockMessage(72), // 3 days ago
  createMockMessage(48), // 2 days ago (most recent)
  createMockMessage(96)  // 4 days ago
];
const result4 = checkMessageGapRestriction(mixedMessages);
console.log('Result:', result4);
console.log('Expected: isBlocked = true\n');

// Test 5: Multiple messages with most recent being new
console.log('Test 5: Multiple messages, most recent is new (2 hours ago)');
const mixedMessages2 = [
  createMockMessage(72), // 3 days ago
  createMockMessage(2),  // 2 hours ago (most recent)
  createMockMessage(48)  // 2 days ago
];
const result5 = checkMessageGapRestriction(mixedMessages2);
console.log('Result:', result5);
console.log('Expected: isBlocked = false\n');

// Test formatTimeSinceLastMessage function
console.log('Testing formatTimeSinceLastMessage...\n');

const now = new Date();
const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);

console.log('1 hour ago:', formatTimeSinceLastMessage(oneHourAgo));
console.log('1 day ago:', formatTimeSinceLastMessage(oneDayAgo));
console.log('3 days ago:', formatTimeSinceLastMessage(threeDaysAgo));
console.log('null input:', formatTimeSinceLastMessage(null));

console.log('\nAll tests completed!');
