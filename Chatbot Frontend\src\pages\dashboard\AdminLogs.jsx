import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { usePagination } from "../../hooks/usePagination";
import { Pagination } from "../../components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select2";
import adminService from "../../services/admin";
import Icon from "../../components/ui/icon";

export default function AdminLogs() {
  // State management
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const { showError } = useAlertMigration();

  // Delayed loading for refresh button
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Pagination hook
  const pagination = usePagination({
    initialPage: 1,
    initialLimit: 20,
  });

  // Filters state
  const [filters, setFilters] = useState({
    actionType: "all",
    targetAuthId: "",
    adminId: "",
  });

  // Fetch admin logs
  const fetchAdminLogs = async (page = 1, newFilters = filters) => {
    try {
      setLoading(true);
      setIsRefreshing(true);
      const params = {
        page,
        limit: pagination.limit,
        ...newFilters,
      };

      // Remove empty filters and "all" values
      Object.keys(params).forEach((key) => {
        if (
          params[key] === "" ||
          params[key] === null ||
          params[key] === undefined ||
          params[key] === "all"
        ) {
          delete params[key];
        }
      });

      const response = await adminService.getAdminLogs(params);
      setLogs(response.data || []);
      pagination.updatePagination(response);
      // Clear any previous errors - not needed with global alerts
    } catch (err) {
      showError(`Failed to load admin logs: ${err.message}`);
      setLogs([]);
    } finally {
      setLoading(false);
      // Keep refreshing state for a bit longer
      setTimeout(() => setIsRefreshing(false), 1000);
    }
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    pagination.goToPage(1); // Reset to first page when filters change
    fetchAdminLogs(1, newFilters);
  };

  // Clear filters
  const clearFilters = () => {
    const newFilters = { actionType: "all", targetAuthId: "", adminId: "" };
    setFilters(newFilters);
    pagination.goToPage(1); // Reset to first page when filters change
    fetchAdminLogs(1, newFilters);
  };

  // Format action type for display
  const formatActionType = (actionType) => {
    if (!actionType) return "Unknown";
    return actionType
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Get action icon
  const getActionIcon = (actionType) => {
    if (!actionType) return <Icon name="FileText" className="w-5 h-5" />;

    if (actionType.includes("create") || actionType.includes("set")) {
      return <Icon name="Check" className="w-5 h-5 text-green-600" />;
    } else if (actionType.includes("delete") || actionType.includes("cancel")) {
      return <Icon name="AlertTriangle" className="w-5 h-5 text-red-600" />;
    } else if (actionType.includes("update") || actionType.includes("extend")) {
      return <Icon name="Settings" className="w-5 h-5 text-blue-600" />;
    } else if (actionType.includes("view") || actionType.includes("get")) {
      return <Icon name="Eye" className="w-5 h-5 text-purple-600" />;
    } else if (actionType.includes("user") || actionType.includes("quota")) {
      return <Icon name="User" className="w-5 h-5 text-indigo-600" />;
    }
    return <Icon name="FileText" className="w-5 h-5 text-gray-600" />;
  };

  // Get action badge color
  const getActionBadgeColor = (actionType) => {
    if (!actionType) return "bg-gray-100 text-gray-800";

    if (actionType.includes("create") || actionType.includes("set")) {
      return "bg-green-100 text-green-800";
    } else if (actionType.includes("delete") || actionType.includes("cancel")) {
      return "bg-red-100 text-red-800";
    } else if (actionType.includes("update") || actionType.includes("extend")) {
      return "bg-blue-100 text-blue-800";
    } else if (actionType.includes("view") || actionType.includes("get")) {
      return "bg-purple-100 text-purple-800";
    } else if (actionType.includes("user") || actionType.includes("quota")) {
      return "bg-indigo-100 text-indigo-800";
    }
    return "bg-gray-100 text-gray-800";
  };

  // Effect to fetch data when pagination parameters change
  useEffect(() => {
    fetchAdminLogs(pagination.page);
  }, [pagination.page, pagination.limit]);

  // Load initial data
  useEffect(() => {
    fetchAdminLogs();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Admin Action Logs
            </h1>
            <p className="text-gray-600">
              Monitor and audit all administrative actions performed in the
              system
            </p>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <button
              className="btn-secondary"
              onClick={() => fetchAdminLogs(pagination.page)}
            >
              <Icon
                name="RefreshCw"
                className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Error alerts now handled by global AlertContainer */}

      {/* Filters */}
      <Card className="bg-white border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Filter" className="w-5 h-5" />
            Filters
          </CardTitle>
          <CardDescription>
            Filter admin logs by action type, target user, or admin ID
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Action Type
              </label>
              <Select
                value={filters.actionType}
                onValueChange={(value) =>
                  handleFilterChange("actionType", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All actions</SelectItem>
                  <SelectItem value="user_subscription_set">
                    Set Subscription
                  </SelectItem>
                  <SelectItem value="user_subscription_extend">
                    Extend Subscription
                  </SelectItem>
                  <SelectItem value="user_subscription_cancel">
                    Cancel Subscription
                  </SelectItem>
                  <SelectItem value="user_quota_reset">Reset Quota</SelectItem>
                  <SelectItem value="user_plan_change">Change Plan</SelectItem>
                  <SelectItem value="user_view">View User</SelectItem>
                  <SelectItem value="system_maintenance">
                    System Maintenance
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target User ID
              </label>
              <input
                type="text"
                placeholder="Enter user ID"
                value={filters.targetAuthId}
                onChange={(e) =>
                  handleFilterChange("targetAuthId", e.target.value)
                }
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin ID
              </label>
              <input
                type="text"
                placeholder="Enter admin ID"
                value={filters.adminId}
                onChange={(e) => handleFilterChange("adminId", e.target.value)}
                className="input-field"
              />
            </div>
            <div className="flex items-end">
              <button onClick={clearFilters} className="btn-primary w-full">
                Clear Filters
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Admin Logs Table */}
      <Card className="bg-white border-0 shadow-lg">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Admin Action Logs</CardTitle>
              <CardDescription className="mt-2">
                {loading
                  ? "Loading..."
                  : `${pagination.totalItems || 0} total logs`}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-600">Loading admin logs...</span>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-12">
              <Icon
                name="FileText"
                className="w-12 h-12 mx-auto mb-4 text-gray-300"
              />
              <p className="text-gray-500">No admin logs found</p>
              <p className="text-sm text-gray-400 mt-1">
                Try adjusting your filters or check back later
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Target User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Admin
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timestamp
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {logs.map((log, index) => (
                    <tr
                      key={index}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          {getActionIcon(log.action_type)}
                          <div>
                            <Badge
                              className={`${getActionBadgeColor(log.action_type)} border-0`}
                            >
                              {formatActionType(log.action_type)}
                            </Badge>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm">
                          {log.target_auth_id ? (
                            <div>
                              <p className="font-medium text-gray-900">
                                {log.target_auth_id}
                              </p>
                              {log.target_user_name && (
                                <p className="text-gray-500">
                                  {log.target_user_name}
                                </p>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-400">System-wide</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm">
                          <p className="font-medium text-gray-900">
                            {log.admin_id || "Unknown"}
                          </p>
                          {log.admin_name && (
                            <p className="text-gray-500">{log.admin_name}</p>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm max-w-xs">
                          {log.details && (
                            <p className="text-gray-900 mb-1">{log.details}</p>
                          )}
                          {log.notes && (
                            <p className="text-gray-600 mb-1">
                              Notes: {log.notes}
                            </p>
                          )}
                          {log.reason && (
                            <p className="text-gray-600">
                              Reason: {log.reason}
                            </p>
                          )}
                          {!log.details && !log.notes && !log.reason && (
                            <span className="text-gray-400">No details</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">
                          <div className="flex items-center gap-1 mb-1">
                            {log.created_at
                              ? new Date(log.created_at).toLocaleDateString()
                              : "N/A"}
                          </div>
                          <div className="text-gray-500 text-xs">
                            {log.created_at
                              ? new Date(log.created_at).toLocaleTimeString()
                              : ""}
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {!loading && logs.length > 0 && (
            <div className="mt-6 border-t border-gray-200 pt-4">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                totalItems={pagination.totalItems}
                onPageChange={pagination.goToPage}
                onItemsPerPageChange={pagination.changeLimit}
                showItemsPerPage={false}
                showInfo={true}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
