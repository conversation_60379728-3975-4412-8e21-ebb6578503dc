import React from "react";
import { cn } from "../../lib/utils";

const Skeleton = ({
  className = "",
  height = "h-4",
  width = "w-full",
  rounded = "rounded",
  ...props
}) => {
  return (
    <div
      className={cn(
        "animate-pulse bg-gray-200 dark:bg-gray-700",
        height,
        width,
        rounded,
        className,
      )}
      {...props}
    />
  );
};

// Dashboard Overview Card skeleton
const DashboardCardSkeleton = ({ className = "" }) => {
  return (
    <div
      className={cn(
        "bg-white border border-gray-200 rounded-xl p-6 shadow-sm",
        className,
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <Skeleton width="w-20" height="h-4" />
        <Skeleton width="w-10" height="w-10" rounded="rounded-lg" />
      </div>
      <Skeleton width="w-16" height="h-8" className="mb-2" />
      <Skeleton width="w-32" height="h-3" />
    </div>
  );
};

// Admin metrics card skeleton
const AdminMetricCardSkeleton = ({ className = "" }) => {
  return (
    <div
      className={cn("bg-white border-0 shadow-lg rounded-xl p-6", className)}
    >
      <div className="flex items-center justify-between space-y-0 pb-2">
        <Skeleton width="w-20" height="h-4" />
        <Skeleton width="w-10" height="w-10" rounded="rounded-lg" />
      </div>
      <div className="pt-2">
        <Skeleton width="w-16" height="h-8" className="mb-2" />
        <Skeleton width="w-24" height="h-3" />
      </div>
    </div>
  );
};

// Product grid card skeleton
const ProductCardSkeleton = ({ className = "" }) => {
  return (
    <div
      className={cn(
        "h-80 bg-white border border-gray-200 rounded-xl p-6 flex flex-col",
        className,
      )}
    >
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <Skeleton width="w-32" height="h-6" />
        <Skeleton width="w-2" height="w-2" rounded="rounded-full" />
      </div>

      {/* Category */}
      <div className="mb-4">
        <Skeleton width="w-20" height="h-6" rounded="rounded-md" />
      </div>

      {/* Description */}
      <div className="flex-1 mb-4 space-y-2">
        <Skeleton width="w-full" height="h-4" />
        <Skeleton width="w-3/4" height="h-4" />
      </div>

      {/* Variations */}
      <div className="flex gap-1.5 mb-4">
        <Skeleton width="w-16" height="h-6" rounded="rounded-md" />
        <Skeleton width="w-20" height="h-6" rounded="rounded-md" />
      </div>

      {/* Price and buttons */}
      <div className="mt-auto">
        <div className="flex items-center justify-between mb-3">
          <Skeleton width="w-16" height="h-5" />
          <Skeleton width="w-12" height="h-4" />
        </div>
        <div className="flex gap-2">
          <Skeleton width="w-16" height="h-8" rounded="rounded-md" />
          <Skeleton width="w-16" height="h-8" rounded="rounded-md" />
        </div>
      </div>
    </div>
  );
};

// Contact list item skeleton
const ContactCardSkeleton = ({ className = "" }) => {
  return (
    <div
      className={cn(
        "bg-white border border-gray-200 rounded-xl p-4",
        className,
      )}
    >
      <div className="flex items-center space-x-4">
        <Skeleton width="w-12" height="w-12" rounded="rounded-full" />
        <div className="flex-1 space-y-2">
          <div className="flex items-center justify-between">
            <Skeleton width="w-32" height="h-5" />
            <Skeleton width="w-16" height="h-4" />
          </div>
          <Skeleton width="w-24" height="h-4" />
          <div className="flex items-center gap-2">
            <Skeleton width="w-6" height="w-6" rounded="rounded" />
            <Skeleton width="w-20" height="h-3" />
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <Skeleton width="w-8" height="w-8" rounded="rounded-md" />
          <Skeleton width="w-8" height="w-8" rounded="rounded-md" />
        </div>
      </div>
    </div>
  );
};

// Knowledge base entry skeleton
const KnowledgeEntryCardSkeleton = ({ className = "" }) => {
  return (
    <div
      className={cn(
        "bg-white border border-gray-200 rounded-xl p-6",
        className,
      )}
    >
      <div className="flex items-start justify-between mb-3">
        <Skeleton width="w-48" height="h-6" />
        <div className="flex gap-2">
          <Skeleton width="w-8" height="w-8" rounded="rounded-md" />
          <Skeleton width="w-8" height="w-8" rounded="rounded-md" />
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <Skeleton width="w-full" height="h-4" />
        <Skeleton width="w-full" height="h-4" />
        <Skeleton width="w-2/3" height="h-4" />
      </div>

      <div className="pt-3 border-t border-gray-100">
        <Skeleton width="w-24" height="h-8" rounded="rounded-md" />
      </div>
    </div>
  );
};

// Table skeleton for data tables
const TableSkeleton = ({ rows = 5, columns = 4, className = "" }) => {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Header skeleton */}
      <div
        className="grid gap-4"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} height="h-4" width="w-3/4" />
        ))}
      </div>

      {/* Rows skeleton */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton
              key={colIndex}
              height="h-4"
              width={colIndex === 0 ? "w-full" : "w-2/3"}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

// Page skeleton for full page loading
const PageSkeleton = ({
  showHeader = true,
  showCards = true,
  cardType = "dashboard",
  cardCount = 6,
  className = "",
}) => {
  const renderCards = () => {
    switch (cardType) {
      case "admin-metrics":
        return Array.from({ length: 4 }).map((_, i) => (
          <AdminMetricCardSkeleton key={i} />
        ));
      case "products":
        return Array.from({ length: cardCount }).map((_, i) => (
          <ProductCardSkeleton key={i} />
        ));
      case "contacts":
        return Array.from({ length: cardCount }).map((_, i) => (
          <ContactCardSkeleton key={i} />
        ));
      case "knowledge":
        return Array.from({ length: cardCount }).map((_, i) => (
          <KnowledgeEntryCardSkeleton key={i} />
        ));
      default:
        return Array.from({ length: cardCount }).map((_, i) => (
          <DashboardCardSkeleton key={i} />
        ));
    }
  };

  const getGridClasses = () => {
    switch (cardType) {
      case "admin-metrics":
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6";
      case "products":
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";
      case "contacts":
      case "knowledge":
        return "space-y-4";
      default:
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header skeleton */}
      {showHeader && (
        <div>
          <Skeleton width="w-64" height="h-8" className="mb-2" />
          <Skeleton width="w-96" height="h-4" />
        </div>
      )}

      {/* Cards skeleton */}
      {showCards && <div className={getGridClasses()}>{renderCards()}</div>}
    </div>
  );
};

// Chat skeleton for chat interfaces
const ChatSkeleton = ({ messages = 5, className = "" }) => {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: messages }).map((_, i) => (
        <div
          key={i}
          className={cn("flex", i % 2 === 0 ? "justify-start" : "justify-end")}
        >
          <div className="max-w-xs space-y-2">
            <Skeleton width={i % 2 === 0 ? "w-32" : "w-40"} height="h-4" />
            <Skeleton width={i % 2 === 0 ? "w-48" : "w-36"} height="h-4" />
          </div>
        </div>
      ))}
    </div>
  );
};

// Form skeleton for forms
const FormSkeleton = ({ fields = 4, className = "" }) => {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: fields }).map((_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton width="w-24" height="h-4" />
          <Skeleton width="w-full" height="h-10" rounded="rounded-md" />
        </div>
      ))}
      <div className="flex justify-end space-x-2 pt-4">
        <Skeleton width="w-20" height="h-9" rounded="rounded-md" />
        <Skeleton width="w-20" height="h-9" rounded="rounded-md" />
      </div>
    </div>
  );
};

export {
  Skeleton,
  DashboardCardSkeleton,
  AdminMetricCardSkeleton,
  ProductCardSkeleton,
  ContactCardSkeleton,
  KnowledgeEntryCardSkeleton,
  TableSkeleton,
  PageSkeleton,
  ChatSkeleton,
  FormSkeleton,
};
export default Skeleton;
