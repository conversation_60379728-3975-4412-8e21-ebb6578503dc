import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Loader2, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import oauthService from "../services/oauth";

export default function OAuthCallback() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState('processing'); // processing, success, error
  const [message, setMessage] = useState('Processing OAuth callback...');
  const [result, setResult] = useState(null);

  useEffect(() => {
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = async () => {
    try {
      // Get callback parameters
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');
      const errorDescription = searchParams.get('error_description');

      // Check for OAuth errors
      if (error) {
        throw new Error(errorDescription || `OAuth error: ${error}`);
      }

      if (!code || !state) {
        throw new Error('Missing authorization code or state parameter');
      }

      // Determine platform from URL path or referrer
      const platform = getPlatformFromCallback() || getPlatformFromState(state) || 'messenger'; // fallback to messenger since whatsapp doesn't use OAuth

      console.log('Processing OAuth callback for platform:', platform);
      console.log('Authorization code:', code);
      console.log('State parameter:', state);
      console.log('Full URL params:', Object.fromEntries(searchParams.entries()));

      setMessage(`Connecting your ${platform.charAt(0).toUpperCase() + platform.slice(1)} account...`);

      // Handle OAuth callback
      const callbackResult = await oauthService.handleCallback(platform, code, state);
      console.log('Callback result:', callbackResult);

      if (callbackResult.success) {
        setStatus('success');
        setMessage(`Successfully connected your ${platform.charAt(0).toUpperCase() + platform.slice(1)} account!`);
        setResult(callbackResult.data);

        // Send success message to parent window
        if (window.opener) {
          console.log('Sending success message to parent window');
          window.opener.postMessage({
            type: 'OAUTH_SUCCESS',
            platform,
            result: callbackResult.data,
          }, '*'); // Use '*' for broader compatibility

          // Also try the specific origin
          window.opener.postMessage({
            type: 'OAUTH_SUCCESS',
            platform,
            result: callbackResult.data,
          }, window.location.origin);
        }

        // Redirect after a short delay
        setTimeout(() => {
          if (window.opener && !window.opener.closed) {
            console.log('Closing OAuth popup');
            window.close();
          } else {
            console.log('Redirecting to social media page');
            navigate('/dashboard/social-media');
          }
        }, 1500);
      } else {
        throw new Error(callbackResult.error || 'OAuth callback failed');
      }
    } catch (error) {
      console.error('OAuth callback error:', error);
      setStatus('error');
      setMessage(error.message);

      // Send error message to parent window
      if (window.opener) {
        window.opener.postMessage({
          type: 'OAUTH_ERROR',
          error: error.message,
        }, window.location.origin);
      }

      // Redirect to error page after delay
      setTimeout(() => {
        if (window.opener) {
          window.close();
        } else {
          navigate('/dashboard/social-media?error=' + encodeURIComponent(error.message));
        }
      }, 3000);
    }
  };

  const getPlatformFromCallback = () => {
    // Try to get platform from URL parameters or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const platformParam = urlParams.get('platform');
    if (platformParam && ['messenger', 'instagram'].includes(platformParam)) {
      return platformParam;
    }

    // Try to get from localStorage (set during OAuth initiation)
    const storedPlatform = localStorage.getItem('oauth_platform');
    if (storedPlatform && ['messenger', 'instagram'].includes(storedPlatform)) {
      localStorage.removeItem('oauth_platform'); // Clean up
      return storedPlatform;
    }

    return null;
  };

  const getPlatformFromState = () => {
    // This is a simple implementation - in production you might want to
    // decode the state parameter to get the platform information
    const referrer = document.referrer;
    if (referrer.includes('messenger')) return 'messenger';
    if (referrer.includes('instagram')) return 'instagram';
    return null;
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="w-12 h-12 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="w-12 h-12 text-green-600" />;
      case 'error':
        return <XCircle className="w-12 h-12 text-red-600" />;
      default:
        return <AlertCircle className="w-12 h-12 text-yellow-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };

  return (
    <div className="min-h-screen w-full bg-gray-50 flex items-center justify-center p-2 sm:p-4">
      <div className="w-full h-full bg-white rounded-none sm:rounded-xl shadow-lg p-4 sm:p-8 text-center flex flex-col items-center justify-center">
        <div className="mb-6">
          {getStatusIcon()}
        </div>

        <h1 className={`text-xl sm:text-2xl font-bold mb-4 ${getStatusColor()}`}>
          {status === 'processing' && 'Connecting Account'}
          {status === 'success' && 'Connection Successful!'}
          {status === 'error' && 'Connection Failed'}
        </h1>

        <p className="text-gray-600 mb-6 text-base sm:text-lg">
          {message}
        </p>

        {status === 'success' && result && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="text-sm text-green-800">
              <p className="font-medium mb-2">Account Details:</p>
              {result.user && (
                <div className="space-y-1">
                  <p><strong>Name:</strong> {result.user.name}</p>
                  {result.user.email && (
                    <p><strong>Email:</strong> {result.user.email}</p>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {status === 'error' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="text-sm text-red-800">
              <p className="font-medium mb-2">What went wrong?</p>
              <p>Please try connecting again or contact support if the problem persists.</p>
            </div>
          </div>
        )}

        <div className="space-y-3">
          {status === 'processing' && (
            <div className="text-sm text-gray-500">
              Please wait while we connect your account...
            </div>
          )}

          {status === 'success' && (
            <div className="text-sm text-gray-500">
              {window.opener ? 'This window will close automatically...' : 'Redirecting to dashboard...'}
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-2">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
              
              {!window.opener && (
                <button
                  onClick={() => navigate('/dashboard/social-media')}
                  className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Back to Dashboard
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
