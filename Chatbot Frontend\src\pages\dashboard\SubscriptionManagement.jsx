import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import { Alert, AlertDescription } from "../../components/ui/alert";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { Textarea } from "../../components/ui/textarea";
import {
  CreditCard,
  RefreshCw,
  Edit3,
  CalendarPlus,
  CalendarMinus,
  RotateCcw,
  Calendar,
  AlertCircle,
  CheckCircle,
  Settings,
  X,
} from "lucide-react";
import Icon from "../../components/ui/icon";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select2";

export default function SubscriptionManagement({
  subscriptionUsers,
  subscriptionStatistics,
  loading,
  onFetchSubscriptionUsers,
  onOpenSubscriptionModal,
  onOpenExpireModal,
  showSubscriptionModal,
  showExpireModal,
  subscriptionModalData,
  onCloseSubscriptionModal,
  onCloseExpireModal,
  onHandleSubscriptionUpdate,
  onHandleExpireSubscriptions,
  onSetSubscriptionModalData,
}) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle refresh with extended spinning animation
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onFetchSubscriptionUsers();
    } finally {
      // Continue spinning for a short period after refresh completes
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000); // Continue spinning for 1 second after completion
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num?.toString() || "0";
  };

  // MetricCard component for dashboard metrics
  const MetricCard = ({ title, value, icon, color, subtitle }) => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <div
          className={`p-2 rounded-lg ${
            color === "blue"
              ? "bg-blue-50"
              : color === "green"
                ? "bg-green-50"
                : color === "red"
                  ? "bg-red-50"
                  : color === "yellow"
                    ? "bg-yellow-50"
                    : "bg-gray-50"
          }`}
        >
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">
          {formatNumber(value)}
        </div>
        <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Subscription Management
            </h1>
            <p className="text-gray-600">
              Manage user subscriptions, plans, and billing
            </p>
          </div>
          <div className="flex flex-wrap gap-3">
            <button className="btn-secondary" onClick={handleRefresh}>
              <Icon
                name="RefreshCw"
                className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
              />
              Refresh All Data
            </button>
          </div>
          <div className="flex flex-wrap gap-3">
            <button className="btn-primary" onClick={onOpenExpireModal}>
              <Icon name="CalendarX" className={`w-4 h-4 mr-2`} />
              Expire Subscriptions
            </button>
          </div>
        </div>
      </div>

      {/* Alert Messages - Handled by parent AdminDashboard */}

      {/* Subscription Statistics */}
      {subscriptionStatistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <MetricCard
            title="Active Subscriptions"
            value={
              subscriptionUsers?.filter(
                (u) => u.plan !== "free" && !u.is_expired,
              ).length || 0
            }
            icon={<Icon name="CreditCard" className="w-5 h-5 text-green-600" />}
            color="green"
            subtitle="Currently active"
          />
          <MetricCard
            title="Expired Subscriptions"
            value={
              subscriptionStatistics?.data?.alerts?.expiredSubscriptions || 0
            }
            icon={<Icon name="CreditCard" className="w-5 h-5 text-red-600" />}
            color="red"
            subtitle="Need renewal"
          />
          <MetricCard
            title="Total Users"
            value={subscriptionStatistics?.data?.overview?.totalUsers || 0}
            icon={<Icon name="CreditCard" className="w-5 h-5 text-blue-600" />}
            color="blue"
            subtitle="Platform users"
          />
          <MetricCard
            title="Quota Violations"
            value={
              subscriptionStatistics?.data?.alerts?.todayQuotaViolations || 0
            }
            icon={
              <Icon name="CreditCard" className="w-5 h-5 text-yellow-600" />
            }
            color="yellow"
            subtitle="Today's violations"
          />
        </div>
      )}

      <div className="card">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            Subscription Management
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expires
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Started
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {subscriptionUsers.map((userData) => (
                <tr key={userData.auth_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-medium ${
                          userData.plan === "admin"
                            ? "bg-red-500"
                            : userData.plan === "besar"
                              ? "bg-purple-500"
                              : userData.plan === "popular"
                                ? "bg-blue-500"
                                : userData.plan === "kecil"
                                  ? "bg-green-500"
                                  : userData.plan === "trial"
                                    ? "bg-yellow-500"
                                    : "bg-gray-500"
                        }`}
                      >
                        {(userData.auth_id || "U").charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {userData.display_name} <br />(
                          {userData.auth_id || "No ID"})
                        </p>
                        <p className="text-sm text-gray-500">
                          {userData.email || "No email"}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        userData.plan === "admin"
                          ? "bg-red-100 text-red-800"
                          : userData.plan === "besar"
                            ? "bg-purple-100 text-purple-800"
                            : userData.plan === "popular"
                              ? "bg-blue-100 text-blue-800"
                              : userData.plan === "kecil"
                                ? "bg-green-100 text-green-800"
                                : userData.plan === "trial"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {(() => {
                        switch (userData.plan) {
                          case "free":
                            return "Kedai Free Plan";
                          case "admin":
                            return "Kedai Admin Plan";
                          case "besar":
                            return "Kedai Besar Plan";
                          case "popular":
                            return "Kedai Popular Plan";
                          case "kecil":
                            return "Kedai Kecil Plan";
                          case "trial":
                            return "Kedai Trial Plan";
                          default:
                            return userData.plan;
                        }
                      })()}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        userData.is_expired
                          ? "bg-red-100 text-red-800"
                          : "bg-green-100 text-green-800"
                      }`}
                    >
                      {userData.is_expired ? "Expired" : "Active"}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-center">
                      {userData.subscription_end_date ? (
                        <>
                          <p
                            className={`font-medium ${
                              userData.days_remaining > 7
                                ? "text-green-600"
                                : userData.days_remaining > 3
                                  ? "text-yellow-600"
                                  : "text-red-600"
                            }`}
                          >
                            {userData.days_remaining || 0} days left
                          </p>
                          <p className="text-gray-500">
                            {new Date(
                              userData.subscription_end_date,
                            ).toLocaleDateString()}
                          </p>
                        </>
                      ) : (
                        <p className="text-gray-500">No expiry</p>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 text-center">
                    {userData.subscription_start_date
                      ? new Date(
                          userData.subscription_start_date,
                        ).toLocaleDateString()
                      : "N/A"}
                  </td>
                  <td className="px-6 py-4 text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-gray-100"
                        >
                          <Icon name="MoreHorizontal" className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem
                          onClick={() =>
                            onOpenSubscriptionModal(
                              userData.auth_id,
                              "set",
                              userData.plan,
                            )
                          }
                          className="flex items-center gap-2 cursor-pointer text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        >
                          <Icon name="Edit3" className="w-4 h-4" />
                          Set Plan
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            onOpenSubscriptionModal(userData.auth_id, "extend")
                          }
                          className="flex items-center gap-2 cursor-pointer text-green-600 hover:text-green-700 hover:bg-green-50"
                        >
                          <Icon name="CalendarPlus" className="w-4 h-4" />
                          Extend
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            onOpenSubscriptionModal(userData.auth_id, "reduce")
                          }
                          className="flex items-center gap-2 cursor-pointer text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                        >
                          <Icon name="CalendarMinus" className="w-4 h-4" />
                          Reduce
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            onOpenSubscriptionModal(userData.auth_id, "expire")
                          }
                          className="flex items-center gap-2 cursor-pointer text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                        >
                          <Icon name="Calendar" className="w-4 h-4" />
                          Set Expiry Date
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            onOpenSubscriptionModal(userData.auth_id, "reset")
                          }
                          className="flex items-center gap-2 cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Icon name="RotateCcw" className="w-4 h-4" />
                          Reset to Free
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
              {subscriptionUsers.length === 0 && (
                <tr>
                  <td
                    colSpan="6"
                    className="px-6 py-4 text-center text-gray-500"
                  >
                    No users found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Expire Subscriptions Modal */}
      {showExpireModal && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!loading) {
                onCloseExpireModal();
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-lg w-full shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  Expire Subscriptions
                </h3>
                {!loading && (
                  <button
                    onClick={onCloseExpireModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                )}
              </div>

              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-red-900 mb-1">Warning</h4>
                      <p className="text-sm text-red-700">
                        This will immediately expire all overdue subscriptions
                        in the system. Users with expired subscriptions will be
                        automatically downgraded to the free plan.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">
                    What will happen:
                  </h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                      All subscriptions past their end date will be marked as
                      expired
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                      Users will be automatically moved to the free plan
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                      System statistics will be updated
                    </li>
                  </ul>
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={onCloseExpireModal}
                    className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={onHandleExpireSubscriptions}
                    className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Settings className="w-4 h-4" />
                        Expire All Overdue
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Modal */}
      {showSubscriptionModal && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!loading) {
                onCloseSubscriptionModal();
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-lg w-full max-h-[90vh] overflow-y-auto shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  {subscriptionModalData.action === "set" &&
                    "Set Subscription Plan"}
                  {subscriptionModalData.action === "extend" &&
                    "Extend Subscription"}
                  {subscriptionModalData.action === "reduce" &&
                    "Reduce Subscription"}
                  {subscriptionModalData.action === "expire" &&
                    "Set Expiry Date"}
                  {subscriptionModalData.action === "reset" &&
                    "Reset Subscription"}
                </h3>
                {!loading && (
                  <button
                    onClick={onCloseSubscriptionModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                )}
              </div>

              <div className="space-y-4">
                {subscriptionModalData.action === "set" && (
                  <div>
                    <label
                      htmlFor="plan"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Plan
                    </label>
                    <Select
                      value={subscriptionModalData.plan}
                      onValueChange={(value) =>
                        onSetSubscriptionModalData((prev) => ({
                          ...prev,
                          plan: value,
                        }))
                      }
                      disabled={loading}
                    >
                      <SelectTrigger className="disabled:opacity-60">
                        <SelectValue placeholder="Select a plan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="free">Free</SelectItem>
                        <SelectItem value="trial">Trial</SelectItem>
                        <SelectItem value="kecil">Kecil</SelectItem>
                        <SelectItem value="popular">Popular</SelectItem>
                        <SelectItem value="besar">Besar</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {(subscriptionModalData.action === "extend" ||
                  subscriptionModalData.action === "reduce") && (
                  <div>
                    <label
                      htmlFor="months"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Months{" "}
                      {subscriptionModalData.action === "reduce"
                        ? "to Reduce"
                        : "to Extend"}
                    </label>
                    <input
                      id="months"
                      type="number"
                      value={subscriptionModalData.months}
                      onChange={(e) =>
                        onSetSubscriptionModalData((prev) => ({
                          ...prev,
                          months: Number(e.target.value),
                        }))
                      }
                      min="1"
                      max="24"
                      placeholder="Number of months"
                      className="input-field disabled:opacity-60"
                      disabled={loading}
                    />
                  </div>
                )}

                {subscriptionModalData.action === "expire" && (
                  <div>
                    <label
                      htmlFor="endDate"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Expiry Date
                    </label>
                    <input
                      id="endDate"
                      type="date"
                      value={subscriptionModalData.endDate}
                      onChange={(e) =>
                        onSetSubscriptionModalData((prev) => ({
                          ...prev,
                          endDate: e.target.value,
                        }))
                      }
                      className="input-field disabled:opacity-60"
                      disabled={loading}
                    />
                  </div>
                )}

                <div>
                  <label
                    htmlFor="notes"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Notes (Optional)
                  </label>
                  <textarea
                    id="notes"
                    value={subscriptionModalData.notes}
                    onChange={(e) =>
                      onSetSubscriptionModalData((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    rows={3}
                    placeholder="Add any notes about this change..."
                    className="input-field resize-none disabled:opacity-60"
                    disabled={loading}
                  />
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={onCloseSubscriptionModal}
                    className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={onHandleSubscriptionUpdate}
                    className={`flex-1 px-4 py-2.5 rounded-xl transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2 ${
                      subscriptionModalData.action === "reset"
                        ? "bg-red-600 text-white hover:bg-red-700"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    }`}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        {subscriptionModalData.action === "set"
                          ? "Set Plan"
                          : subscriptionModalData.action === "extend"
                            ? "Extend Subscription"
                            : subscriptionModalData.action === "reduce"
                              ? "Reduce Subscription"
                              : subscriptionModalData.action === "expire"
                                ? "Set Expiry"
                                : subscriptionModalData.action === "reset"
                                  ? "Reset to Free"
                                  : "Update"}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
