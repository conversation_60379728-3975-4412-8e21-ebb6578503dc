import { useState, useEffect, useRef } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { aiService } from "../../services/ai";
import { useMobileAlertMigration } from "../../contexts/MobileAlertContext";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { checkMessageGapRestriction } from "../../utils/messageValidation";
import {
  ArrowLeft,
  Send,
  Phone,
  Edit,
  Image as ImageIcon,
  Paperclip,
  Smile,
  X
} from "lucide-react";
import Icon from "../../components/ui/icon";

// Add custom CSS for animations
const animationStyles = `
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.3s ease-out;
  }

  .message-sending {
    animation: fade-in-up 0.2s ease-out, pulse 1s infinite;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = animationStyles;
  if (!document.head.querySelector('style[data-mobile-chat-animations]')) {
    styleSheet.setAttribute('data-mobile-chat-animations', 'true');
    document.head.appendChild(styleSheet);
  }
}


/**
 * Mobile Chat View Component
 * WhatsApp-like chat interface for individual contact conversations
 */
export default function MobileChatView({ user, settings = {} }) {
  const { contactId } = useParams();
  const navigate = useNavigate();
  const { showError, showSuccess, showQuickSuccess, showQuickError } = useMobileAlertMigration();
  
  const [contact, setContact] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingChat, setLoadingChat] = useState(false);
  const [messageInput, setMessageInput] = useState("");
  const [isSending, setIsSending] = useState(false);

  // Message pagination for performance
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);
  const [messagesPage, setMessagesPage] = useState(1);
  const MESSAGES_PER_PAGE = 30;

  // Enhanced message features
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [messageType, setMessageType] = useState("text");
  const [isUploading, setIsUploading] = useState(false);
  const [caption, setCaption] = useState("");

  // Human takeover features
  const [humanTakeoverStatus, setHumanTakeoverStatus] = useState(null);
  const [isTogglingTakeover, setIsTogglingTakeover] = useState(false);

  // Auto-refresh for real-time updates
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(settings.autoRefresh !== false);
  const autoRefreshIntervalRef = useRef(null);
  const refreshInterval = settings.refreshInterval || 5000;

  // Message notifications
  const [newMessageCount, setNewMessageCount] = useState(0);
  const [lastSeenMessageId, setLastSeenMessageId] = useState(null);

  // Message gap restriction
  const [messageGapRestriction, setMessageGapRestriction] = useState({
    isBlocked: false,
    message: null,
    daysSinceLastMessage: 0
  });

  const chatContainerRef = useRef(null);
  const messageInputRef = useRef(null);
  const fileInputRef = useRef(null);
  const showLoadingState = useDelayedLoading(loading, 200);

  // Gesture handling
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  // Edit contact modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    name: "",
    notes: "",
    tags: ""
  });

  // Fetch contact details
  const fetchContact = async () => {
    if (!contactId) return;

    try {
      const userId = user?.user?.id || user?.profile?.id || user?.id;
      if (!userId) return;

      const data = await aiService.getContacts({ authId: userId, limit: 100 });
      if (data.success) {
        const foundContact = data.data.find(c => c.id === contactId);
        if (foundContact) {
          setContact(foundContact);
          await fetchChatHistory(foundContact);
        } else {
          showError("Contact not found");
          navigate("/dashboard/contacts");
        }
      } else {
        showError(data.error);
      }
    } catch {
      showError("Failed to fetch contact details");
    } finally {
      setLoading(false);
    }
  };

  // Fetch chat history with pagination
  const fetchChatHistory = async (contactData, pageNum = 1, append = false, silent = false) => {
    if (!contactData) return;

    // Only show loading if not silent
    if (!silent) {
      if (!append) setLoadingChat(true);
      else setLoadingMoreMessages(true);
    }

    try {


      const userId = user?.user?.id || user?.profile?.id || user?.id;

      const data = await aiService.getChatHistory(
        userId,
        contactData.phone_number,
        MESSAGES_PER_PAGE,
        (pageNum - 1) * MESSAGES_PER_PAGE
      );

      if (data.success) {
        const rawMessages = data.data || [];

        // Transform API data to component format
        // Note: You (the logged-in user) are the Assistant, so Assistant messages should show as "user" (right side)
        // and Customer messages should show as "bot" (left side) from your perspective
        const messages = rawMessages
          .map(msg => ({
            id: msg.id,
            message: msg.content || msg.message || '', // API uses 'content', component expects 'message'
            sender: msg.sender === 'Assistant' ? 'user' : 'bot', // Assistant = You (right side), Customer = Them (left side)
            timestamp: msg.timestamp,
            message_type: msg.hasMedia ? (msg.mediaInfo?.mediaType || 'file') : 'text',
            file_url: msg.mediaInfo?.fileUrl || null,
            file_name: msg.mediaInfo?.filename || null
          }))
          // Sort messages by timestamp - oldest first (chronological order)
          .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        if (append) {
          // Prepend older messages to the beginning
          setChatHistory(prev => {
            const updatedHistory = [...messages, ...prev];
            // Check message gap restriction when history is updated
            const gapCheck = checkMessageGapRestriction(updatedHistory);
            setMessageGapRestriction(gapCheck);
            return updatedHistory;
          });
        } else {
          // For silent refresh, only update if there are new messages
          if (silent) {
            setChatHistory(prev => {
              // Check if there are new messages by comparing the latest message IDs
              if (messages.length > 0 && prev.length > 0) {
                const latestNewId = messages[0]?.id;
                const latestCurrentId = prev[0]?.id;
                if (latestNewId !== latestCurrentId) {
                  // Check message gap restriction for new messages
                  const gapCheck = checkMessageGapRestriction(messages);
                  setMessageGapRestriction(gapCheck);
                  return messages; // Update only if there are new messages
                }
                return prev; // No new messages, keep current state
              }
              // Check message gap restriction for initial messages
              const gapCheck = checkMessageGapRestriction(messages);
              setMessageGapRestriction(gapCheck);
              return messages;
            });
          } else {
            setChatHistory(messages);
            setMessagesPage(1);
            // Check message gap restriction for new chat history
            const gapCheck = checkMessageGapRestriction(messages);
            setMessageGapRestriction(gapCheck);
          }
        }

        // Check if there are more messages
        setHasMoreMessages(messages.length === MESSAGES_PER_PAGE);

        // Track new messages (only for initial load)
        if (!append && lastSeenMessageId) {
          const lastSeenIndex = messages.findIndex(msg => msg.id === lastSeenMessageId);
          if (lastSeenIndex >= 0) {
            const newMessages = messages.slice(lastSeenIndex + 1);
            const botMessages = newMessages.filter(msg => msg.sender === "bot" || msg.sender === "ai");
            setNewMessageCount(botMessages.length);
          }
        }

        // Scroll to bottom after loading messages (only for initial load and not silent)
        if (!append && !silent) {
          setTimeout(() => scrollToBottom(), 100);
        }
      } else {
        if (!silent) {
          showError(data.error || "Failed to load chat history");
        }
      }
    } catch (error) {
      if (!silent) {
        showError("Failed to load chat history", error.message);
      }
    } finally {
      if (!silent) {
        setLoadingChat(false);
        setLoadingMoreMessages(false);
      }
    }
  };

  // Load more messages (older messages)
  const loadMoreMessages = () => {
    if (!loadingMoreMessages && hasMoreMessages && contact) {
      const nextPage = messagesPage + 1;
      setMessagesPage(nextPage);
      fetchChatHistory(contact, nextPage, true);
    }
  };

  // Scroll to bottom of chat
  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      // Mark messages as read when scrolled to bottom
      markMessagesAsRead();
    }
  };

  // Mark messages as read
  const markMessagesAsRead = () => {
    if (chatHistory.length > 0) {
      const lastMessage = chatHistory[chatHistory.length - 1];
      setLastSeenMessageId(lastMessage.id);
      setNewMessageCount(0);


    }
  };

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      showQuickError("File too large (max 10MB)");
      return;
    }

    setSelectedFile(file);
    setMessageType(file.type.startsWith("image/") ? "image" : "document");

    // Create preview for images
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => setFilePreview(e.target.result);
      reader.readAsDataURL(file);
    } else {
      setFilePreview(null);
    }
  };

  // Clear file selection
  const clearFileSelection = () => {
    setSelectedFile(null);
    setFilePreview(null);
    setMessageType("text");
    setCaption("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Send message (text or file)
  const sendMessage = async () => {
    if (isSending || !contact) return;

    // Check message gap restriction first
    if (messageGapRestriction.isBlocked) {
      showQuickError(messageGapRestriction.message);
      return;
    }

    // Check if we have content to send
    const hasText = messageInput.trim();
    const hasFile = selectedFile;

    if (!hasText && !hasFile) return;

    const message = messageInput.trim();
    const fileToSend = selectedFile;
    const captionToSend = caption.trim();

    // Clear inputs immediately
    setMessageInput("");
    setCaption("");
    clearFileSelection();
    setIsSending(true);

    try {


      const userId = user?.user?.id || user?.profile?.id || user?.id;

      let data;
      if (fileToSend) {
        // Send file message using the upload-media endpoint
        setIsUploading(true);
        const formData = new FormData();
        formData.append("media", fileToSend);
        formData.append("authId", userId);
        formData.append("phoneNumber", contact.phone_number);

        if (captionToSend || message) {
          formData.append("caption", captionToSend || message);
        }

        const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:3000";
        const response = await fetch(`${API_BASE_URL}/api/ai/upload-media`, {
          method: "POST",
          body: formData,
        });

        data = await response.json();
      } else {
        // Send text message using the chat endpoint
        data = await aiService.chat(message, userId, contact.phone_number);
      }

      if (data.success) {
        // Add the sent message to chat history immediately
        const messageId = Date.now();
        const newMessage = {
          id: messageId,
          message: fileToSend ? (captionToSend || message || "File sent") : message,
          sender: "user", // Use "user" directly since this is already the transformed format for display
          timestamp: new Date().toISOString(),
          message_type: messageType,
          file_url: fileToSend ? URL.createObjectURL(fileToSend) : null,
          status: "sent"
        };
        setChatHistory(prev => [...prev, newMessage]);

        // Scroll to bottom with smooth animation
        setTimeout(() => scrollToBottom(), 100);

        // Refresh chat history to get the bot response (silent to avoid loading animation)
        setTimeout(() => {
          fetchChatHistory(contact, 1, false, true); // Add silent=true parameter
        }, 1000);
        scrollToBottom();
      } else {
        showError(data.error || "Failed to send message");
        // Restore inputs on error
        setMessageInput(message);
        if (fileToSend) {
          setSelectedFile(fileToSend);
          setCaption(captionToSend);
        }
      }
    } catch {
      showError("Failed to send message");
      // Restore inputs on error
      setMessageInput(message);
      if (fileToSend) {
        setSelectedFile(fileToSend);
        setCaption(captionToSend);
      }
    } finally {
      setIsSending(false);
      setIsUploading(false);
    }
  };

  // Handle enter key press
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Swipe gesture handlers
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isRightSwipe = distance < -50;

    // Right swipe to go back (like iOS)
    if (isRightSwipe) {
      navigate("/dashboard/contacts");
    }
  };

  // Check human takeover status
  const checkHumanTakeoverStatus = async () => {
    if (!contact) return;

    const userId = user?.user?.id || user?.profile?.id || user?.id;
    try {
      const data = await aiService.getHumanTakeoverStatus(
        userId,
        contact.phone_number
      );

      if (data.success) {
        setHumanTakeoverStatus(data.data);
      }
    } catch {
      // Silently fail for takeover status check
    }
  };

  // Toggle human takeover
  const toggleHumanTakeover = async () => {
    if (!contact || isTogglingTakeover) return;

    setIsTogglingTakeover(true);
    const userId = user?.user?.id || user?.profile?.id || user?.id;

    try {
      const isCurrentlyActive = humanTakeoverStatus?.isActive;
      let data;

      if (isCurrentlyActive) {
        data = await aiService.endHumanTakeover(userId, contact.phone_number);
      } else {
        data = await aiService.startHumanTakeover(userId, contact.phone_number, 30);
      }

      if (data.success) {
        showSuccess(data.data.message);
        checkHumanTakeoverStatus();
      } else {
        showError(data.error || "Failed to toggle human takeover");
      }
    } catch {
      showError("Failed to toggle human takeover");
    } finally {
      setIsTogglingTakeover(false);
    }
  };

  // Auto-refresh chat
  const startAutoRefresh = () => {
    if (autoRefreshIntervalRef.current) {
      clearInterval(autoRefreshIntervalRef.current);
    }

    if (autoRefreshEnabled && contact) {
      autoRefreshIntervalRef.current = setInterval(() => {
        // Silent refresh - don't show loading or scroll
        fetchChatHistory(contact, 1, false, true); // Add silent parameter
      }, refreshInterval);
    }
  };

  useEffect(() => {
    fetchContact();
  }, [contactId, user]);

  // Handle scroll events to mark messages as read and load more messages
  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      const isAtTop = scrollTop <= 10;

      // Mark messages as read when at bottom
      if (isAtBottom && newMessageCount > 0) {
        markMessagesAsRead();
      }

      // Load more messages when at top
      if (isAtTop && hasMoreMessages && !loadingMoreMessages) {
        loadMoreMessages();
      }
    }
  };

  // Update auto-refresh when settings change
  useEffect(() => {
    setAutoRefreshEnabled(settings.autoRefresh !== false);
  }, [settings.autoRefresh]);

  useEffect(() => {
    if (contact) {
      checkHumanTakeoverStatus();
      startAutoRefresh();
    }

    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
      }
    };
  }, [contact, autoRefreshEnabled, refreshInterval]);

  // Format message timestamp with date and time
  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    const time = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    // If message is from today, show only time
    if (messageDate.getTime() === today.getTime()) {
      return time;
    }

    // If message is from yesterday
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    if (messageDate.getTime() === yesterday.getTime()) {
      return `Yesterday ${time}`;
    }

    // If message is from this week (within 7 days)
    const daysDiff = Math.floor((today - messageDate) / (1000 * 60 * 60 * 24));
    if (daysDiff < 7) {
      const dayName = date.toLocaleDateString([], { weekday: 'short' });
      return `${dayName} ${time}`;
    }

    // For older messages, show date and time
    const dateStr = date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    return `${dateStr} ${time}`;
  };

  // Get contact initials
  const getContactInitials = (contact) => {
    if (contact?.name) {
      return contact.name
        .split(" ")
        .map(word => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    return contact?.phone_number?.slice(-2) || "??";
  };

  // Edit contact functions
  const openEditModal = () => {
    if (!contact) return;

    setEditForm({
      name: contact.name || "",
      notes: contact.notes || "",
      tags: contact.tags ? contact.tags.join(", ") : ""
    });
    setShowEditModal(true);
  };

  const updateContact = async () => {
    if (!contact) return;

    try {
      const userId = user?.user?.id || user?.profile?.id || user?.id;
      if (!userId) {
        showError("User not authenticated");
        return;
      }



      const updateData = {
        authId: userId,
        name: editForm.name.trim() || null,
        notes: editForm.notes.trim() || null,
        tags: editForm.tags.split(",").map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await aiService.updateContact(contact.id, updateData);

      if (response.success) {
        // Update local contact state
        const updatedContact = {
          ...contact,
          ...updateData
        };
        setContact(updatedContact);

        showQuickSuccess("Contact updated!");
        setShowEditModal(false);
      } else {
        showQuickError(response.error || "Failed to update contact");
      }
    } catch (error) {
      showError("Failed to update contact", error);
    }
  };

  if (showLoadingState) {
    return (
      <div className="h-full flex flex-col">
        {/* Header skeleton */}
        <div className="bg-white border-b border-gray-200 p-4 animate-pulse">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-gray-200 rounded"></div>
            <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
        
        {/* Chat skeleton */}
        <div className="flex-1 p-4 space-y-4 animate-pulse">
          {[1, 2, 3].map((i) => (
            <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
              <div className="max-w-xs">
                <div className="h-10 bg-gray-200 rounded-lg mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Contact not found</p>
          <button
            onClick={() => navigate("/dashboard/contacts")}
            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="h-full w-full flex flex-col bg-white overflow-hidden"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 p-4 flex items-center gap-3">
        <button
          onClick={() => navigate("/dashboard/contacts")}
          className="p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        
        <div className="relative">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
            {getContactInitials(contact)}
          </div>
          {/* New message indicator */}
          {newMessageCount > 0 && (
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-xs text-white font-bold">
                {newMessageCount > 9 ? "9+" : newMessageCount}
              </span>
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 truncate">
            {contact.name || "Unknown Contact"}
          </h3>
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Phone className="w-3 h-3" />
            <span className="truncate">{contact.phone_number}</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Human Takeover Toggle */}
          {humanTakeoverStatus && (
            <button
              onClick={toggleHumanTakeover}
              disabled={isTogglingTakeover}
              className={`px-5 py-3 rounded-full text-xs font-medium transition-colors ${
                humanTakeoverStatus.isActive
                  ? "bg-orange-100 text-orange-700 hover:bg-orange-200"
                  : "bg-green-100 text-green-700 hover:bg-green-200"
              }`}
            >
              {isTogglingTakeover ? "..." : (humanTakeoverStatus.isActive ? "Manual" : "Auto")}
            </button>
          )}

          <button
            onClick={openEditModal}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            title="Edit contact"
          >
            <Edit className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Chat Messages */}
      <div
        ref={chatContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50"
      >
        {loadingChat ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-gray-600 text-sm">Loading messages...</p>
            </div>
          </div>
        ) : chatHistory.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                <Icon name="MessageCircle" className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-600">No messages yet</p>
              <p className="text-gray-500 text-sm">Start a conversation!</p>
              <p className="text-xs text-red-500 mt-2">Debug: chatHistory.length = {chatHistory.length}</p>
            </div>
          </div>
        ) : (
          <>
            {/* Beginning of conversation indicator */}
            {!hasMoreMessages && chatHistory.length > 0 && (
              <div className="text-center p-4 text-gray-500 text-sm">
                Beginning of conversation
              </div>
            )}

            {/* Load more messages indicator */}
            {loadingMoreMessages && (
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                <span className="text-gray-600 text-sm">Loading older messages...</span>
              </div>
            )}

            {chatHistory.map((message, index) => {
            const isFromYou = message.sender === "user"; // Your messages as Assistant (right side)

            // Handle both mobile and desktop data formats
            const hasMedia = message.hasMedia ||
                            message.message_type === "image" ||
                            message.message_type === "document" ||
                            message.message_type === "video" ||
                            message.message_type === "audio";

            const mediaType = message.mediaInfo?.mediaType || message.message_type;
            const mediaUrl = message.mediaInfo?.fileUrl || message.file_url;
            const fileName = message.mediaInfo?.fileName || message.file_name;

            return (
              <div
                key={message.id || index}
                className={`flex ${isFromYou ? "justify-end" : "justify-start"} mb-3 animate-fade-in-up`}
              >
                <div
                  className={`max-w-xs lg:max-w-md rounded-lg overflow-hidden shadow-sm ${
                    isFromYou
                      ? "bg-blue-600 text-white"
                      : "bg-white text-gray-900 border border-gray-200"
                  }`}
                >
                  {/* Media content */}
                  {hasMedia && mediaUrl && (
                    <div className="relative">
                      {mediaType === "image" ? (
                        <img
                          src={mediaUrl}
                          alt="Shared image"
                          className="w-full h-auto max-h-64 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                          onClick={() => window.open(mediaUrl, '_blank')}
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      ) : mediaType === "video" ? (
                        <video
                          controls
                          className="w-full h-auto max-h-64 rounded-lg"
                          src={mediaUrl}
                        >
                          Your browser does not support the video tag.
                        </video>
                      ) : mediaType === "audio" ? (
                        <audio
                          controls
                          className="w-full"
                          src={mediaUrl}
                        >
                          Your browser does not support the audio tag.
                        </audio>
                      ) : (
                        <div className="p-3 flex items-center gap-2 bg-gray-50">
                          <Paperclip className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-700 truncate">
                            {fileName || "Document"}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Message text */}
                  {message.message && (
                    <div className="px-3 py-2">
                      <p className="text-sm whitespace-pre-wrap break-words">
                        {message.message}
                      </p>
                    </div>
                  )}

                  {/* Timestamp */}
                  <div className="px-3 pb-2">
                    <p
                      className={`text-xs ${
                        isFromYou ? "text-blue-100" : "text-gray-500"
                      }`}
                    >
                      {formatMessageTime(message.timestamp)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
          </>
        )}

        {/* Typing indicator when sending message */}
        {isSending && (
          <div className="flex justify-end animate-fade-in-up">
            <div className="max-w-xs lg:max-w-md rounded-lg overflow-hidden bg-blue-500 text-white shadow-lg animate-pulse">
              <div className="px-3 py-2">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200">
        {/* File Preview */}
        {selectedFile && (
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-start gap-3 bg-gray-50 rounded-lg p-3">
              {filePreview ? (
                <img
                  src={filePreview}
                  alt="Preview"
                  className="w-16 h-16 object-cover rounded-lg"
                />
              ) : (
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                  <Paperclip className="w-6 h-6 text-gray-400" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
                {messageType === "image" && (
                  <input
                    type="text"
                    placeholder="Add a caption..."
                    value={caption}
                    onChange={(e) => setCaption(e.target.value)}
                    className="mt-2 w-full px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                )}
              </div>
              <button
                onClick={clearFileSelection}
                className="p-1 hover:bg-gray-200 rounded-full transition-colors"
              >
                <Icon name="X" className="w-4 h-4 text-gray-500" />
              </button>
            </div>
          </div>
        )}

        {/* Message Gap Restriction Warning */}
        {messageGapRestriction.isBlocked && (
          <div className="px-4 py-2 bg-red-50 border-t border-red-200">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <p className="text-sm text-red-700 font-medium">
                {messageGapRestriction.message}
              </p>
            </div>
          </div>
        )}

        <div className="p-4">
          <div className="flex items-center gap-2">
            {/* File input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*,.pdf,.doc,.docx,.txt"
              onChange={handleFileSelect}
              className="hidden"
            />

            <button
              onClick={() => fileInputRef.current?.click()}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Paperclip className="w-5 h-5 text-gray-500" />
            </button>

            <div className="flex-1 relative">
              <textarea
                ref={messageInputRef}
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={selectedFile ? "Add a message..." : "Type a message..."}
                rows={1}
                className="mobile-input w-full px-4 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{ minHeight: '40px', maxHeight: '120px' }}
              />
            </div>

            <button
              onClick={sendMessage}
              disabled={(!messageInput.trim() && !selectedFile) || isSending || messageGapRestriction.isBlocked}
              className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 transform ${
                (messageInput.trim() || selectedFile) && !isSending && !messageGapRestriction.isBlocked
                  ? "bg-blue-600 text-white hover:bg-blue-700 hover:scale-105 active:scale-95"
                  : messageGapRestriction.isBlocked
                  ? "bg-red-200 text-red-400 cursor-not-allowed"
                  : "bg-gray-200 text-gray-400"
              } ${isSending ? "animate-pulse" : ""}`}
              title={messageGapRestriction.isBlocked ? messageGapRestriction.message : ""}
            >
              {isSending || isUploading ? (
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
              ) : (
                <Send className={`w-4 h-4 transition-transform duration-200 ${
                  (messageInput.trim() || selectedFile) ? "transform rotate-0" : ""
                }`} />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Edit Contact Modal */}
      {showEditModal && contact && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white w-full max-w-md rounded-xl p-6 mobile-bounce-in">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Contact</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Contact name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={editForm.notes}
                  onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add notes about this contact"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <input
                  type="text"
                  value={editForm.tags}
                  onChange={(e) => setEditForm(prev => ({ ...prev, tags: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="VIP, Regular, New (comma separated)"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={updateContact}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
