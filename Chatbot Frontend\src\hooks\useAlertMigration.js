import { useAlert } from "../contexts/AlertContext";

/**
 * Migration helper hook to replace inline Alert components with global alerts
 * This hook provides methods to show alerts that previously would have been inline
 */
export const useAlertMigration = () => {
  const { success, error, warning, info } = useAlert();

  const showError = (message, title = null) => {
    return error(message, { title, duration: 5000 });
  };

  const showSuccess = (message, title = null) => {
    return success(message, { title, duration: 5000 });
  };

  const showWarning = (message, title = null) => {
    return warning(message, { title, duration: 5000 });
  };

  const showInfo = (message, title = null) => {
    return info(message, { title, duration: 5000 });
  };

  return {
    showError,
    showSuccess,
    showWarning,
    showInfo,
  };
};
