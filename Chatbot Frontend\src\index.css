@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

/* CSS Custom Properties for consistent theming */
:root {
  /* Primary Blue Theme Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* UI Component Variables (Shadcn/UI) */
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  /* Ensure mobile viewport is properly handled */
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  /* Prevent horizontal scrolling on mobile */
  body {
    overflow-x: hidden;
  }

  /* Mobile-friendly touch targets */
  button, a, input, textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile chat optimizations */
  .mobile-chat-container {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  /* Mobile input optimizations */
  .mobile-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Mobile safe areas */
  .mobile-safe-top {
    padding-top: env(safe-area-inset-top);
  }
}

/* Tablet-specific styles */
@media (min-width: 769px) and (max-width: 1024px) {
  /* Tablet button optimizations */
  .btn-primary, .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    min-height: 42px;
  }

  /* Ensure buttons don't get too small on tablets */
  button {
    min-height: 40px;
    min-width: 40px;
  }

  /* Tablet-friendly spacing */
  .space-y-6 > * + * {
    margin-top: 1.25rem;
  }

  /* Tablet modal improvements */
  .fixed.inset-0 .bg-white.rounded-xl {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  /* Better touch targets for tablet */
  .grid.gap-3 {
    gap: 0.875rem;
  }

  /* Tablet-specific flex improvements */
  .flex.gap-2 {
    gap: 0.625rem;
  }

  .flex.gap-3 {
    gap: 0.875rem;
  }
}

  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Mobile animations */
  .mobile-slide-in {
    animation: slideInFromRight 0.3s ease-out;
  }

  .mobile-slide-out {
    animation: slideOutToRight 0.3s ease-in;
  }

  .mobile-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .mobile-bounce-in {
    animation: mobileBounceIn 0.3s ease-out;
  }

  /* Mobile alert animations */
  .mobile-alert-enter-up {
    animation: mobileAlertEnterUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .mobile-alert-enter-down {
    animation: mobileAlertEnterDown 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .mobile-alert-exit-up {
    animation: mobileAlertExitUp 0.3s ease-in;
  }

  .mobile-alert-exit-down {
    animation: mobileAlertExitDown 0.3s ease-in;
  }



  /* Message bubble animations */
  .message-bubble-in {
    animation: messageBubbleIn 0.3s ease-out;
  }

  /* Typing indicator animation */
  .typing-dots {
    animation: typingDots 1.4s infinite;
  }

  .typing-dots:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-dots:nth-child(3) {
    animation-delay: 0.4s;
  }

/* Mobile animation keyframes */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes mobileBounceIn {
  0% {
    transform: scale(0.9) translateY(20px);
    opacity: 0;
  }
  50% {
    transform: scale(1.02) translateY(-5px);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Mobile alert keyframes */
@keyframes mobileAlertEnterUp {
  0% {
    transform: translateY(100%) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes mobileAlertEnterDown {
  0% {
    transform: translateY(-100%) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes mobileAlertExitUp {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(-100%) scale(0.95);
    opacity: 0;
  }
}

@keyframes mobileAlertExitDown {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(100%) scale(0.95);
    opacity: 0;
  }
}



@keyframes messageBubbleIn {
  0% {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes typingDots {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Global Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family:
    "Inter",
    "SF Pro Display",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    "Roboto",
    "Oxygen",
    "Ubuntu",
    "Cantarell",
    "Fira Sans",
    "Droid Sans",
    "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #1f2937;
}

/* ===== CUSTOM BUTTON COMPONENTS ===== */

/* Primary action button - for main CTAs */
.btn-primary {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #2563eb;
}

.btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #1e40af);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

/* Secondary button - for secondary actions */
.btn-secondary {
  background: white;
  border: 2px solid #bfdbfe;
  color: #1d4ed8;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-secondary:hover {
  background: #eff6ff;
  border-color: #93c5fd;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* ===== FORM COMPONENTS ===== */

/* Consistent input field styling */
.input-field {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease;
  color: #374151;
}

.input-field::placeholder {
  color: #9ca3af;
}

.input-field:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* ===== CARD COMPONENTS ===== */

/* Standard card styling */
.card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Highlighted card for important content */
.card-highlight {
  background: linear-gradient(to bottom right, #eff6ff, #dbeafe);
  border: 2px solid #2563eb;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

/* ===== UTILITY CLASSES ===== */

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(to right, #2563eb, #1e40af);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Animation classes for smooth page entry */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== LOADING ANIMATIONS ===== */

/* Loading dots animation for chat interface */
.loading-dots {
  display: inline-block;
}

.loading-dots:after {
  content: "...";
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%,
  20% {
    color: rgba(0, 0, 0, 0);
    text-shadow:
      0.25em 0 0 rgba(0, 0, 0, 0),
      0.5em 0 0 rgba(0, 0, 0, 0);
  }
  40% {
    color: black;
    text-shadow:
      0.25em 0 0 rgba(0, 0, 0, 0),
      0.5em 0 0 rgba(0, 0, 0, 0);
  }
  60% {
    text-shadow:
      0.25em 0 0 black,
      0.5em 0 0 rgba(0, 0, 0, 0);
  }
  80%,
  100% {
    text-shadow:
      0.25em 0 0 black,
      0.5em 0 0 black;
  }
}

/* ===== SCROLLBAR STYLING ===== */

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ===== DARK MODE SUPPORT ===== */
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.205 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.205 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.205 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.205 0 0);
  --input: oklch(0.205 0 0);
  --ring: oklch(0.556 0 0);
  --sidebar: oklch(0.145 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.985 0 0);
  --sidebar-primary-foreground: oklch(0.205 0 0);
  --sidebar-accent: oklch(0.205 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.205 0 0);
  --sidebar-ring: oklch(0.556 0 0);
}

.dark * {
  border-color: #374151;
}

.dark body {
  background: #0f172a;
  color: #f8fafc;
}

/* ===== MODAL/DIALOG BACKDROP SUPPORT ===== */

/* Ensure html and body don't interfere with modal positioning */
html,
body {
  position: relative;
  /* Prevent layout shift from scrollbar appearing/disappearing */
  overflow-y: scroll;
  scrollbar-gutter: stable;
}

/* Ensure modal backdrop covers entire viewport */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* ===== SELECT DROPDOWN FIXES ===== */

/* Force stable layout for body and root */
body {
  overflow-x: hidden;
  width: 100%;
  position: relative;
}

#root {
  position: relative;
  overflow-x: hidden;
  width: 100%;
  min-height: 100vh;
}

/* Force Select content to position absolutely within viewport */
[data-radix-select-content] {
  position: fixed !important;
  z-index: 9999 !important;
  max-width: calc(100vw - 20px) !important;
  max-height: calc(100vh - 20px) !important;
}

/* Lock the trigger in position */
[data-radix-select-trigger] {
  position: relative !important;
  isolation: isolate !important;
}

/* Ensure portal doesn't interfere with page layout */
[data-radix-portal] {
  position: fixed !important;
  inset: 0 !important;
  pointer-events: none !important;
  z-index: 9999 !important;
}

/* Allow interaction with dropdown content */
[data-radix-portal] > * {
  pointer-events: auto !important;
}

/* Prevent scrollbar changes during dropdown */
html {
  scrollbar-gutter: stable;
}

/* Custom scrollbar for Select dropdown */
[data-radix-select-viewport] {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f8f9fa;
}

[data-radix-select-viewport]::-webkit-scrollbar {
  width: 6px;
}

[data-radix-select-viewport]::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

[data-radix-select-viewport]::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

[data-radix-select-viewport]::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Full screen modal backdrop */
.modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  overflow: hidden !important;
  /* Force it to be above everything */
  transform: translateZ(0) !important;
  will-change: transform !important;
}

/* Ensure the dialog portal is properly positioned */
[data-radix-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 9999 !important;
}

/* Disable body scroll when modal is open */
body.modal-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
}

/* Ensure root containers don't interfere */
#root,
[data-radix-portal] {
  position: relative !important;
}

/* Mobile browser fixes */
@supports (-webkit-touch-callout: none) {
  .modal-backdrop {
    height: -webkit-fill-available !important;
    min-height: -webkit-fill-available !important;
  }
}
