/**
 * Shared error handling utilities for frontend
 */

/**
 * Extract error message from API response
 * @param {Error} error - The error object from axios
 * @param {string} defaultMessage - Default error message
 * @returns {string} Error message
 */
export function getErrorMessage(error, defaultMessage = "An error occurred") {
  if (error.response?.data?.error) {
    return error.response.data.error;
  }

  if (error.response?.data?.message) {
    return error.response.data.message;
  }

  if (error.message) {
    return error.message;
  }

  return defaultMessage;
}

/**
 * Handle API errors with consistent error throwing
 * @param {Error} error - The error object from axios
 * @param {string} defaultMessage - Default error message
 * @throws {Error} Formatted error
 */
export function handleApiError(error, defaultMessage = "An error occurred") {
  const message = getErrorMessage(error, defaultMessage);
  throw new Error(message);
}

/**
 * Create a standardized API call wrapper with error handling
 * @param {Function} apiCall - The API call function
 * @param {string} errorMessage - Default error message
 * @returns {Function} Wrapped API call
 */
export function createApiWrapper(apiCall, errorMessage) {
  return async (...args) => {
    try {
      return await apiCall(...args);
    } catch (error) {
      handleApiError(error, errorMessage);
    }
  };
}

/**
 * Validate form data
 * @param {Object} data - Form data
 * @param {Array} requiredFields - Array of required field names
 * @throws {Error} If validation fails
 */
export function validateFormData(data, requiredFields) {
  const missingFields = requiredFields.filter(
    (field) => !data[field] || data[field].trim() === "",
  );

  if (missingFields.length > 0) {
    throw new Error(
      `Please fill in all required fields: ${missingFields.join(", ")}`,
    );
  }
}

/**
 * Handle authentication errors
 * @param {Error} error - The error object
 * @returns {boolean} True if it's an auth error
 */
export function isAuthError(error) {
  return (
    error.response?.status === 401 ||
    error.message?.includes("unauthorized") ||
    error.message?.includes("authentication")
  );
}
