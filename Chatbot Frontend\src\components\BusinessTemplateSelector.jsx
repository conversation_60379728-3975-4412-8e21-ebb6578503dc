import React, { useState } from 'react';
import { getAllTemplates, getTemplateByType } from '../data/businessTemplates';
import Icon from './ui/icon';

export default function BusinessTemplateSelector({ onTemplateSelect, onClose }) {
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const templates = getAllTemplates();

  const handleTemplateClick = (templateKey) => {
    const template = getTemplateByType(templateKey);
    setSelectedTemplate({ key: templateKey, ...template });
  };

  const handleApplyTemplate = () => {
    if (selectedTemplate) {
      onTemplateSelect(selectedTemplate);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Choose Business Template</h2>
            <p className="text-sm text-gray-600 mt-1">
              Quick setup with pre-configured prompts and settings for your business type
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Icon name="X" className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Template List */}
          <div className="w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
            <h3 className="font-semibold text-gray-900 mb-4">Business Types</h3>
            <div className="space-y-3">
              {templates.map((template) => (
                <button
                  key={template.key}
                  onClick={() => handleTemplateClick(template.key)}
                  className={`w-full text-left p-4 rounded-lg border transition-all ${
                    selectedTemplate?.key === template.key
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{template.icon}</span>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Template Preview */}
          <div className="w-1/2 p-6 overflow-y-auto">
            {selectedTemplate ? (
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-3xl">{selectedTemplate.icon}</span>
                  <div>
                    <h3 className="font-bold text-gray-900">{selectedTemplate.name}</h3>
                    <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
                  </div>
                </div>

                {/* System Prompt Preview */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">System Prompt</h4>
                  <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700 font-mono">
                    {selectedTemplate.systemPrompt}
                  </div>
                </div>

                {/* Order Prompt Preview */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Order Processing Prompt</h4>
                  <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700 font-mono">
                    {selectedTemplate.orderPrompt}
                  </div>
                </div>

                {/* Settings Preview */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Optimized Settings</h4>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-gray-600">Temperature:</span>
                        <span className="ml-2 font-medium">{selectedTemplate.settings.temperature}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Max Tokens:</span>
                        <span className="ml-2 font-medium">{selectedTemplate.settings.max_tokens}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Memory:</span>
                        <span className="ml-2 font-medium">{selectedTemplate.settings.chat_history_limit} messages</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Similarity:</span>
                        <span className="ml-2 font-medium">{selectedTemplate.settings.similarity_threshold}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sample Knowledge */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Sample Knowledge Base</h4>
                  <div className="space-y-2">
                    {selectedTemplate.sampleKnowledge.map((item, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <h5 className="font-medium text-gray-900 text-sm">{item.title}</h5>
                        <p className="text-xs text-gray-600 mt-1">{item.content}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Apply Button */}
                <button
                  onClick={handleApplyTemplate}
                  className="w-full btn-primary py-3 flex items-center justify-center gap-2"
                >
                  <Icon name="Check" className="w-4 h-4" />
                  Apply This Template
                </button>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <Icon name="MousePointer" className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>Select a business type to see the template preview</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
