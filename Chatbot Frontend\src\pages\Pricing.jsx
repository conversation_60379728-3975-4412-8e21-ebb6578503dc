import PricingCard from "../components/PricingCard";
import { Link } from "react-router-dom";
import { useState } from "react";
import { Check } from "lucide-react";

const plans = [
  {
    plan: "Kecil",
    monthlyPrice: "RM69",
    yearlyPrice: "RM58",
    period: "/month",
    monthlyOriginalPrice: "RM99",
    yearlyOriginalPrice: "RM82",
    description:
      "Perfect for small businesses just starting with AI automation",
    features: [
      "5,000 AI messages per month",
      "1,000 contacts",
      "50 knowledge base entries",
      "100 product catalog items",
      "GPT-4o Mini AI model",
      "WhatsApp integration",
      "Basic setup",
    ],
    color: "blue",
    buttonText: "Select Package",
  },
  {
    plan: "Popular",
    monthlyPrice: "RM199",
    yearlyPrice: "RM166",
    period: "/month",
    monthlyOriginalPrice: "RM299",
    yearlyOriginalPrice: "RM249",
    description: "Most popular plan for growing businesses with advanced AI",
    features: [
      "10,000 AI messages per month",
      "5,000 contacts",
      "200 knowledge base entries",
      "500 product catalog items",
      "GPT-4 Mini, GPT-4o, GPT-3.5 Turbo",
      "WhatsApp integration",
      "Advanced setup",
      "Priority support",
    ],
    highlight: true,
    color: "blue",
    buttonText: "Select Package",
    popular: true,
  },
  {
    plan: "Besar",
    monthlyPrice: "RM599",
    yearlyPrice: "RM499",
    period: "/month",
    monthlyOriginalPrice: "RM799",
    yearlyOriginalPrice: "RM666",
    description:
      "Enterprise solution for large businesses with unlimited scalability",
    features: [
      "50,000 AI messages per month",
      "10,000 contacts",
      "1,000 knowledge base entries",
      "2,000 product catalog items",
      "GPT-4 Mini, GPT-4o, GPT-3.5 Turbo",
      "WhatsApp integration",
      "Advanced setup",
      "Priority support",
    ],
    color: "blue",
    buttonText: "Select Package",
  },
];

const faqs = [
  {
    question: "How does the free trial work for my business?",
    answer:
      "You get full access to all features for 7 days but you will get limited usage per month. No need to give credit card details. Perfect time to test with your regular customers. Terms and conditions apply.",
  },
  {
    question: "Can I change package anytime?",
    answer:
      "Yes! If your business grows or you need fewer features, you can upgrade or downgrade. Changes take effect immediately.",
  },
  {
    question: "What happens if I exceed my message limit?",
    answer:
      "You will not be able to send messages if you exceed your message limit. You can upgrade to a higher plan to continue using the service or request to reset your message limit.",
  },
  {
    question: "Do you accept Malaysian payment methods?",
    answer:
      "Yes! We accept bank transfer and E-Wallet payment for larger businesses. All payments in Ringgit Malaysia.",
  },
  {
    question: "How does it take order from customers?",
    answer:
      "It will be integrated with WhatsApp. You can use WhatsApp to take order from customers, and the AI will automatically process the order and store the order in Google Sheet.",
  },
  {
    question:
      "Can I ask support team to help me with setup or add more knowledge base entries?",
    answer:
      "Yes! We offer support for setup and knowledge base entries with a cost of RM30 per request (Price might vary depending on the complexity of the request). You can contact us via WhatsApp.",
  },
];

export default function Pricing() {
  const [isYearly, setIsYearly] = useState(false);

  const getPlansWithCurrentPricing = () => {
    return plans.map((plan) => ({
      ...plan,
      price: isYearly ? plan.yearlyPrice : plan.monthlyPrice,
      originalPrice: isYearly
        ? plan.yearlyOriginalPrice
        : plan.monthlyOriginalPrice,
    }));
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
      {/* Header Section */}
      <section className="relative overflow-hidden py-20 lg:pt-16">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>

        <div className="relative container mx-auto px-6 text-center">
          <h1 className="text-5xl lg:text-6xl font-extrabold text-gray-900 mb-6 leading-tight">
            Choose Your
            <span className="gradient-text block lg:inline">
              {" "}
              Business Package
            </span>
          </h1>

          <p className="text-xl lg:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            Pricing designed for Malaysian businesses. Reasonable price for your
            business.
            <br />
            <span className="font-semibold text-blue-600">
              No contracts, cancel anytime.
            </span>
          </p>

          {/* Pricing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-16">
            <span
              className={`text-gray-600 font-semibold ${!isYearly ? "text-blue-600" : ""}`}
            >
              Monthly
            </span>
            <div className="relative">
              <input
                type="checkbox"
                className="sr-only"
                checked={isYearly}
                onChange={() => setIsYearly(!isYearly)}
              />
              <div
                className={`w-12 h-6 rounded-full shadow-inner cursor-pointer transition-colors ${
                  isYearly ? "bg-blue-600" : "bg-gray-300"
                }`}
                onClick={() => setIsYearly(!isYearly)}
              >
                <div
                  className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow transition-transform ${
                    isYearly ? "translate-x-6" : "translate-x-1"
                  }`}
                ></div>
              </div>
            </div>
            <span
              className={`text-gray-600 font-semibold ${isYearly ? "text-blue-600" : ""}`}
            >
              Yearly
            </span>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="container mx-auto px-6 pb-20">
        <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {getPlansWithCurrentPricing().map((plan, index) => (
            <PricingCard key={index} {...plan} />
          ))}
        </div>
      </section>

      {/* Features Comparison */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Compare All Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See exactly what's included in each package
            </p>
          </div>

          <div className="overflow-x-auto">
            <div className="card">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-blue-50 to-blue-100">
                  <tr>
                    <th className="text-left p-6 font-semibold text-gray-900">
                      Features
                    </th>
                    <th className="text-center p-6 font-semibold text-gray-900">
                      Kecil
                    </th>
                    <th className="text-center p-6 font-semibold text-gray-900 bg-blue-600 text-white">
                      Popular
                    </th>
                    <th className="text-center p-6 font-semibold text-gray-900">
                      Besar
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {[
                    {
                      feature: "AI Messages per Month",
                      small: "5,000",
                      popular: "10,000",
                      large: "50,000",
                    },
                    {
                      feature: "Contacts",
                      small: "1,000",
                      popular: "5,000",
                      large: "10,000",
                    },
                    {
                      feature: "Knowledge Base Entries",
                      small: "50",
                      popular: "200",
                      large: "1,000",
                    },
                    {
                      feature: "Product Catalog Items",
                      small: "100",
                      popular: "500",
                      large: "2,000",
                    },
                    {
                      feature: "AI Models",
                      small: "GPT-4o Mini",
                      popular: "GPT-4 Mini, GPT-4o, GPT-3.5 Turbo",
                      large: "GPT-4 Mini, GPT-4o, GPT-3.5 Turbo",
                    },
                    {
                      feature: "WhatsApp Integration",
                      small: "Yes",
                      popular: "Yes",
                      large: "Yes",
                    },
                    {
                      feature: "Setup",
                      small: "Basic",
                      popular: "Advanced",
                      large: "Advanced",
                    },
                    {
                      feature: "Priority Support",
                      small: "No",
                      popular: "Yes",
                      large: "Yes",
                    },
                  ].map((row, index) => (
                    <tr
                      key={index}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      <td className="p-6 font-medium text-gray-900">
                        {row.feature}
                      </td>
                      <td className="p-6 text-center text-gray-600">
                        {row.small === "Yes" ? (
                          <div className="flex justify-center">
                            <Check className="w-5 h-5 text-green-500" />
                          </div>
                        ) : (
                          row.small
                        )}
                      </td>
                      <td className="p-6 text-center text-gray-600 bg-blue-50">
                        {row.popular === "Yes" ? (
                          <div className="flex justify-center">
                            <Check className="w-5 h-5 text-green-500" />
                          </div>
                        ) : (
                          row.popular
                        )}
                      </td>
                      <td className="p-6 text-center text-gray-600">
                        {row.large === "Yes" ? (
                          <div className="flex justify-center">
                            <Check className="w-5 h-5 text-green-500" />
                          </div>
                        ) : (
                          row.large
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Common questions from Malaysian business owners like you
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div className="text-center mt-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to grow your business?
            </h3>
            <p className="text-gray-600 mb-8">
              Join thousands of Malaysian businesses already using Chilbee
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="btn-primary text-lg px-8 py-4">
                Get Started Now
              </Link>
              <Link to="/login" className="btn-secondary text-lg px-8 py-4">
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
