import { createContext, useContext, useState, useEffect } from "react";
import authService from "../services/auth";
import adminService from "../services/admin";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Impersonation state
  const [isImpersonating, setIsImpersonating] = useState(false);
  const [impersonationSession, setImpersonationSession] = useState(null);
  const [originalAdminUser, setOriginalAdminUser] = useState(null);

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          const userData = await authService.getCurrentUser();
          setUser(userData);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error("Auth check failed:", error.message);
        // Clear invalid token
        authService.signout();
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (email, password) => {
    const response = await authService.signin(email, password);
    // Get complete user data including profile/username after successful signin
    const userData = await authService.getCurrentUser();

    setUser(userData);
    setIsAuthenticated(true);
    return response;
  };

  // Register function
  const register = async (email, password, phone, name) => {
    const response = await authService.signup(email, password, phone, name);
    // Don't automatically log in user after registration
    // They need to verify their email first
    return response;
  };

  // Logout function
  const logout = async () => {
    try {
      await authService.signout();
    } catch (error) {
      console.error("Logout failed:", error.message);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  // Reset password function
  const resetPassword = async (email) => {
    return await authService.resetPassword(email);
  };

  // Complete password reset (using token from email)
  const forgotPassword = async (newPassword, accessToken, refreshToken) => {
    return await authService.forgotPassword(
      newPassword,
      accessToken,
      refreshToken,
    );
  };

  // Change password function
  const changePassword = async (currentPassword, newPassword) => {
    return await authService.changePassword(currentPassword, newPassword);
  };

  // Change display name function
  const changeDisplayName = async (newDisplayName) => {
    return await authService.changeDisplayName(newDisplayName);
  };

  // Update user data
  const updateUser = (userData) => {
    setUser(userData);
  };

  // Refresh user data from server
  const refreshUser = async () => {
    try {
      if (authService.isAuthenticated()) {
        const userData = await authService.getCurrentUser();
        setUser(userData);
        return userData;
      }
    } catch (error) {
      console.error("Failed to refresh user data:", error);
      // If refresh fails with auth error, logout user
      if (error.message.includes("401") || error.message.includes("token")) {
        await logout();
      }
      throw error;
    }
  };

  // Check if current user is admin
  const isAdmin = () => {
    const plan = user?.profile?.plan || user?.user?.plan;
    const role = user?.profile?.role || user?.user?.role;
    return plan === "admin" || role === "admin";
  };

  // Start impersonating a user (admin only)
  const startImpersonation = async (targetAuthId, notes = null) => {
    if (!isAdmin()) {
      throw new Error("Only admins can impersonate users");
    }

    try {
      // Store current admin user before impersonation
      setOriginalAdminUser(user);

      // Start impersonation on the backend
      const impersonationResponse = await adminService.startImpersonation(
        targetAuthId,
        notes,
      );
      const sessionId = impersonationResponse.session.id;

      // Get the impersonated user data
      const impersonatedUserResponse =
        await adminService.getImpersonatedUser(sessionId);
      const impersonatedUser = impersonatedUserResponse.user;

      // Update state
      setUser(impersonatedUser);
      setIsImpersonating(true);
      setImpersonationSession(impersonationResponse.session);

      return impersonationResponse;
    } catch (error) {
      console.error("Failed to start impersonation:", error);
      throw error;
    }
  };

  // Stop impersonating and return to admin user
  const stopImpersonation = async () => {
    if (!isImpersonating || !impersonationSession) {
      throw new Error("Not currently impersonating");
    }

    try {
      // Stop impersonation on the backend
      await adminService.stopImpersonation(impersonationSession.id);

      // Restore original admin user
      setUser(originalAdminUser);
      setIsImpersonating(false);
      setImpersonationSession(null);
      setOriginalAdminUser(null);

      return { success: true };
    } catch (error) {
      console.error("Failed to stop impersonation:", error);
      throw error;
    }
  };

  // Get impersonation status
  const getImpersonationStatus = async () => {
    if (!isAdmin()) {
      return { isImpersonating: false, activeSessions: [] };
    }

    try {
      return await adminService.getImpersonationStatus();
    } catch (error) {
      console.error("Failed to get impersonation status:", error);
      return { isImpersonating: false, activeSessions: [] };
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
    resetPassword,
    forgotPassword,
    changePassword,
    changeDisplayName,
    updateUser,
    refreshUser,
    isAdmin,
    // Impersonation methods and state
    isImpersonating,
    impersonationSession,
    originalAdminUser,
    startImpersonation,
    stopImpersonation,
    getImpersonationStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
