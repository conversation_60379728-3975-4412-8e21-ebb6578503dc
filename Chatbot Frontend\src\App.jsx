// React Router imports for navigation
import { BrowserRouter as Router, Routes, Route, useLocation } from "react-router-dom";

// Page imports - Auth related pages
import Login from "./pages/Login";
import Register from "./pages/Register";
import ResetPassword from "./pages/ResetPassword";
import NewPassword from "./pages/NewPassword";
import ResetPasswordComplete from "./pages/ResetPasswordComplete";
import ChangePassword from "./pages/ChangePassword";
import EmailVerification from "./pages/EmailVerification";
import EmailVerified from "./pages/EmailVerified";

// Page imports - Main app pages
import Home from "./pages/Home";
import Pricing from "./pages/Pricing";
import Profile from "./pages/Profile";
import Dashboard from "./pages/dashboard/Dashboard";
import Showcase from "./pages/Showcase";
import ContactUs from "./pages/ContactUs";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";

// Components and Context
import Navbar from "./components/Navbar";
import { AuthProvider } from "./contexts/AuthContext";
import { AlertProvider } from "./contexts/AlertContext";
import { AlertContainer } from "./components/AlertContainer";
import Footer from "./components/Footer";

/**
 * Main App Component
 * Handles routing and provides authentication context to the entire application
 */
function FooterRoutesWrapper() {
  const location = useLocation();
  const showFooter = [
    "/",
    "/pricing",
    "/contact",
    "/terms",
    "/privacy",
    "/showcase"
  ].includes(location.pathname);
  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-white">
        {/* Navigation bar - fixed at top */}
        <Navbar />
        {/* Alert Container - fixed at top-right */}
        <AlertContainer />
        {/* Main content area with top padding to account for fixed navbar */}
        <main className="pt-20">
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Home />} />
            <Route path="/pricing" element={<Pricing />} />
            {/* Authentication Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/new-password" element={<NewPassword />} />
            <Route path="/reset-password-complete" element={<ResetPasswordComplete />} />
            <Route path="/change-password" element={<ChangePassword />} />
            <Route path="/verify-email" element={<EmailVerification />} />
            <Route path="/email-verified" element={<EmailVerified />} />
            <Route path="/showcase" element={<Showcase />} />
            <Route path="/contact" element={<ContactUs />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            {/* Protected Routes */}
            <Route path="/profile" element={<Profile />} />
            <Route path="/dashboard/*" element={<Dashboard />} />
          </Routes>
        </main>
        {showFooter && <Footer />}
      </div>
    </>
  );
}

function App() {
  return (
    <AuthProvider>
      <AlertProvider>
        <Router>
          <FooterRoutesWrapper />
        </Router>
      </AlertProvider>
    </AuthProvider>
  );
}

export default App;
