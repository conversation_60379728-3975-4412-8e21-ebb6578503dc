// AI Service for handling all AI-related API calls
import { api } from "./api.js";

export const aiService = {
  // 1. AI Chat
  async chat(prompt, authId, phoneNumber) {
    try {
      const response = await api.post("/api/ai/chat", {
        prompt,
        authId,
        phoneNumber,
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to send message");
    }
  },

  // 2. Chat History
  async getChatHistory(authId, phoneNumber, limit = 50, offset = 0) {
    try {
      const response = await api.get("/api/ai/chat-history", {
        params: { authId, phoneNumber, limit, offset },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch chat history",
      );
    }
  },

  // 3. Subscription Status
  async getSubscriptionStatus(authId) {
    try {
      const response = await api.get("/api/ai/subscription-status", {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch subscription status",
      );
    }
  },

  // 4. Available Plans
  async getPlans() {
    try {
      const response = await api.get("/api/ai/plans");
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to fetch plans");
    }
  },

  // 5. Add Knowledge
  async addKnowledgeEntry(data) {
    try {
      const response = await api.post("/api/ai/add-knowledge", data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to add knowledge entry",
      );
    }
  },

  // 5a. Add Knowledge with Image
  async addKnowledgeEntryWithImage(formData) {
    try {
      const response = await api.post(
        "/api/ai/add-knowledge-with-image",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error ||
          "Failed to add knowledge entry with image",
      );
    }
  },

  // 6. Update Knowledge
  async updateKnowledgeEntry(data) {
    try {
      const response = await api.put("/api/ai/update-knowledge", data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update knowledge entry",
      );
    }
  },

  // 6a. Update Knowledge with Image
  async updateKnowledgeEntryWithImage(formData) {
    try {
      const response = await api.put(
        "/api/ai/update-knowledge-with-image",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error ||
          "Failed to update knowledge entry with image",
      );
    }
  },

  // 7. Delete Knowledge
  async deleteKnowledgeEntry(data) {
    try {
      const response = await api.delete("/api/ai/delete-knowledge", { data });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to delete knowledge entry",
      );
    }
  },

  // 8. List Knowledge
  async getKnowledgeBase(params = {}) {
    try {
      const response = await api.get("/api/ai/list-knowledge", { params });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch knowledge base",
      );
    }
  },

  // 8a. Send Knowledge Base Image
  async sendKnowledgeImage(data) {
    try {
      const response = await api.post("/api/ai/send-knowledge-image", data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to send knowledge base image",
      );
    }
  },

  // 9. Register Customer (WhatsApp)
  async registerCustomer(data) {
    try {
      const response = await api.post("/api/ai/register-customer", data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to register customer",
      );
    }
  },

  // 10. Get Customer Config
  async getCustomerConfig(authId) {
    try {
      const response = await api.get("/api/ai/customer-config", {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        // Customer config not found - this is expected for new users
        return {
          success: false,
          error: "Customer configuration not found",
          notFound: true,
        };
      }
      throw new Error(
        error.response?.data?.error || "Failed to fetch customer config",
      );
    }
  },

  // 11. Update Customer Config
  async updateCustomerConfig(data) {
    try {
      const response = await api.put("/api/ai/customer-config", data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update customer config",
      );
    }
  },

  // 12. Test WhatsApp
  async testWhatsApp(data) {
    try {
      const response = await api.post("/api/ai/test-whatsapp", data);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to test WhatsApp");
    }
  },

  // 13. Webhook Info
  async getWebhookInfo(authId) {
    try {
      const response = await api.get("/api/ai/webhook-info", {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch webhook info",
      );
    }
  },

  // 14. Deactivate Integration
  async deactivateIntegration(authId) {
    try {
      const response = await api.delete("/api/ai/customer-config", {
        data: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to deactivate integration",
      );
    }
  },

  // 17. Get Contacts
  async getContacts(params = {}) {
    try {
      const response = await api.get("/api/ai/contacts", { params });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch contacts",
      );
    }
  },

  // 18. Update Contact
  async updateContact(contactId, data) {
    try {
      const response = await api.put(`/api/ai/contacts/${contactId}`, data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update contact",
      );
    }
  },

  // 19. Get Orders
  async getOrders(params = {}) {
    try {
      const response = await api.get("/api/ai/orders", { params });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Failed to fetch orders");
    }
  },

  // 20. Get Order Details
  async getOrderDetails(orderId, authId) {
    try {
      const response = await api.get(`/api/ai/orders/${orderId}`, {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch order details",
      );
    }
  },

  // 21. Update Order Status
  async updateOrderStatus(orderId, data) {
    try {
      const response = await api.put(`/api/ai/orders/${orderId}/status`, data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update order status",
      );
    }
  },

  // 22. Daily Statistics
  async getDailyStatistics(params = {}) {
    try {
      const response = await api.get("/api/ai/statistics/daily", { params });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch daily statistics",
      );
    }
  },

  // 23. Monthly Statistics
  async getMonthlyStatistics(params = {}) {
    try {
      const response = await api.get("/api/ai/statistics/monthly", { params });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch monthly statistics",
      );
    }
  },

  // 24. Statistics Overview
  async getStatisticsOverview(authId) {
    try {
      const response = await api.get("/api/ai/statistics/overview", {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch statistics overview",
      );
    }
  },

  // 25. Message Statistics
  async getMessageStatistics(params = {}) {
    try {
      const response = await api.get("/api/ai/statistics/messages", { params });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch message statistics",
      );
    }
  },

  // 26. Get Chatbot Settings
  async getChatbotSettings(authId) {
    try {
      const response = await api.get("/api/ai/chatbot-settings", {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch chatbot settings",
      );
    }
  },

  // 27. Update Chatbot Settings
  async updateChatbotSettings(data) {
    try {
      const response = await api.put("/api/ai/chatbot-settings", data);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update chatbot settings",
      );
    }
  },

  // Get Quota Status
  async getQuotaStatus(authId) {
    try {
      const response = await api.get("/api/ai/quota-status", {
        params: { authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch quota status",
      );
    }
  },

  // Human Takeover Management
  async startHumanTakeover(authId, phoneNumber, timeoutMinutes = 30) {
    try {
      const response = await api.post("/api/ai/human-takeover/start", {
        authId,
        phoneNumber,
        timeoutMinutes,
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to start human takeover",
      );
    }
  },

  async endHumanTakeover(authId, phoneNumber) {
    try {
      const response = await api.post("/api/ai/human-takeover/end", {
        authId,
        phoneNumber,
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to end human takeover",
      );
    }
  },

  async getHumanTakeoverStatus(authId, phoneNumber) {
    try {
      const response = await api.get("/api/ai/human-takeover/status", {
        params: { authId, phoneNumber },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get human takeover status",
      );
    }
  },

  async updateHumanTakeoverActivity(authId, phoneNumber) {
    try {
      const response = await api.post("/api/ai/human-takeover/activity", {
        authId,
        phoneNumber,
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error ||
          "Failed to update human takeover activity",
      );
    }
  },

  async getHumanTakeoverSessions(authId, limit = 50, offset = 0) {
    try {
      const response = await api.get("/api/ai/human-takeover/sessions", {
        params: { authId, limit, offset },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get human takeover sessions",
      );
    }
  },

  // Social Media Integrations
  async getSocialMediaIntegrations(authId) {
    try {
      const response = await api.get("/api/ai/social-media/integrations", {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch social media integrations",
      );
    }
  },

  async registerMessengerIntegration(authId, config) {
    try {
      const response = await api.post("/api/ai/messenger/register", config, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to register Messenger integration",
      );
    }
  },

  async registerInstagramIntegration(authId, config) {
    try {
      const response = await api.post("/api/ai/instagram/register", config, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to register Instagram integration",
      );
    }
  },

  async togglePlatformIntegration(authId, platform, isEnabled) {
    try {
      const response = await api.post(`/api/ai/${platform}/toggle`, { isEnabled }, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to toggle ${platform} integration`,
      );
    }
  },

  // Google Integrations
  async getGoogleIntegrations(authId) {
    try {
      const response = await api.get("/api/ai/google/integrations", {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch Google integrations",
      );
    }
  },

  async getGoogleIntegration(authId, serviceType) {
    try {
      const response = await api.get(`/api/ai/google/${serviceType}`, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to fetch Google ${serviceType} integration`,
      );
    }
  },

  async saveGoogleSheetsIntegration(authId, config) {
    try {
      const response = await api.post("/api/ai/google/sheets", config, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      // Preserve the full error response for better error handling
      const errorObj = new Error(
        error.response?.data?.error || "Failed to save Google Sheets integration",
      );
      errorObj.response = error.response;
      throw errorObj;
    }
  },

  async saveGoogleCalendarIntegration(authId, config) {
    try {
      const response = await api.post("/api/ai/google/calendar", config, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to save Google Calendar integration",
      );
    }
  },

  async toggleGoogleIntegration(authId, serviceType, isEnabled) {
    try {
      const response = await api.post(`/api/ai/google/${serviceType}/toggle`, { isEnabled }, {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to toggle Google ${serviceType} integration`,
      );
    }
  },

  // WhatsApp Integration (separate from OAuth)
  async getWhatsAppStatus(authId) {
    try {
      const response = await api.get("/api/ai/whatsapp/status", {
        headers: { "x-auth-id": authId },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to fetch WhatsApp status",
      );
    }
  },
};

// Export as default for compatibility
export default aiService;
