import express from "express";
import supabase from "../utils/supabase.js";

const router = express.Router();

// Middleware to check admin access
const requireAdmin = (req, res, next) => {
  const { adminid, admintoken } = req.headers;
  if (!adminid || !admintoken || admintoken !== process.env.ADMIN_TOKEN) {
    return res.status(401).json({ error: "Admin access required" });
  }
  req.adminId = adminid;
  next();
};

// Helper function to log admin actions
async function logAdminAction(
  adminId,
  actionType,
  targetAuthId = null,
  actionDetails = {},
) {
  try {
    await supabase.from("admin_actions").insert({
      admin_id: adminId,
      action_type: actionType,
      target_auth_id: targetAuthId,
      action_details: actionDetails,
    });
  } catch (error) {
    console.error("Error logging admin action:", error);
  }
}

// Helper function to log subscription history
async function logSubscriptionHistory(
  authId,
  planName,
  startDate,
  endDate,
  actionType,
  adminId,
  notes,
) {
  try {
    await supabase.from("user_subscription_history").insert({
      auth_id: authId,
      plan_name: planName,
      start_date: startDate,
      end_date: endDate,
      action_type: actionType,
      admin_id: adminId,
      notes: notes,
    });
  } catch (historyError) {
    // Note: Could not log to subscription history table
  }
}

// ===== SUBSCRIPTION PLANS MANAGEMENT =====

// Get all available subscription plans
router.get("/plans", requireAdmin, async (req, res) => {
  try {
    const { data: plans, error } = await supabase
      .from("subscription_plans")
      .select("*")
      .order("price", { ascending: true });

    if (error) throw error;

    res.json({
      success: true,
      data: plans,
    });
  } catch (error) {
    console.error("Error getting plans:", error);
    res.status(500).json({ error: "Failed to get subscription plans" });
  }
});

// Update subscription plan limits (admin can modify existing plans)
router.put("/plans/:planName", requireAdmin, async (req, res) => {
  try {
    const { planName } = req.params;
    const updates = {};

    // Map request body to database columns
    if (req.body.description !== undefined)
      updates.description = req.body.description;
    if (req.body.price !== undefined) updates.price = req.body.price;
    if (req.body.durationDays !== undefined)
      updates.duration_days = req.body.durationDays;
    if (req.body.monthlyMessageLimit !== undefined)
      updates.monthly_message_limit = req.body.monthlyMessageLimit;
    if (req.body.totalContactLimit !== undefined)
      updates.total_contact_limit = req.body.totalContactLimit;
    if (req.body.knowledgeBaseLimit !== undefined)
      updates.knowledge_base_limit = req.body.knowledgeBaseLimit;
    if (req.body.productCatalogLimit !== undefined)
      updates.product_catalog_limit = req.body.productCatalogLimit;
    if (req.body.features !== undefined) updates.features = req.body.features;

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ error: "No fields to update" });
    }

    const { data: plan, error } = await supabase
      .from("subscription_plans")
      .update(updates)
      .eq("name", planName)
      .select()
      .single();

    if (error) throw error;

    if (!plan) {
      return res.status(404).json({ error: "Subscription plan not found" });
    }

    await logAdminAction(req.adminId, "plan_update", null, {
      plan_name: planName,
      updates,
    });

    res.json({
      success: true,
      message: "Subscription plan updated successfully",
      data: plan,
    });
  } catch (error) {
    console.error("Error updating plan:", error);
    res.status(500).json({ error: "Failed to update subscription plan" });
  }
});

// ===== USER SUBSCRIPTIONS MANAGEMENT =====

// Get user subscriptions with filters
router.get("/users", requireAdmin, async (req, res) => {
  try {
    const {
      authId,
      plan,
      page = 1,
      limit = 50,
      sortBy = "created_at",
      sortOrder = "desc",
    } = req.query;

    let query = supabase
      .from("profiles")
      .select(
        "auth_id, plan, created_at, display_name, email, phone, role, is_suspended, subscription_start_date, subscription_end_date",
        { count: "exact" },
      );

    // Apply filters
    if (authId) query = query.eq("auth_id", authId);
    if (plan) query = query.eq("plan", plan);

    // Apply sorting
    const validSortFields = ["created_at", "auth_id", "plan", "display_name"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    query = query.order(sortField, { ascending: sortOrder === "asc" });

    // Apply pagination
    const pageLimit = Math.min(parseInt(limit), 100);
    const offset = (parseInt(page) - 1) * pageLimit;
    query = query.range(offset, offset + pageLimit - 1);

    const { data: users, error, count } = await query;

    if (error) throw error;

    // Enhance data with plan details and subscription status
    const now = new Date();
    const enhancedUsers = users.map((user) => {
      const endDate = user.subscription_end_date
        ? new Date(user.subscription_end_date)
        : null;
      const isExpired = endDate ? endDate < now : false;
      const daysRemaining = endDate
        ? Math.max(0, Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)))
        : null;

      return {
        ...user,
        is_trial: user.plan === "trial",
        is_expired: isExpired,
        days_remaining:
          user.plan === "free" || user.plan === "admin" ? null : daysRemaining,
      };
    });

    res.json({
      success: true,
      data: enhancedUsers,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: pageLimit,
        totalPages: Math.ceil(count / pageLimit),
      },
    });
  } catch (error) {
    console.error("Error getting users:", error);
    res.status(500).json({ error: "Failed to get user subscriptions" });
  }
});

// Update user subscription (simplified)
router.put("/users/:authId/subscription", requireAdmin, async (req, res) => {
  try {
    const { authId } = req.params;
    const { plan } = req.body;

    // Validate plan
    const validPlans = ["free", "trial", "kecil", "popular", "besar", "admin"];
    if (plan && !validPlans.includes(plan)) {
      return res
        .status(400)
        .json({
          error: "Invalid plan. Must be one of: " + validPlans.join(", "),
        });
    }

    // Get current user data
    const { data: currentUser, error: userError } = await supabase
      .from("profiles")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (userError) {
      return res.status(404).json({ error: "User not found" });
    }

    if (!plan) {
      return res.status(400).json({ error: "Plan is required" });
    }

    const now = new Date().toISOString();
    const updates = {
      plan: plan,
      updated_at: now,
    };

    // Set subscription dates based on plan
    if (plan === "trial") {
      updates.subscription_start_date = now;
      updates.subscription_end_date = new Date(
        Date.now() + 7 * 24 * 60 * 60 * 1000,
      ).toISOString(); // 7 days
    } else if (plan === "free" || plan === "admin") {
      updates.subscription_start_date = now;
      updates.subscription_end_date = null; // Unlimited
    } else if (["kecil", "popular", "besar"].includes(plan)) {
      updates.subscription_start_date = now;
      updates.subscription_end_date = new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000,
      ).toISOString(); // 30 days
    }

    // Update user profile
    const { data: updatedUser, error: updateError } = await supabase
      .from("profiles")
      .update(updates)
      .eq("auth_id", authId)
      .select()
      .single();

    if (updateError) throw updateError;

    // Log the action in history
    await logSubscriptionHistory(
      authId,
      plan,
      new Date().toISOString(),
      null,
      "admin_update",
      req.adminId,
      `Admin updated plan to: ${plan}`,
    );

    await logAdminAction(req.adminId, "subscription_update", authId, {
      old_plan: currentUser.plan,
      new_plan: plan,
    });

    res.json({
      success: true,
      message: "User subscription updated successfully",
      data: updatedUser,
    });
  } catch (error) {
    console.error("Error updating user subscription:", error);
    res.status(500).json({ error: "Failed to update user subscription" });
  }
});

// Subscription dates are now tracked in subscription_start_date and subscription_end_date
// Use the subscription update endpoints to change plans and dates

// Set user subscription plan (simplified)
router.post(
  "/users/:authId/set-subscription",
  requireAdmin,
  async (req, res) => {
    try {
      const { authId } = req.params;
      const { plan, notes } = req.body;

      // Validate plan
      const validPlans = [
        "free",
        "trial",
        "kecil",
        "popular",
        "besar",
        "admin",
      ];
      if (!validPlans.includes(plan)) {
        return res
          .status(400)
          .json({
            error: "Invalid plan. Must be one of: " + validPlans.join(", "),
          });
      }

      // Get current user data
      const { data: currentUser, error: userError } = await supabase
        .from("profiles")
        .select("*")
        .eq("auth_id", authId)
        .single();

      if (userError) {
        return res.status(404).json({ error: "User not found" });
      }

      const now = new Date().toISOString();
      const updates = {
        plan: plan,
        updated_at: now,
      };

      // Set subscription dates based on plan
      if (plan === "trial") {
        updates.subscription_start_date = now;
        updates.subscription_end_date = new Date(
          Date.now() + 7 * 24 * 60 * 60 * 1000,
        ).toISOString(); // 7 days
      } else if (plan === "free" || plan === "admin") {
        updates.subscription_start_date = now;
        updates.subscription_end_date = null; // Unlimited
      } else if (["kecil", "popular", "besar"].includes(plan)) {
        updates.subscription_start_date = now;
        updates.subscription_end_date = new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000,
        ).toISOString(); // 30 days
      }

      // Update user profile
      const { data: updatedUser, error: updateError } = await supabase
        .from("profiles")
        .update(updates)
        .eq("auth_id", authId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Log subscription change history
      await logSubscriptionHistory(
        authId,
        plan,
        new Date().toISOString(),
        null,
        "admin_subscription",
        req.adminId,
        notes || `Admin set subscription to ${plan}`,
      );

      await logAdminAction(req.adminId, "subscription_set", authId, {
        old_plan: currentUser.plan,
        new_plan: plan,
        notes,
      });

      res.json({
        success: true,
        message: `User subscription set to ${plan}`,
        data: {
          authId: authId,
          oldPlan: currentUser.plan,
          newPlan: plan,
        },
      });
    } catch (error) {
      console.error("Error setting user subscription:", error);
      res.status(500).json({ error: "Failed to set user subscription" });
    }
  },
);

// Cancel user subscription (simplified - set to free plan)
router.post(
  "/users/:authId/cancel-subscription",
  requireAdmin,
  async (req, res) => {
    try {
      const { authId } = req.params;
      const { reason } = req.body;

      // Get current user data
      const { data: currentUser, error: userError } = await supabase
        .from("profiles")
        .select("*")
        .eq("auth_id", authId)
        .single();

      if (userError) {
        return res.status(404).json({ error: "User not found" });
      }

      if (currentUser.plan === "free") {
        return res.status(400).json({ error: "User is already on free plan" });
      }

      const now = new Date().toISOString();
      const updates = {
        plan: "free",
        subscription_start_date: now,
        subscription_end_date: null, // Free plan is unlimited
        updated_at: now,
      };

      // Update user profile
      const { data: updatedUser, error: updateError } = await supabase
        .from("profiles")
        .update(updates)
        .eq("auth_id", authId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Log cancellation
      await logSubscriptionHistory(
        authId,
        currentUser.plan,
        new Date().toISOString(),
        new Date().toISOString(),
        "cancelled",
        req.adminId,
        reason || "Admin cancelled subscription",
      );

      await logAdminAction(req.adminId, "subscription_cancel", authId, {
        cancelled_plan: currentUser.plan,
        reason: reason || null,
      });

      res.json({
        success: true,
        message: "Subscription cancelled - user moved to free plan",
        data: {
          authId: authId,
          oldPlan: currentUser.plan,
          newPlan: "free",
        },
      });
    } catch (error) {
      console.error("Error cancelling user subscription:", error);
      res.status(500).json({ error: "Failed to cancel user subscription" });
    }
  },
);

// Extend user subscription
router.post(
  "/users/:authId/extend-subscription",
  requireAdmin,
  async (req, res) => {
    try {
      const { authId } = req.params;
      const { months = 1, days = 0, notes } = req.body;

      // Get current user data
      const { data: currentUser, error: userError } = await supabase
        .from("profiles")
        .select("*")
        .eq("auth_id", authId)
        .single();

      if (userError) {
        return res.status(404).json({ error: "User not found" });
      }

      // Check if user has a paid plan that can be extended
      const extendablePlans = ["trial", "kecil", "popular", "besar"];
      if (!extendablePlans.includes(currentUser.plan)) {
        return res.status(400).json({
          error: `Cannot extend ${currentUser.plan} plan. Only trial, kecil, popular, and besar plans can be extended.`,
        });
      }

      // Calculate extension/deduction period (allow negative values)
      const extensionMonths = parseInt(months) || 0;
      const extensionDays = parseInt(days) || 0;

      if (extensionMonths === 0 && extensionDays === 0) {
        return res
          .status(400)
          .json({ error: "Months and days cannot both be 0" });
      }

      // Get current end date or use current time if expired/null
      const now = new Date();
      let baseDate = currentUser.subscription_end_date
        ? new Date(currentUser.subscription_end_date)
        : now;

      // For deductions, use the actual end date even if expired
      // For extensions, if subscription is already expired, extend from current time
      if (baseDate < now && (extensionMonths > 0 || extensionDays > 0)) {
        baseDate = now;
      }

      // Calculate new end date
      const newEndDate = new Date(baseDate);
      if (extensionMonths !== 0) {
        newEndDate.setMonth(newEndDate.getMonth() + extensionMonths);
      }
      if (extensionDays !== 0) {
        newEndDate.setDate(newEndDate.getDate() + extensionDays);
      }

      // Check if subscription should be downgraded to free plan
      const shouldDowngrade = newEndDate <= now;

      let updates = {
        updated_at: now.toISOString(),
      };

      if (shouldDowngrade) {
        // Downgrade to free plan
        updates.plan = "free";
        updates.subscription_start_date = now.toISOString();
        updates.subscription_end_date = null; // Free plan has no end date
      } else {
        // Update subscription end date
        updates.subscription_end_date = newEndDate.toISOString();

        // If subscription was expired and we're extending, also update start date
        if (
          !currentUser.subscription_end_date ||
          new Date(currentUser.subscription_end_date) < now
        ) {
          if (extensionMonths > 0 || extensionDays > 0) {
            updates.subscription_start_date = now.toISOString();
          }
        }
      }

      const { data: updatedUser, error: updateError } = await supabase
        .from("profiles")
        .update(updates)
        .eq("auth_id", authId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Calculate total extension/deduction in days for display
      const totalExtensionDays = Math.ceil(
        (newEndDate - baseDate) / (1000 * 60 * 60 * 24),
      );
      const isExtension = extensionMonths > 0 || extensionDays > 0;
      const actionType = shouldDowngrade
        ? "admin_downgrade"
        : isExtension
          ? "admin_extend"
          : "admin_deduct";

      // Log subscription change in history
      await logSubscriptionHistory(
        authId,
        shouldDowngrade ? "free" : currentUser.plan,
        updates.subscription_start_date || currentUser.subscription_start_date,
        shouldDowngrade ? null : newEndDate.toISOString(),
        actionType,
        req.adminId,
        notes ||
          (shouldDowngrade
            ? `Downgraded to free plan due to deduction of ${Math.abs(extensionMonths)} month(s) and ${Math.abs(extensionDays)} day(s)`
            : `${isExtension ? "Extended" : "Deducted"} by ${Math.abs(extensionMonths)} month(s) and ${Math.abs(extensionDays)} day(s)`),
      );

      // Log admin action
      await logAdminAction(
        req.adminId,
        shouldDowngrade ? "subscription_downgrade" : "subscription_modify",
        authId,
        {
          old_plan: currentUser.plan,
          new_plan: shouldDowngrade ? "free" : currentUser.plan,
          old_end_date: currentUser.subscription_end_date,
          new_end_date: shouldDowngrade ? null : newEndDate.toISOString(),
          extension_months: extensionMonths,
          extension_days: extensionDays,
          total_extension_days: totalExtensionDays,
          downgraded: shouldDowngrade,
          notes,
        },
      );

      // Prepare response message
      let message;
      if (shouldDowngrade) {
        message = `Subscription deducted and user downgraded to free plan`;
      } else if (isExtension) {
        message = `Subscription extended by ${extensionMonths} month(s) and ${extensionDays} day(s)`;
      } else {
        message = `Subscription deducted by ${Math.abs(extensionMonths)} month(s) and ${Math.abs(extensionDays)} day(s)`;
      }

      res.json({
        success: true,
        message: message,
        data: {
          authId: authId,
          oldPlan: currentUser.plan,
          newPlan: shouldDowngrade ? "free" : currentUser.plan,
          oldEndDate: currentUser.subscription_end_date,
          newEndDate: shouldDowngrade ? null : newEndDate.toISOString(),
          extensionDays: totalExtensionDays,
          extensionMonths: extensionMonths,
          extensionDaysOnly: extensionDays,
          downgraded: shouldDowngrade,
        },
      });
    } catch (error) {
      console.error("Error extending user subscription:", error);
      res.status(500).json({ error: "Failed to extend user subscription" });
    }
  },
);

// ===== QUOTA MANAGEMENT =====

// Get user quotas and usage
router.get("/users/:authId/quotas", requireAdmin, async (req, res) => {
  try {
    const { authId } = req.params;

    // Get quota information
    const { data: quotaInfo, error: quotaError } = await supabase.rpc(
      "check_user_quotas",
      { p_auth_id: authId },
    );

    if (quotaError) throw quotaError;

    // Get subscription details
    const { data: subscription, error: subError } = await supabase.rpc(
      "get_current_user_subscription",
      { p_auth_id: authId },
    );

    if (subError) throw subError;

    // Get current month usage
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();

    const { data: monthlyUsage, error: usageError } = await supabase
      .from("monthly_usage")
      .select("*")
      .eq("auth_id", authId)
      .eq("year", currentYear)
      .eq("month", currentMonth)
      .single();

    if (usageError && usageError.code !== "PGRST116") throw usageError;

    res.json({
      success: true,
      data: {
        subscription: subscription[0] || null,
        quotas: quotaInfo[0] || null,
        monthlyUsage: monthlyUsage || {
          messages_sent: 0,
          messages_received: 0,
          total_contacts: 0,
          new_contacts: 0,
          knowledge_base_queries: 0,
        },
      },
    });
  } catch (error) {
    console.error("Error getting user quotas:", error);
    res.status(500).json({ error: "Failed to get user quotas" });
  }
});

// Reset user monthly usage
router.post("/users/:authId/reset-usage", requireAdmin, async (req, res) => {
  try {
    const { authId } = req.params;
    const { year, month } = req.body;

    const targetYear = year || new Date().getFullYear();
    const targetMonth = month || new Date().getMonth() + 1;

    const { error } = await supabase.from("monthly_usage").upsert({
      auth_id: authId,
      year: targetYear,
      month: targetMonth,
      messages_sent: 0,
      messages_received: 0,
      total_contacts: 0,
      new_contacts: 0,
      knowledge_base_queries: 0,
    });

    if (error) throw error;

    await logAdminAction(req.adminId, "usage_reset", authId, {
      year: targetYear,
      month: targetMonth,
    });

    res.json({
      success: true,
      message: "Monthly usage reset successfully",
    });
  } catch (error) {
    console.error("Error resetting usage:", error);
    res.status(500).json({ error: "Failed to reset monthly usage" });
  }
});

// ===== SYSTEM MAINTENANCE =====

// Manually expire overdue subscriptions and reset to free plan
router.post("/maintenance/expire-overdue", requireAdmin, async (req, res) => {
  try {
    const now = new Date();
    const currentISO = now.toISOString();

    // Find all users with expired subscriptions (excluding free and admin plans)
    const { data: overdueUsers, error: findError } = await supabase
      .from("profiles")
      .select("auth_id, plan, subscription_end_date, display_name, email")
      .not("plan", "in", '("free","admin")')
      .not("subscription_end_date", "is", null)
      .lt("subscription_end_date", currentISO);

    if (findError) throw findError;

    if (!overdueUsers || overdueUsers.length === 0) {
      return res.json({
        success: true,
        message: "No overdue subscriptions found",
        expiredCount: 0,
        users: [],
      });
    }

    const expiredUsers = [];
    const errors = [];

    // Process each overdue user
    for (const user of overdueUsers) {
      try {
        // Update user to free plan
        const { data: updatedUser, error: updateError } = await supabase
          .from("profiles")
          .update({
            plan: "free",
            subscription_start_date: currentISO,
            subscription_end_date: null, // Free plan is unlimited
            updated_at: currentISO,
          })
          .eq("auth_id", user.auth_id)
          .select()
          .single();

        if (updateError) throw updateError;

        // Log subscription history
        await logSubscriptionHistory(
          user.auth_id,
          user.plan,
          user.subscription_end_date, // Old end date
          currentISO, // New end date (cancelled)
          "expired",
          req.adminId,
          `Subscription expired and reset to free plan - was ${user.plan}`,
        );

        // Log admin action
        await logAdminAction(req.adminId, "subscription_expire", user.auth_id, {
          old_plan: user.plan,
          new_plan: "free",
          expired_date: user.subscription_end_date,
          processed_date: currentISO,
        });

        expiredUsers.push({
          authId: user.auth_id,
          oldPlan: user.plan,
          expiredDate: user.subscription_end_date,
          displayName: user.display_name,
          email: user.email,
        });
      } catch (userError) {
        console.error(`Error processing user ${user.auth_id}:`, userError);
        errors.push({
          authId: user.auth_id,
          error: userError.message,
        });
      }
    }

    // Log overall maintenance action
    await logAdminAction(req.adminId, "overdue_expiry_maintenance", null, {
      total_found: overdueUsers.length,
      successfully_expired: expiredUsers.length,
      errors: errors.length,
    });

    res.json({
      success: true,
      message: `Processed ${overdueUsers.length} overdue subscriptions. ${expiredUsers.length} users reset to free plan.`,
      expiredCount: expiredUsers.length,
      totalFound: overdueUsers.length,
      users: expiredUsers,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Error running overdue expiry:", error);
    res.status(500).json({ error: "Failed to expire overdue subscriptions" });
  }
});

// Get system statistics
router.get("/stats", requireAdmin, async (req, res) => {
  try {
    // Get plan statistics from profiles
    const { data: planStats, error: planError } = await supabase
      .from("profiles")
      .select("plan")
      .not("plan", "is", null);

    if (planError) throw planError;

    // Count users by plan
    const planCounts = {};
    planStats.forEach((user) => {
      planCounts[user.plan] = (planCounts[user.plan] || 0) + 1;
    });

    // Get quota violations today
    const today = new Date().toISOString().split("T")[0];
    const { count: todayViolations } = await supabase
      .from("quota_violations")
      .select("*", { count: "exact", head: true })
      .gte("created_at", today);

    // Get total active users
    const { count: totalUsers } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true });

    // Get total contacts across all users
    const { count: totalContacts } = await supabase
      .from("contacts")
      .select("*", { count: "exact", head: true });

    // Get total message statistics
    const { data: messageStats, error: messageError } = await supabase
      .from("message_statistics")
      .select("message_type, total_tokens")
      .not("total_tokens", "is", null);

    if (messageError) throw messageError;

    // Calculate message and token totals
    let totalMessages = 0;
    let totalTokens = 0;
    let incomingMessages = 0;
    let outgoingMessages = 0;

    messageStats.forEach((stat) => {
      totalMessages++;
      totalTokens += stat.total_tokens || 0;
      if (stat.message_type === "incoming") {
        incomingMessages++;
      } else if (stat.message_type === "outgoing") {
        outgoingMessages++;
      }
    });

    // Get messages from the last 30 days for growth metrics
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { count: recentMessages } = await supabase
      .from("message_statistics")
      .select("*", { count: "exact", head: true })
      .gte("created_at", thirtyDaysAgo.toISOString());

    // Get WhatsApp integration count
    const { count: activeIntegrations } = await supabase
      .from("whatsapp_customers")
      .select("*", { count: "exact", head: true })
      .eq("is_active", true);

    // Get knowledge base entries count
    const { count: totalKnowledgeEntries } = await supabase
      .from("knowledge_base")
      .select("*", { count: "exact", head: true });

    res.json({
      success: true,
      data: {
        planStatistics: Object.entries(planCounts).map(([plan, count]) => ({
          plan,
          activeUsers: count,
        })),
        overview: {
          totalUsers: totalUsers || 0,
          totalContacts: totalContacts || 0,
          totalMessages: totalMessages,
          totalTokens: totalTokens,
          activeIntegrations: activeIntegrations || 0,
          totalKnowledgeEntries: totalKnowledgeEntries || 0,
        },
        messageBreakdown: {
          incomingMessages: incomingMessages,
          outgoingMessages: outgoingMessages,
          recentMessages: recentMessages || 0, // Last 30 days
          averageTokensPerMessage:
            totalMessages > 0 ? Math.round(totalTokens / totalMessages) : 0,
        },
        alerts: {
          expiredSubscriptions: 0,
          todayQuotaViolations: todayViolations || 0,
        },
        lastUpdated: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error getting system stats:", error);
    res.status(500).json({ error: "Failed to get system statistics" });
  }
});

// Get subscription history
router.get("/users/:authId/history", requireAdmin, async (req, res) => {
  try {
    const { authId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const pageLimit = Math.min(parseInt(limit), 100);
    const offset = (parseInt(page) - 1) * pageLimit;

    const {
      data: history,
      error,
      count,
    } = await supabase
      .from("user_subscription_history")
      .select("*", { count: "exact" })
      .eq("auth_id", authId)
      .order("created_at", { ascending: false })
      .range(offset, offset + pageLimit - 1);

    if (error) throw error;

    res.json({
      success: true,
      data: history,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: pageLimit,
        totalPages: Math.ceil(count / pageLimit),
      },
    });
  } catch (error) {
    console.error("Error getting subscription history:", error);
    res.status(500).json({ error: "Failed to get subscription history" });
  }
});

// Get admin action logs
router.get("/logs", requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      actionType,
      targetAuthId,
      adminId: filterAdminId,
    } = req.query;

    let query = supabase.from("admin_actions").select("*", { count: "exact" });

    if (actionType) query = query.eq("action_type", actionType);
    if (targetAuthId) query = query.eq("target_auth_id", targetAuthId);
    if (filterAdminId) query = query.eq("admin_id", filterAdminId);

    query = query.order("created_at", { ascending: false });

    const pageLimit = Math.min(parseInt(limit), 100);
    const offset = (parseInt(page) - 1) * pageLimit;
    query = query.range(offset, offset + pageLimit - 1);

    const { data: logs, error, count } = await query;

    if (error) throw error;

    res.json({
      success: true,
      data: logs,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: pageLimit,
        totalPages: Math.ceil(count / pageLimit),
      },
    });
  } catch (error) {
    console.error("Error getting admin logs:", error);
    res.status(500).json({ error: "Failed to get admin action logs" });
  }
});

// ===== WHATSAPP SYSTEM MANAGEMENT =====

// Refresh WhatsApp access token (system-wide)
router.post("/refresh-whatsapp-token", requireAdmin, async (req, res) => {
  try {
    const { appId, appSecret } = req.body;

    if (!appId || !appSecret) {
      return res.status(400).json({
        error: "Missing required fields: appId, appSecret",
      });
    }

    const newToken = await refreshWhatsAppAccessToken(appId, appSecret);

    if (newToken) {
      await logAdminAction(req.adminId, "whatsapp_token_refresh", null, {
        app_id: appId,
        token_preview: newToken.substring(0, 10) + "...",
      });

      res.json({
        success: true,
        message: "Access token refreshed successfully",
        // Don't return the actual token for security
        tokenPreview: newToken.substring(0, 10) + "...",
      });
    } else {
      res.status(400).json({ error: "Failed to refresh access token" });
    }
  } catch (error) {
    console.error("Error refreshing WhatsApp token:", error);
    res.status(500).json({ error: "Failed to refresh WhatsApp access token" });
  }
});

// Get WhatsApp system status and configuration
router.get("/whatsapp-status", requireAdmin, async (req, res) => {
  try {
    // Get total number of registered WhatsApp customers
    const { count: totalCustomers } = await supabase
      .from("whatsapp_customers")
      .select("*", { count: "exact", head: true })
      .eq("is_active", true);

    // Get recent WhatsApp activity from message statistics
    const { data: recentActivity, error: activityError } = await supabase
      .from("message_statistics")
      .select("created_at, auth_id")
      .eq("message_type", "outgoing")
      .gte(
        "created_at",
        new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      ) // Last 24 hours
      .order("created_at", { ascending: false })
      .limit(100);

    if (activityError) throw activityError;

    // Check if system token is configured
    const systemToken =
      process.env.SYSTEM_ACCESS_TOKEN || process.env.WHATSAPP_ACCESS_TOKEN;
    const tokenConfigured = !!systemToken;

    // Get verification token status
    const verifyToken = process.env.VERIFY_TOKEN;
    const verifyTokenConfigured = !!verifyToken;

    res.json({
      success: true,
      data: {
        systemStatus: {
          tokenConfigured,
          verifyTokenConfigured,
          tokenPreview: systemToken
            ? systemToken.substring(0, 10) + "..."
            : null,
        },
        customers: {
          totalActive: totalCustomers || 0,
        },
        recentActivity: {
          messagesLast24h: recentActivity?.length || 0,
          uniqueUsersLast24h: new Set(
            recentActivity?.map((a) => a.auth_id) || [],
          ).size,
        },
      },
    });
  } catch (error) {
    console.error("Error getting WhatsApp status:", error);
    res.status(500).json({ error: "Failed to get WhatsApp system status" });
  }
});

// Helper function to refresh WhatsApp access token
async function refreshWhatsAppAccessToken(appId, appSecret) {
  try {
    const axios = (await import("axios")).default;
    const response = await axios.get(
      `https://graph.facebook.com/v18.0/oauth/access_token`,
      {
        params: {
          grant_type: "client_credentials",
          client_id: appId,
          client_secret: appSecret,
        },
      },
    );

    if (response.data.access_token) {
      // Note: In production, you would want to save this to your environment or database
      // For now, we'll just return it for manual update
      return response.data.access_token;
    }

    return null;
  } catch (error) {
    console.error("Error refreshing WhatsApp access token:", error);
    return null;
  }
}

// Get user statistics
router.get("/users/:authId/stats", requireAdmin, async (req, res) => {
  try {
    const { authId } = req.params;
    const { startDate, endDate } = req.query;

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (profileError) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get total contacts
    const { count: totalContacts } = await supabase
      .from("contacts")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", authId);

    // Get total knowledge base entries
    const { count: totalKnowledgeEntries } = await supabase
      .from("knowledge_base")
      .select("*", { count: "exact", head: true })
      .eq("auth_id", authId);

    // Get message statistics
    let messageQuery = supabase
      .from("message_statistics")
      .select("message_type, total_tokens, created_at")
      .eq("auth_id", authId);

    if (startDate) {
      messageQuery = messageQuery.gte("created_at", startDate);
    }
    if (endDate) {
      messageQuery = messageQuery.lte("created_at", endDate);
    }

    const { data: messages, error: messageError } = await messageQuery;

    if (messageError) throw messageError;

    // Calculate message statistics
    const messageStats = {
      total: messages.length,
      incoming: messages.filter(m => m.message_type === "incoming").length,
      outgoing: messages.filter(m => m.message_type === "outgoing").length,
      totalTokens: messages.reduce((sum, m) => sum + (m.total_tokens || 0), 0),
    };

    // Get current month's usage
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const { data: monthlyUsage, error: usageError } = await supabase
      .from("monthly_usage")
      .select("*")
      .eq("auth_id", authId)
      .eq("year", currentYear)
      .eq("month", currentMonth)
      .single();

    // Get current month's total tokens
    const { data: monthlyTokens, error: tokensError } = await supabase
      .from("message_statistics")
      .select("total_tokens")
      .eq("auth_id", authId)
      .gte("created_at", new Date(currentYear, currentMonth - 1, 1).toISOString())
      .lt("created_at", new Date(currentYear, currentMonth, 1).toISOString());

    const currentMonthTokens = monthlyTokens?.reduce((sum, msg) => sum + (msg.total_tokens || 0), 0) || 0;
    monthlyUsage.total_tokens = currentMonthTokens;
    
    // Get WhatsApp integration status
    const { data: whatsappConfig, error: whatsappError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("auth_id", authId)
      .single();

    // Get subscription history (last 5 changes)
    const { data: recentHistory } = await supabase
      .from("user_subscription_history")
      .select("*")
      .eq("auth_id", authId)
      .order("created_at", { ascending: false })
      .limit(5);

    // Calculate daily message averages for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentMessages = messages.filter(
      m => new Date(m.created_at) >= thirtyDaysAgo
    );

    const dailyAverage = recentMessages.length / 30;

    res.json({
      success: true,
      data: {
        profile: {
          authId: profile.auth_id,
          displayName: profile.display_name,
          email: profile.email,
          plan: profile.plan,
          subscriptionEndDate: profile.subscription_end_date,
          isActive: !profile.is_suspended,
          createdAt: profile.created_at,
          phone: profile.phone,
        },
        usage: {
          contacts: totalContacts || 0,
          knowledgeBase: totalKnowledgeEntries || 0,
          messages: messageStats,
          dailyMessageAverage: Math.round(dailyAverage * 100) / 100,
          currentMonth: monthlyUsage || {
            messages_sent: 0,
            messages_received: 0,
            total_contacts: 0,
            new_contacts: 0,
            knowledge_base_queries: 0,
            total_tokens: 0,
          },
        },
        integrations: {
          whatsapp: whatsappConfig ? {
            isActive: whatsappConfig.is_active,
            phoneNumberId: whatsappConfig.whatsapp_phone_number_id,
            hasSystemToken: !!whatsappConfig.system_access_token,
            lastUpdated: whatsappConfig.updated_at
          } : null
        },
        subscriptionHistory: recentHistory || []
      }
    });
  } catch (error) {
    console.error("Error getting user stats:", error);
    res.status(500).json({ error: "Failed to get user statistics" });
  }
});

// ===== USER IMPERSONATION =====

// Start impersonating a user
router.post("/impersonate/:authId", requireAdmin, async (req, res) => {
  try {
    const { authId } = req.params;
    const { notes } = req.body;

    // Verify the target user exists
    const { data: targetUser, error: userError } = await supabase
      .from("profiles")
      .select("auth_id, display_name, email, plan, is_suspended")
      .eq("auth_id", authId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({ error: "User not found" });
    }

    // Check if user is suspended
    if (targetUser.is_suspended) {
      return res.status(400).json({ error: "Cannot impersonate suspended user" });
    }

    // Create an impersonation session
    const impersonationData = {
      admin_id: req.adminId,
      target_auth_id: authId,
      started_at: new Date().toISOString(),
      notes: notes || null,
      is_active: true,
    };

    const { data: impersonationSession, error: sessionError } = await supabase
      .from("admin_impersonation_sessions")
      .insert([impersonationData])
      .select()
      .single();

    if (sessionError) {
      console.error("Error creating impersonation session:", sessionError);
      return res.status(500).json({ error: "Failed to start impersonation" });
    }

    // Log the impersonation action
    await logAdminAction(
      req.adminId,
      "user_impersonation_start",
      authId,
      {
        target_user: targetUser.display_name || targetUser.email,
        session_id: impersonationSession.id,
        notes: notes || null,
      }
    );

    res.json({
      success: true,
      session: impersonationSession,
      targetUser: {
        authId: targetUser.auth_id,
        displayName: targetUser.display_name,
        email: targetUser.email,
        plan: targetUser.plan,
      },
      message: "Impersonation started successfully",
    });
  } catch (error) {
    console.error("Error starting impersonation:", error);
    res.status(500).json({ error: "Failed to start impersonation" });
  }
});

// Stop impersonating a user
router.post("/stop-impersonation/:sessionId", requireAdmin, async (req, res) => {
  try {
    const { sessionId } = req.params;

    // Get the current impersonation session
    const { data: session, error: sessionError } = await supabase
      .from("admin_impersonation_sessions")
      .select("*")
      .eq("id", sessionId)
      .eq("admin_id", req.adminId)
      .eq("is_active", true)
      .single();

    if (sessionError || !session) {
      return res.status(404).json({ error: "Active impersonation session not found" });
    }

    // End the impersonation session
    const { error: updateError } = await supabase
      .from("admin_impersonation_sessions")
      .update({
        ended_at: new Date().toISOString(),
        is_active: false,
      })
      .eq("id", sessionId);

    if (updateError) {
      console.error("Error ending impersonation session:", updateError);
      return res.status(500).json({ error: "Failed to stop impersonation" });
    }

    // Log the end of impersonation
    await logAdminAction(
      req.adminId,
      "user_impersonation_end",
      session.target_auth_id,
      {
        session_id: sessionId,
        duration_minutes: Math.round(
          (new Date() - new Date(session.started_at)) / (1000 * 60)
        ),
      }
    );

    res.json({
      success: true,
      message: "Impersonation stopped successfully",
    });
  } catch (error) {
    console.error("Error stopping impersonation:", error);
    res.status(500).json({ error: "Failed to stop impersonation" });
  }
});

// Get current impersonation status
router.get("/impersonation-status", requireAdmin, async (req, res) => {
  try {
    // Get active impersonation sessions for this admin
    const { data: sessions, error: sessionError } = await supabase
      .from("admin_impersonation_sessions")
      .select("id, target_auth_id, started_at, notes")
      .eq("admin_id", req.adminId)
      .eq("is_active", true)
      .order("started_at", { ascending: false });

    if (sessionError) {
      console.error("Error getting impersonation status:", sessionError);
      return res.status(500).json({ error: "Failed to get impersonation status" });
    }

    // Get profile data for each session
    const sessionsWithProfiles = await Promise.all(
      (sessions || []).map(async (session) => {
        const { data: profile } = await supabase
          .from("profiles")
          .select("display_name, email, plan")
          .eq("auth_id", session.target_auth_id)
          .single();
        
        return {
          ...session,
          profiles: profile || { display_name: "Unknown", email: "Unknown", plan: "free" }
        };
      })
    );

    res.json({
      success: true,
      activeSessions: sessionsWithProfiles || [],
      isImpersonating: (sessionsWithProfiles || []).length > 0,
    });
  } catch (error) {
    console.error("Error getting impersonation status:", error);
    res.status(500).json({ error: "Failed to get impersonation status" });
  }
});

// Get impersonated user data (for admin use)
router.get("/impersonated-user/:sessionId", requireAdmin, async (req, res) => {
  try {
    const { sessionId } = req.params;

    // Get the impersonation session
    const { data: session, error: sessionError } = await supabase
      .from("admin_impersonation_sessions")
      .select("id, target_auth_id, started_at, notes")
      .eq("id", sessionId)
      .eq("admin_id", req.adminId)
      .eq("is_active", true)
      .single();

    if (sessionError || !session) {
      return res.status(404).json({ error: "Active impersonation session not found" });
    }

    // Get the target user's profile separately
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("auth_id", session.target_auth_id)
      .single();

    if (profileError || !profile) {
      return res.status(404).json({ error: "Target user profile not found" });
    }

    // Create user object that mimics regular auth response
    const impersonatedUser = {
      user: {
        id: session.target_auth_id,
        email: profile.email,
        plan: profile.plan,
      },
      profile: profile,
      username: profile.display_name || profile.email,
      isImpersonated: true,
      impersonationSession: {
        id: session.id,
        started_at: session.started_at,
        notes: session.notes,
      },
    };

    res.json({
      success: true,
      user: impersonatedUser,
    });
  } catch (error) {
    console.error("Error getting impersonated user:", error);
    res.status(500).json({ error: "Failed to get impersonated user data" });
  }
});

// List impersonation history
router.get("/impersonation-history", requireAdmin, async (req, res) => {
  try {
    const { limit = 50, page = 1 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const { data: sessions, error: sessionError, count } = await supabase
      .from("admin_impersonation_sessions")
      .select("id, target_auth_id, started_at, ended_at, notes, is_active", { count: "exact" })
      .eq("admin_id", req.adminId)
      .order("started_at", { ascending: false })
      .range(offset, offset + parseInt(limit) - 1);

    if (sessionError) {
      console.error("Error getting impersonation history:", sessionError);
      return res.status(500).json({ error: "Failed to get impersonation history" });
    }

    // Get profile data for each session and calculate durations
    const sessionsWithDuration = await Promise.all(
      (sessions || []).map(async (session) => {
        const { data: profile } = await supabase
          .from("profiles")
          .select("display_name, email, plan")
          .eq("auth_id", session.target_auth_id)
          .single();
        
        return {
          ...session,
          profiles: profile || { display_name: "Unknown", email: "Unknown", plan: "free" },
          duration_minutes: session.ended_at 
            ? Math.round((new Date(session.ended_at) - new Date(session.started_at)) / (1000 * 60))
            : null,
        };
      })
    );

    res.json({
      success: true,
      sessions: sessionsWithDuration,
      pagination: {
        total: count || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil((count || 0) / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Error getting impersonation history:", error);
    res.status(500).json({ error: "Failed to get impersonation history" });
  }
});

export default router;
