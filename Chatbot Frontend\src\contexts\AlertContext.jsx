import React, { createContext, useContext, useState, useCallback } from "react";

const AlertContext = createContext();

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error("useAlert must be used within an AlertProvider");
  }
  return context;
};

export const AlertProvider = ({ children }) => {
  const [alerts, setAlerts] = useState([]);

  const addAlert = useCallback((alert) => {
    const id = Date.now() + Math.random();
    const newAlert = {
      id,
      type: alert.type || "info", // 'success', 'error', 'warning', 'info'
      title: alert.title,
      message: alert.message,
      duration: alert.duration || 5000,
      persistent: alert.persistent || false,
      ...alert,
    };

    setAlerts((prev) => [...prev, newAlert]);

    // Auto-remove after duration if not persistent
    if (!newAlert.persistent && newAlert.duration > 0) {
      setTimeout(() => {
        removeAlert(id);
      }, newAlert.duration);
    }

    return id;
  }, []);

  const removeAlert = useCallback((id) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id));
  }, []);

  const clearAllAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  // Convenience methods
  const success = useCallback(
    (message, options = {}) => {
      return addAlert({ type: "success", message, ...options });
    },
    [addAlert],
  );

  const error = useCallback(
    (message, options = {}) => {
      return addAlert({ type: "error", message, ...options });
    },
    [addAlert],
  );

  const warning = useCallback(
    (message, options = {}) => {
      return addAlert({ type: "warning", message, ...options });
    },
    [addAlert],
  );

  const info = useCallback(
    (message, options = {}) => {
      return addAlert({ type: "info", message, ...options });
    },
    [addAlert],
  );

  const value = {
    alerts,
    addAlert,
    removeAlert,
    clearAllAlerts,
    success,
    error,
    warning,
    info,
  };

  return (
    <AlertContext.Provider value={value}>{children}</AlertContext.Provider>
  );
};
