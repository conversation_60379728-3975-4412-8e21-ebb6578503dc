import { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useAlertMigration } from "../hooks/useAlertMigration";
import { InlineLoader } from "../components/ui/loader";
import Icon from "../components/ui/icon";

export default function ResetPassword() {
  const [email, setEmail] = useState("");
  const [sent, setSent] = useState(false);
  const [loading, setLoading] = useState(false);
  const { resetPassword } = useAuth();
  const { showError } = useAlertMigration();

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Clear previous errors - not needed with global alerts
    setLoading(true);
    try {
      await resetPassword(email);
      setSent(true);
    } catch (err) {
      showError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        {/* Success State */}
        {sent ? (
          <div className="fade-in">
            {/* Success Header */}
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <Icon name="CheckCircle" className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Check your email
              </h2>
              <p className="text-gray-600">
                We've sent password reset instructions to your email
              </p>
            </div>

            {/* Success Card */}
            <div className="card">
              <div className="text-center space-y-6">
                <div className="bg-green-50 rounded-2xl p-6">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Icon
                      name="CheckCircle"
                      className="w-5 h-5 text-green-600"
                    />
                    <span className="text-green-800 font-semibold">
                      Email sent successfully!
                    </span>
                  </div>
                  <p className="text-green-700 text-sm leading-relaxed">
                    We've sent a password reset link to{" "}
                    <span className="font-semibold">{email}</span>. Please check
                    your inbox and follow the instructions.
                  </p>
                </div>

                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Didn't receive the email? Check your spam folder or request
                    a new one.
                  </p>

                  <div className="space-y-3">
                    <button
                      onClick={() => {
                        setSent(false);
                        setEmail("");
                        // Clear errors - not needed with global alerts
                      }}
                      className="btn-secondary w-full py-3"
                    >
                      <Icon name="Refresh" className="w-4 h-4 mr-2" />
                      Try again
                    </button>

                    <Link
                      to="/login"
                      className="btn-primary w-full py-3 text-center block"
                    >
                      Back to Sign In
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Form State */
          <div className="fade-in">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <Icon name="Lock" className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Reset your password
              </h2>
              <p className="text-gray-600 leading-relaxed">
                Enter your email address and we'll send you a link to reset your
                password
              </p>
            </div>

            {/* Form Card */}
            <div className="card">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Input */}
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-semibold text-gray-700 mb-3"
                  >
                    Email address
                  </label>
                  <div className="relative">
                    <input
                      id="email"
                      type="email"
                      className="input-field"
                      style={{ paddingLeft: "3rem" }}
                      placeholder="Enter your email address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                      <Icon name="Mail" className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Error messages now handled by global AlertContainer */}

                {/* Submit Button */}
                <button
                  type="submit"
                  className="btn-primary w-full text-lg py-4 relative overflow-hidden"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-3">
                      <InlineLoader size="sm" />
                      <span>Sending reset email...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <Icon name="Send" className="w-5 h-5" />
                      <span>Send reset email</span>
                    </div>
                  )}
                </button>

                {/* Help Text */}
                <div className="bg-blue-50 rounded-2xl p-4">
                  <div className="flex items-start gap-3">
                    <Icon
                      name="Info"
                      className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5"
                    />
                    <div className="text-sm text-blue-700">
                      <p className="font-medium mb-1">Having trouble?</p>
                      <p>
                        Make sure to check your spam folder. If you still don't
                        receive an email, contact our support team.
                      </p>
                    </div>
                  </div>
                </div>
              </form>
            </div>

            {/* Links */}
            <div className="mt-8 text-center space-y-4">
              <p className="text-gray-600">
                Remember your password?{" "}
                <Link
                  to="/login"
                  className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200 hover:underline"
                >
                  Sign in here
                </Link>
              </p>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 px-4 text-gray-500">
                    or
                  </span>
                </div>
              </div>

              <p className="text-gray-600">
                Don't have an account?{" "}
                <Link
                  to="/register"
                  className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200 hover:underline"
                >
                  Create one here
                </Link>
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
