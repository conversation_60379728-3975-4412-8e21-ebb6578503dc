import { useState, useEffect } from "react";
import { aiService } from "../../services/ai";
import QuotaStatus from "../../components/QuotaStatus";
import { useMobileAlertMigration } from "../../contexts/MobileAlertContext";
import { getUserId, formatNumber, handleApiError } from "../../utils/common";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { Bot, Users, MessageCircle, TrendingUp } from "lucide-react";

/**
 * Mobile Overview Component
 * Displays key metrics and quick actions in a mobile-optimized layout
 */
export default function MobileOverview({ user }) {
  const { showError } = useMobileAlertMigration();
  const [overview, setOverview] = useState(null);
  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);



  // Fetch overview data
  const fetchOverview = async () => {
    try {


      const userId = getUserId(user);
      if (!userId) return;

      const data = await aiService.getStatisticsOverview(userId);
      if (data.success) {
        setOverview(data.data);
      } else {
        showError(data.error);
      }
    } catch (error) {
      handleApiError(error, showError, "Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchOverview();
    }
  }, [user]);

  const formatTime = (ms) => {
    if (ms >= 1000) return (ms / 1000).toFixed(1) + "s";
    return ms + "ms";
  };

  if (showLoadingState) {
    return (
      <div className="p-4 space-y-4">
        {/* Loading skeleton */}
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-lg p-4">
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-1"></div>
                <div className="h-2 bg-gray-200 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col overflow-hidden">
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl p-4 text-white">
        <h2 className="text-lg font-bold mb-1">
          Welcome back, {user?.username || "User"}!
        </h2>
        <p className="text-blue-100 text-sm">
          Here's your chatbot activity overview
        </p>
      </div>

      {/* Today's Stats */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-3">
          Today's Activity
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Bot className="w-4 h-4 text-blue-600" />
              </div>
            </div>
            <p className="text-xs font-medium text-gray-600 mb-1">
              Messages Today
            </p>
            <p className="text-xl font-bold text-blue-600">
              {formatNumber(overview?.today?.total_messages || 0)}
            </p>
            <p className="text-xs text-gray-500">
              {overview?.today?.incoming_messages || 0} in ·{" "}
              {overview?.today?.outgoing_messages || 0} out
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="w-4 h-4 text-green-600" />
              </div>
            </div>
            <p className="text-xs font-medium text-gray-600 mb-1">
              Active Contacts
            </p>
            <p className="text-xl font-bold text-green-600">
              {formatNumber(overview?.contacts?.active || 0)}
            </p>
            <p className="text-xs text-gray-500">
              Active contacts
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="w-4 h-4 text-purple-600" />
              </div>
            </div>
            <p className="text-xs font-medium text-gray-600 mb-1">
              Avg Response
            </p>
            <p className="text-xl font-bold text-purple-600">
              {formatTime(overview?.performance?.avgResponseTime || 0)}
            </p>
            <p className="text-xs text-gray-500">
              Response time
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-orange-600" />
              </div>
            </div>
            <p className="text-xs font-medium text-gray-600 mb-1">
              Success Rate
            </p>
            <p className="text-xl font-bold text-orange-600">
              {overview?.performance?.knowledgeHitRate || 0}%
            </p>
            <p className="text-xs text-gray-500">
              Knowledge hit rate
            </p>
          </div>
        </div>
      </div>

      {/* Quota Status */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-3">
          Usage & Quota
        </h3>
        <div>
          {getUserId(user) ? (
            <QuotaStatus authId={getUserId(user)} />
          ) : (
            <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 text-center">
              <p className="text-sm text-gray-600">
                Loading quota information...
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-3">
          All Time Stats
        </h3>
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">This Month Messages</span>
            <span className="font-semibold text-gray-900">
              {formatNumber(overview?.thisMonth?.total_messages || 0)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Total Contacts</span>
            <span className="font-semibold text-gray-900">
              {formatNumber(overview?.contacts?.total || 0)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Avg Response Time</span>
            <span className="font-semibold text-gray-900">
              {formatTime(overview?.performance?.avgResponseTime || 0)}
            </span>
          </div>
        </div>
      </div>

        {/* Bottom padding for safe area */}
        <div className="h-4"></div>
      </div>
    </div>
  );
}
