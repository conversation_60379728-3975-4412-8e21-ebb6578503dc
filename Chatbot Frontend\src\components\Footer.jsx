import React from "react";
import { Link } from "react-router-dom";

export default function Footer() {
  return (
    <footer className="w-full bg-white/90 py-8">
      <div className="container mx-auto px-6 flex flex-col md:flex-row items-center justify-between gap-4 text-gray-600 text-sm text-center md:text-left">
        <div className="flex items-center gap-2 justify-center md:justify-start w-full md:w-auto">
          <span className="font-bold text-blue-700">Chilbee</span>
          <span className="inline">&copy; {new Date().getFullYear()} All rights reserved.</span>
        </div>
        <div className="flex flex-row items-center gap-2 justify-center w-full md:w-auto">
          <Link to="/contact" className="hover:text-blue-600 transition-colors">Contact Us</Link>
          <span className="mx-1">|</span>
          <Link to="/terms" className="hover:text-blue-600 transition-colors">Terms & Conditions</Link>
          <span className="mx-1">|</span>
          <Link to="/privacy" className="hover:text-blue-600 transition-colors">Privacy Policy</Link>
        </div>
      </div>
    </footer>
  );
} 