import React from "react";
import { useAlert } from "../contexts/AlertContext";
import Icon from "./ui/icon";
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from "lucide-react";

export const AlertContainer = () => {
  const { alerts, removeAlert } = useAlert();

  if (alerts.length === 0) return null;

  const getAlertIcon = (type) => {
    switch (type) {
      case "success":
        return <Icon name="CircleCheck" className="w-5 h-5" />;
      case "error":
        return <Icon name="AlertCircle" className="w-5 h-5" />;
      case "warning":
        return <Icon name="AlertTriangle" className="w-5 h-5" />;
      case "info":
      default:
        return <Icon name="Info" className="w-5 h-5" />;
    }
  };

  const getAlertStyles = (type) => {
    switch (type) {
      case "success":
        return "bg-green-50 border-green-200 text-green-800";
      case "error":
        return "bg-red-50 border-red-200 text-red-800";
      case "warning":
        return "bg-yellow-50 border-yellow-200 text-yellow-800";
      case "info":
      default:
        return "bg-blue-50 border-blue-200 text-blue-800";
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {alerts.map((alert) => (
        <div
          key={alert.id}
          className={`relative border rounded-xl p-4 shadow-lg animate-in slide-in-from-right duration-300 ${getAlertStyles(alert.type)}`}
        >
          <div className="flex items-center gap-3">
            {getAlertIcon(alert.type)}
            <div className="flex-1 min-w-0">
              {alert.title && (
                <h4 className="font-medium text-sm mb-1">{alert.title}</h4>
              )}
              <p className="text-sm">{alert.message}</p>
            </div>
            <button
              onClick={() => removeAlert(alert.id)}
              className="flex-shrink-0 text-current hover:opacity-70 transition-opacity"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};
