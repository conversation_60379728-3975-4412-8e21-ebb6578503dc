import { useState, useRef, useEffect } from 'react';

/**
 * Lazy Loading Image Component
 * Only loads images when they come into viewport for better performance
 */
export default function LazyImage({ 
  src, 
  alt, 
  className = "", 
  placeholder = null,
  onLoad = () => {},
  onError = () => {},
  ...props 
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad();
  };

  const handleError = () => {
    setHasError(true);
    onError();
  };

  return (
    <div ref={imgRef} className={`relative ${className}`} {...props}>
      {!isInView && (
        <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
          {placeholder || (
            <div className="text-gray-400 text-sm">Loading...</div>
          )}
        </div>
      )}
      
      {isInView && !hasError && (
        <>
          {!isLoaded && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              <div className="text-gray-400 text-sm">Loading...</div>
            </div>
          )}
          <img
            src={src}
            alt={alt}
            className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
            onLoad={handleLoad}
            onError={handleError}
            loading="lazy"
          />
        </>
      )}
      
      {hasError && (
        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
          <div className="text-gray-500 text-sm">Failed to load image</div>
        </div>
      )}
    </div>
  );
}

/**
 * Optimized Avatar Component with lazy loading
 */
export function LazyAvatar({ name, className = "w-12 h-12", ...props }) {
  const initials = name
    ? name
        .split(" ")
        .map(word => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2)
    : "??";

  return (
    <div 
      className={`${className} bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm`}
      {...props}
    >
      {initials}
    </div>
  );
}

/**
 * Performance monitoring hook
 */
export function usePerformanceMonitor(componentName) {
  useEffect(() => {
    if (import.meta.env.MODE === 'development') {
      const startTime = performance.now();

      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;

        if (renderTime > 16) { // More than one frame (60fps)
          console.warn(`${componentName} took ${renderTime.toFixed(2)}ms to render`);
        }
      };
    }
  });
}

/**
 * Debounced search hook for better performance
 */
export function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Intersection Observer hook for infinite scrolling
 */
export function useInfiniteScroll(callback, hasMore = true) {
  const [isFetching, setIsFetching] = useState(false);
  const elementRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && hasMore && !isFetching) {
          setIsFetching(true);
          callback().finally(() => setIsFetching(false));
        }
      },
      { threshold: 1.0 }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [callback, hasMore, isFetching]);

  return [elementRef, isFetching];
}

/**
 * Memory usage monitor (development only)
 */
export function useMemoryMonitor() {
  useEffect(() => {
    // Memory monitoring disabled in production for performance
    if (import.meta.env.MODE === 'development' && 'memory' in performance) {
      const logMemory = () => {
        const memory = performance.memory;
        console.log('Memory usage:', {
          used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`
        });
      };

      const interval = setInterval(logMemory, 10000); // Log every 10 seconds
      return () => clearInterval(interval);
    }
  }, []);
}
