-- =====================================================
-- URGENT FIX: Follow-up Constraints and Duplicate Rules
-- =====================================================
-- Run these commands in Supabase SQL Editor to fix the errors

-- 1. Fix the follow_up_type constraint to allow more types
ALTER TABLE follow_up_schedules 
DROP CONSTRAINT IF EXISTS follow_up_schedules_follow_up_type_check;

ALTER TABLE follow_up_schedules 
ADD CONSTRAINT follow_up_schedules_follow_up_type_check 
CHECK (follow_up_type IN (
  'booking_reminder', 
  'order_follow_up', 
  'engagement_check',
  'price_follow_up',
  'support_follow_up',
  'purchase_assistance'
));

-- 2. Clean up any duplicate follow-up rules (optional)
-- This will keep only the most recent rule for each auth_id + intent_category combination
DELETE FROM follow_up_rules 
WHERE id NOT IN (
  SELECT DISTINCT ON (auth_id, intent_category) id
  FROM follow_up_rules
  ORDER BY auth_id, intent_category, created_at DESC
);

-- 3. Verify the fixes
-- Check allowed follow_up_types
SELECT constraint_name, check_clause 
FROM information_schema.check_constraints 
WHERE constraint_name = 'follow_up_schedules_follow_up_type_check';

-- Check for duplicate rules
SELECT auth_id, intent_category, COUNT(*) as count
FROM follow_up_rules 
GROUP BY auth_id, intent_category 
HAVING COUNT(*) > 1;

-- 4. Test follow-up scheduling (should work now)
-- You can test by triggering AI analysis on any contact
