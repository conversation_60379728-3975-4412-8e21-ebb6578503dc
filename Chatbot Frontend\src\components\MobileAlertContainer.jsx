import React from 'react';
import { useMobileAlert } from '../contexts/MobileAlertContext';
import MobileAlert from './MobileAlert';

/**
 * Mobile Alert Container Component
 * Renders all active mobile alerts with proper positioning and stacking
 */
export const MobileAlertContainer = () => {
  const { alerts, removeAlert } = useMobileAlert();

  if (alerts.length === 0) return null;

  // Group alerts by position
  const alertsByPosition = alerts.reduce((acc, alert) => {
    const position = alert.position || 'bottom';
    if (!acc[position]) acc[position] = [];
    acc[position].push(alert);
    return acc;
  }, {});

  return (
    <>
      {/* Top alerts */}
      {alertsByPosition.top && (
        <div className="fixed left-0 right-0 z-100 pointer-events-none" style={{ top: '100px' }}>
          <div className="p-2 space-y-1">
            {alertsByPosition.top.map((alert) => (
              <div key={alert.id} className="pointer-events-auto">
                <MobileAlert
                  type={alert.type}
                  title={alert.title}
                  message={alert.message}
                  duration={alert.duration}
                  persistent={alert.persistent}
                  position="top"
                  onDismiss={() => removeAlert(alert.id)}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Center alerts */}
      {alertsByPosition.center && (
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <div className="p-2 w-full max-w-sm">
            {alertsByPosition.center.map((alert) => (
              <div key={alert.id} className="pointer-events-auto mb-1 last:mb-0">
                <MobileAlert
                  type={alert.type}
                  title={alert.title}
                  message={alert.message}
                  duration={alert.duration}
                  persistent={alert.persistent}
                  position="center"
                  onDismiss={() => removeAlert(alert.id)}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Bottom alerts */}
      {alertsByPosition.bottom && (
        <div className="fixed bottom-0 left-0 right-0 z-50 pointer-events-none">
          <div className="p-2 space-y-1">
            {alertsByPosition.bottom.map((alert) => (
              <div key={alert.id} className="pointer-events-auto">
                <MobileAlert
                  type={alert.type}
                  title={alert.title}
                  message={alert.message}
                  duration={alert.duration}
                  persistent={alert.persistent}
                  position="bottom"
                  onDismiss={() => removeAlert(alert.id)}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default MobileAlertContainer;
