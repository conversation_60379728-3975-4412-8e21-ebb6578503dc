import express from "express";
import supabase from "../utils/supabase.js";
import {
  getPaginationParams,
  buildPaginatedResponse,
} from "../utils/pagination.js";

const router = express.Router();

// Sign up (register)
router.post("/signup", async (req, res) => {
  const { email, password, phone, name } = req.body;

  // First, create the auth user
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });
  if (error) return res.status(400).json({ error: error.message });

  // If auth user was created successfully, create profile record
  let profile = null;
  if (data.user) {
    const profileData = {
      auth_id: data.user.id,
      display_name: name,
      phone: phone,
      email: email,
      plan: "free",
    };

    const { data: profileInsertData, error: profileError } = await supabase
      .from("profiles")
      .insert([profileData])
      .select()
      .single();

    if (profileError) {
      console.error("Profile creation error:", profileError);
      // Don't fail the signup if profile creation fails
    } else {
      profile = profileInsertData;
    }
  }

  // Add plan info to user data
  const userWithPlan = {
    ...data.user,
    plan: profile?.plan || "free",
  };

  res.json({
    user: userWithPlan,
    session: data.session,
    profile: profile,
    displayName: profile?.display_name || null,
  });
});

// Sign in (login)
router.post("/signin", async (req, res) => {
  const { email, password } = req.body;
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  if (error) return res.status(400).json({ error: error.message });

  // Get profile data for the signed-in user
  let profile = null;
  if (data.user) {
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("auth_id", data.user.id)
      .single();

    if (profileError && profileError.code !== "PGRST116") {
      console.error("Profile fetch error:", profileError);
    } else if (profileData) {
      profile = profileData;
    }
    // If PGRST116 (no rows), profile stays null - this is expected for new users
  }

  // Add plan info to user data
  const userWithPlan = {
    ...data.user,
    plan: profile?.plan || "free",
  };

  res.json({
    user: userWithPlan,
    session: data.session,
    profile: profile,
    displayName: profile?.display_name || null,
  });
});

// Sign out
router.post("/signout", async (req, res) => {
  const { access_token } = req.body;
  const { error } = await supabase.auth.signOut({ accessToken: access_token });
  if (error) return res.status(400).json({ error: error.message });
  res.json({ message: "Signed out successfully" });
});

// Get current user (from access token)
router.get("/user", async (req, res) => {
  const { access_token } = req.headers;
  const { data, error } = await supabase.auth.getUser(access_token);
  if (error) return res.status(400).json({ error: error.message });

  // Get profile data
  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select("*")
    .eq("auth_id", data.user.id)
    .single();

  // Handle profile error (PGRST116 means no profile exists, which is normal for new users)
  if (profileError && profileError.code !== "PGRST116") {
    console.error("Profile fetch error:", profileError);
  }

  // Add plan info to user data
  const userWithPlan = {
    ...data.user,
    plan: profile?.plan || "free",
  };

  res.json({
    user: userWithPlan,
    profile: profile || null,
  });
});

// Password reset (send email)
router.post("/reset-password", async (req, res) => {
  const { email } = req.body;
  const { data, error } = await supabase.auth.resetPasswordForEmail(email);
  if (error) return res.status(400).json({ error: error.message });
  res.json({ message: "Password reset email sent", data });
});

// Forgot password (change password with access token from reset URL)
router.post("/forgot-password", async (req, res) => {
  const { newPassword, accessToken, refreshToken } = req.body;

  if (!newPassword || !accessToken) {
    return res.status(400).json({
      error: "New password and access token are required",
    });
  }

  try {
    // Set the session using the access token from the reset URL
    const { data: sessionData, error: sessionError } =
      await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken || "",
      });

    if (sessionError || !sessionData.user) {
      return res.status(400).json({ error: "Invalid or expired reset token" });
    }

    // Update the password
    const { data: updateData, error: updateError } =
      await supabase.auth.updateUser({
        password: newPassword,
      });

    if (updateError) {
      return res.status(400).json({ error: updateError.message });
    }

    // Get profile data for the user
    let profile = null;
    if (updateData.user) {
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("auth_id", updateData.user.id)
        .single();

      if (!profileError || profileError.code === "PGRST116") {
        profile = profileData;
      } else {
        console.error("Profile fetch error:", profileError);
      }
    }

    res.json({
      message: "Password updated successfully",
      user: updateData.user,
      session: updateData.session,
      profile: profile,
      displayName: profile?.display_name || null,
    });
  } catch (err) {
    console.error("Forgot password error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Change password (for authenticated users)
router.post("/change-password", async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Verify current password by attempting to sign in
    const { error: verifyError } = await supabase.auth.signInWithPassword({
      email: userData.user.email,
      password: currentPassword,
    });

    if (verifyError) {
      return res.status(400).json({ error: "Current password is incorrect" });
    }

    // Set session and update password
    await supabase.auth.setSession({
      access_token: token,
      refresh_token: req.body.refresh_token || "",
    });

    const { data, error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json({
      message: "Password updated successfully",
      user: data.user,
    });
  } catch (err) {
    console.error("Password change error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Change display name (for authenticated users)
router.post("/change-display-name", async (req, res) => {
  const { newDisplayName } = req.body;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  if (!newDisplayName || newDisplayName.trim() === "") {
    return res.status(400).json({ error: "Display name is required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Update display name in profiles table
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .update({ display_name: newDisplayName.trim() })
      .eq("auth_id", userData.user.id)
      .select()
      .single();

    if (profileError) {
      console.error("Profile update error:", profileError);
      return res.status(400).json({ error: "Failed to update display name" });
    }

    // Also update the user metadata in Supabase auth
    const { data: authUpdateData } = await supabase.auth.updateUser({
      data: {
        display_name: newDisplayName.trim(),
      },
    });

    res.json({
      message: "Display name updated successfully",
      profile: profileData,
      displayName: profileData.display_name,
      user: authUpdateData?.user || userData.user,
    });
  } catch (err) {
    console.error("Display name change error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Helper function to check if user is admin
async function isAdmin(authId) {
  const { data: profile, error } = await supabase
    .from("profiles")
    .select("role, plan")
    .eq("auth_id", authId)
    .single();

  if (error) {
    console.error("Admin check error:", error);
    return false;
  }

  return profile?.role === "admin" || profile?.plan === "admin";
}

// Change user plan (admin only)
router.post("/admin/change-user-plan", async (req, res) => {
  const { userId, newPlan } = req.body;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  if (!userId || !newPlan) {
    return res.status(400).json({ error: "User ID and new plan are required" });
  }

  const validPlans = ["free", "kecil", "popular", "besar", "admin"];
  if (!validPlans.includes(newPlan)) {
    return res.status(400).json({ error: "Invalid plan type" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Update user plan in profiles table
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .update({ plan: newPlan })
      .eq("auth_id", userId)
      .select("auth_id, display_name, email, plan, phone")
      .single();

    if (profileError) {
      console.error("Plan update error:", profileError);
      if (profileError.code === "PGRST116") {
        return res.status(404).json({ error: "User not found" });
      }
      return res.status(400).json({ error: "Failed to update user plan" });
    }

    res.json({
      message: "User plan updated successfully",
      updatedUser: {
        authId: profileData.auth_id,
        displayName: profileData.display_name,
        email: profileData.email,
        plan: profileData.plan,
        phone: profileData.phone,
      },
      updatedBy: {
        authId: userData.user.id,
        email: userData.user.email,
      },
    });
  } catch (err) {
    console.error("Plan change error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Get all users (admin only)
router.get("/admin/users", async (req, res) => {
  const { search, plan, sortBy = "created_at", sortOrder = "desc" } = req.query;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    // Build query with filters
    let query = supabase
      .from("profiles")
      .select(
        "auth_id, display_name, email, plan, phone, is_suspended, created_at",
        { count: "exact" },
      );

    // Apply filters
    if (search) {
      query = query.or(
        `display_name.ilike.%${search}%, email.ilike.%${search}%`,
      );
    }

    if (plan) {
      query = query.eq("plan", plan);
    }

    // Apply sorting
    const validSortFields = ["created_at", "display_name", "email", "plan"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: profiles, error: profilesError, count } = await query;

    if (profilesError) {
      console.error("Users fetch error:", profilesError);
      return res.status(400).json({ error: "Failed to fetch users" });
    }

    res.json(buildPaginatedResponse(profiles, count || 0, page, limit));
  } catch (err) {
    console.error("Users fetch error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Get system overview (admin only)
router.get("/admin/system-overview", async (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Get system statistics
    const [
      { count: totalUsers },
      { count: totalKnowledgeEntries },
      { count: totalContacts },
      { count: totalMessages },
      { data: recentUsers },
      { data: planDistribution },
    ] = await Promise.all([
      supabase.from("profiles").select("*", { count: "exact", head: true }),
      supabase
        .from("knowledge_base")
        .select("*", { count: "exact", head: true }),
      supabase.from("contacts").select("*", { count: "exact", head: true }),
      supabase
        .from("message_statistics")
        .select("*", { count: "exact", head: true }),
      supabase
        .from("profiles")
        .select("auth_id, display_name, email, plan, created_at")
        .order("created_at", { ascending: false })
        .limit(10),
      supabase
        .from("profiles")
        .select("plan")
        .then((result) => {
          if (result.error) throw result.error;
          const distribution = {};
          result.data.forEach((user) => {
            distribution[user.plan] = (distribution[user.plan] || 0) + 1;
          });
          return { data: distribution };
        }),
    ]);

    // Get today's activity
    const today = new Date().toISOString().split("T")[0];
    const { data: todayStats } = await supabase
      .from("daily_statistics")
      .select("*")
      .eq("date", today);

    const todayActivity = todayStats?.reduce(
      (acc, stat) => ({
        totalMessages: acc.totalMessages + (stat.total_messages || 0),
        totalTokens: acc.totalTokens + (stat.total_tokens || 0),
        uniqueContacts: acc.uniqueContacts + (stat.unique_contacts || 0),
      }),
      { totalMessages: 0, totalTokens: 0, uniqueContacts: 0 },
    ) || { totalMessages: 0, totalTokens: 0, uniqueContacts: 0 };

    res.json({
      systemStats: {
        totalUsers: totalUsers || 0,
        totalKnowledgeEntries: totalKnowledgeEntries || 0,
        totalContacts: totalContacts || 0,
        totalMessages: totalMessages || 0,
      },
      todayActivity,
      planDistribution: planDistribution || {},
      recentUsers: recentUsers || [],
    });
  } catch (err) {
    console.error("System overview error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Get user detailed info (admin only)
router.get("/admin/user/:userId", async (req, res) => {
  const { userId } = req.params;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("auth_id", userId)
      .single();

    if (profileError) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get user statistics
    const [
      { count: knowledgeCount },
      { count: contactsCount },
      { data: recentMessages },
      { data: chatbotSettings },
    ] = await Promise.all([
      supabase
        .from("knowledge_base")
        .select("*", { count: "exact", head: true })
        .eq("auth_id", userId),
      supabase
        .from("contacts")
        .select("*", { count: "exact", head: true })
        .eq("auth_id", userId),
      supabase
        .from("message_statistics")
        .select("*")
        .eq("auth_id", userId)
        .order("created_at", { ascending: false })
        .limit(10),
      supabase
        .from("chatbot_settings")
        .select("*")
        .eq("auth_id", userId)
        .single(),
    ]);

    res.json({
      profile,
      statistics: {
        knowledgeEntries: knowledgeCount || 0,
        contacts: contactsCount || 0,
      },
      recentMessages: recentMessages || [],
      chatbotSettings: chatbotSettings || null,
    });
  } catch (err) {
    console.error("User details error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Suspend/Unsuspend user (admin only)
router.post("/admin/user/:userId/suspend", async (req, res) => {
  const { userId } = req.params;
  const { suspend } = req.body; // true to suspend, false to unsuspend
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Add suspended field to profile or update existing
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .update({
        is_suspended: suspend === true,
        suspended_at: suspend === true ? new Date().toISOString() : null,
        suspended_by: suspend === true ? userData.user.id : null,
      })
      .eq("auth_id", userId)
      .select()
      .single();

    if (profileError) {
      return res.status(404).json({ error: "User not found" });
    }

    res.json({
      message: `User ${suspend ? "suspended" : "unsuspended"} successfully`,
      user: profileData,
      actionBy: {
        authId: userData.user.id,
        email: userData.user.email,
      },
    });
  } catch (err) {
    console.error("User suspension error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Get all knowledge base entries across all users (admin only)
router.get("/admin/knowledge-base", async (req, res) => {
  const {
    userId,
    search,
    sortBy = "created_at",
    sortOrder = "desc",
  } = req.query;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    let query = supabase.from("knowledge_base").select(
      `
        id, auth_id, title, content, created_at, updated_at,
        profiles!inner(display_name, email)
      `,
      { count: "exact" },
    );

    // Apply filters
    if (userId) {
      query = query.eq("auth_id", userId);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%, content.ilike.%${search}%`);
    }

    // Apply sorting
    const validSortFields = ["created_at", "updated_at", "title"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error("Knowledge base fetch error:", error);
      return res
        .status(400)
        .json({ error: "Failed to fetch knowledge base entries" });
    }

    res.json(buildPaginatedResponse(data, count || 0, page, limit));
  } catch (err) {
    console.error("Knowledge base admin error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Delete knowledge base entry (admin only)
router.delete("/admin/knowledge-base/:entryId", async (req, res) => {
  const { entryId } = req.params;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    const { data, error } = await supabase
      .from("knowledge_base")
      .delete()
      .eq("id", entryId)
      .select()
      .single();

    if (error) {
      return res.status(404).json({ error: "Knowledge base entry not found" });
    }

    res.json({
      message: "Knowledge base entry deleted successfully",
      deletedEntry: data,
      deletedBy: {
        authId: userData.user.id,
        email: userData.user.email,
      },
    });
  } catch (err) {
    console.error("Knowledge deletion error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Get system-wide message statistics (admin only)
router.get("/admin/message-statistics", async (req, res) => {
  const {
    startDate,
    endDate,
    groupBy = "date",
    userId,
    sortBy = "date",
    sortOrder = "desc",
  } = req.query;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    let query = supabase
      .from("daily_statistics")
      .select("*, profiles!inner(display_name, email)", { count: "exact" });

    // Apply filters
    if (startDate) {
      query = query.gte("date", startDate);
    }
    if (endDate) {
      query = query.lte("date", endDate);
    }
    if (userId) {
      query = query.eq("auth_id", userId);
    }

    // Apply sorting
    const validSortFields = [
      "date",
      "total_messages",
      "total_tokens",
      "unique_contacts",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "date";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error("Message statistics error:", error);
      return res
        .status(400)
        .json({ error: "Failed to fetch message statistics" });
    }

    // Aggregate data if needed
    let processedData = data || [];
    if (groupBy === "date") {
      const aggregated = {};
      processedData.forEach((stat) => {
        const date = stat.date;
        if (!aggregated[date]) {
          aggregated[date] = {
            date,
            totalMessages: 0,
            totalTokens: 0,
            uniqueContacts: 0,
            userCount: 0,
            users: [],
          };
        }
        aggregated[date].totalMessages += stat.total_messages || 0;
        aggregated[date].totalTokens += stat.total_tokens || 0;
        aggregated[date].uniqueContacts += stat.unique_contacts || 0;
        aggregated[date].userCount += 1;
        aggregated[date].users.push({
          authId: stat.auth_id,
          displayName: stat.profiles?.display_name,
          email: stat.profiles?.email,
        });
      });
      processedData = Object.values(aggregated);
    }

    const response = buildPaginatedResponse(
      processedData,
      count || 0,
      page,
      limit,
    );
    response.groupBy = groupBy;

    res.json(response);
  } catch (err) {
    console.error("Message statistics admin error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

// System maintenance - Clean old data (admin only)
router.post("/admin/maintenance/cleanup", async (req, res) => {
  const { daysToKeep = 90, tables = ["chat_history", "message_statistics"] } =
    req.body;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Authorization token required" });
  }

  const token = authHeader.split(" ")[1];

  try {
    // Get current user from token
    const { data: userData, error: userError } =
      await supabase.auth.getUser(token);

    if (userError || !userData.user) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }

    // Check if current user is admin
    const adminCheck = await isAdmin(userData.user.id);
    if (!adminCheck) {
      return res.status(403).json({ error: "Admin access required" });
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const results = {};

    // Clean up chat history
    if (tables.includes("chat_history")) {
      const { count: deletedChatHistory } = await supabase
        .from("chat_history")
        .delete()
        .lt("created_at", cutoffDate.toISOString())
        .select("*", { count: "exact", head: true });
      results.chat_history = deletedChatHistory || 0;
    }

    // Clean up message statistics
    if (tables.includes("message_statistics")) {
      const { count: deletedMessageStats } = await supabase
        .from("message_statistics")
        .delete()
        .lt("created_at", cutoffDate.toISOString())
        .select("*", { count: "exact", head: true });
      results.message_statistics = deletedMessageStats || 0;
    }

    res.json({
      message: "Cleanup completed successfully",
      results,
      cutoffDate: cutoffDate.toISOString(),
      performedBy: {
        authId: userData.user.id,
        email: userData.user.email,
      },
    });
  } catch (err) {
    console.error("Cleanup error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

export default router;
