import { useState, useCallback, useMemo } from "react";

/**
 * Custom hook for managing pagination state
 * @param {Object} options - Configuration options
 * @param {number} options.initialPage - Initial page number (default: 1)
 * @param {number} options.initialLimit - Initial items per page (default: 10)
 * @param {string} options.initialSearch - Initial search query (default: '')
 * @param {string} options.initialSortBy - Initial sort field (default: '')
 * @param {string} options.initialSortOrder - Initial sort order (default: 'desc')
 * @param {Object} options.initialFilters - Initial filter values (default: {})
 * @returns {Object} Pagination state and methods
 */
export const usePagination = ({
  initialPage = 1,
  initialLimit = 10,
  initialSearch = "",
  initialSortBy = "",
  initialSortOrder = "desc",
  initialFilters = {},
} = {}) => {
  // Pagination state
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Search and filter state
  const [search, setSearch] = useState(initialSearch);
  const [sortBy, setSortBy] = useState(initialSortBy);
  const [sortOrder, setSortOrder] = useState(initialSortOrder);
  const [filters, setFilters] = useState(initialFilters);

  // Loading and error state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Calculate pagination info
  const paginationInfo = useMemo(
    () => ({
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      startItem: totalItems > 0 ? (page - 1) * limit + 1 : 0,
      endItem: Math.min(page * limit, totalItems),
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    }),
    [page, limit, totalItems, totalPages],
  );

  // Get current query parameters
  const getQueryParams = useCallback(
    () => ({
      page,
      limit,
      search: search.trim(),
      sortBy,
      sortOrder,
      ...filters,
    }),
    [page, limit, search, sortBy, sortOrder, filters],
  );

  // Update pagination data from API response
  const updatePagination = useCallback(
    (data) => {
      if (data.pagination) {
        setTotalItems(data.pagination.total || 0);
        setTotalPages(data.pagination.totalPages || 0);
      } else if (data.total !== undefined) {
        setTotalItems(data.total);
        setTotalPages(Math.ceil(data.total / limit));
      }
    },
    [limit],
  );

  // Page navigation methods
  const goToPage = useCallback(
    (newPage) => {
      if (newPage >= 1 && newPage <= totalPages && newPage !== page) {
        setPage(newPage);
      }
    },
    [page, totalPages],
  );

  const goToFirstPage = useCallback(() => {
    setPage(1);
  }, []);

  const goToLastPage = useCallback(() => {
    setPage(totalPages);
  }, [totalPages]);

  const goToNextPage = useCallback(() => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  }, [page, totalPages]);

  const goToPrevPage = useCallback(() => {
    if (page > 1) {
      setPage(page - 1);
    }
  }, [page]);

  // Change items per page
  const changeLimit = useCallback((newLimit) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  }, []);

  // Search methods
  const updateSearch = useCallback((newSearch) => {
    setSearch(newSearch);
    setPage(1); // Reset to first page when searching
  }, []);

  const clearSearch = useCallback(() => {
    setSearch("");
    setPage(1);
  }, []);

  // Sorting methods
  const updateSort = useCallback(
    (field, order = null) => {
      if (sortBy === field) {
        // Toggle sort order if same field
        setSortOrder(order || (sortOrder === "asc" ? "desc" : "asc"));
      } else {
        // Set new field with provided order or default to 'desc'
        setSortBy(field);
        setSortOrder(order || "desc");
      }
      setPage(1); // Reset to first page when sorting
    },
    [sortBy, sortOrder],
  );

  const clearSort = useCallback(() => {
    setSortBy("");
    setSortOrder("desc");
    setPage(1);
  }, []);

  // Filter methods
  const updateFilter = useCallback((key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
    setPage(1); // Reset to first page when filtering
  }, []);

  const updateFilters = useCallback((newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filtering
  }, []);

  const clearFilter = useCallback((key) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
    setPage(1);
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setPage(1);
  }, []);

  // Reset all pagination state
  const reset = useCallback(() => {
    setPage(initialPage);
    setLimit(initialLimit);
    setSearch(initialSearch);
    setSortBy(initialSortBy);
    setSortOrder(initialSortOrder);
    setFilters(initialFilters);
    setTotalItems(0);
    setTotalPages(0);
    setError("");
  }, [
    initialPage,
    initialLimit,
    initialSearch,
    initialSortBy,
    initialSortOrder,
    initialFilters,
  ]);

  // Helper method to check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return (
      search.trim() !== "" ||
      Object.keys(filters).some(
        (key) =>
          filters[key] !== "" &&
          filters[key] !== null &&
          filters[key] !== undefined,
      )
    );
  }, [search, filters]);

  return {
    // State
    page,
    limit,
    totalItems,
    totalPages,
    search,
    sortBy,
    sortOrder,
    filters,
    loading,
    error,

    // Computed values
    paginationInfo,
    hasActiveFilters,

    // Methods
    getQueryParams,
    updatePagination,

    // Page navigation
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToNextPage,
    goToPrevPage,
    changeLimit,

    // Search
    updateSearch,
    clearSearch,

    // Sorting
    updateSort,
    clearSort,

    // Filtering
    updateFilter,
    updateFilters,
    clearFilter,
    clearAllFilters,

    // State setters
    setLoading,
    setError,
    setPage,
    setLimit,
    setSearch,
    setSortBy,
    setSortOrder,
    setFilters,

    // Reset
    reset,
  };
};
