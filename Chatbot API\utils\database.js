import supabase from "./supabase.js";

/**
 * Get customer configuration by authId
 * @param {string} authId - The auth ID of the customer
 * @returns {Promise<Object>} Customer configuration data
 */
export async function getCustomerConfig(authId) {
  const { data, error } = await supabase
    .from("whatsapp_customers")
    .select("*")
    .eq("auth_id", authId)
    .eq("is_active", true)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new Error("Customer configuration not found");
    }
    throw error;
  }

  return data;
}

/**
 * Find customer by phone number ID
 * @param {string} phoneNumberId - The WhatsApp phone number ID
 * @returns {Promise<Object|null>} Customer data or null if not found
 */
export async function findCustomerByPhoneNumber(phoneNumberId) {
  const { data, error } = await supabase
    .from("whatsapp_customers")
    .select("*")
    .eq("whatsapp_phone_number_id", phoneNumberId)
    .eq("is_active", true)
    .single();

  if (error && error.code !== "PGRST116") {
    throw error;
  }

  return data;
}

/**
 * Check user quotas
 * @param {string} authId - The auth ID of the user
 * @returns {Promise<Object>} Quota information
 */
export async function checkUserQuotas(authId) {
  const { data, error } = await supabase.rpc("check_user_quotas", {
    p_auth_id: authId,
  });

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Log quota violation
 * @param {Object} violationData - Violation data
 * @returns {Promise<void>}
 */
export async function logQuotaViolation(violationData) {
  const { error } = await supabase.from("quota_violations").insert(violationData);
  
  if (error) {
    throw error;
  }
}

/**
 * Upsert contact information
 * @param {string} authId - The auth ID
 * @param {string} phoneNumber - The phone number
 * @returns {Promise<void>}
 */
export async function upsertContact(authId, phoneNumber) {
  const { error } = await supabase.rpc("upsert_contact", {
    p_auth_id: authId,
    p_phone_number: phoneNumber,
  });

  if (error) {
    throw error;
  }
}

/**
 * Get user profile by auth ID
 * @param {string} authId - The auth ID
 * @returns {Promise<Object>} User profile data
 */
export async function getUserProfile(authId) {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("auth_id", authId)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      throw new Error("User profile not found");
    }
    throw error;
  }

  return data;
}

/**
 * Get active products for a user
 * @param {string} authId - The auth ID
 * @returns {Promise<Array>} Array of active products
 */
export async function getActiveProducts(authId) {
  const { data, error } = await supabase
    .from("product_catalog")
    .select("*")
    .eq("auth_id", authId)
    .eq("is_active", true)
    .order("category", { ascending: true })
    .order("name", { ascending: true });

  if (error) {
    throw error;
  }

  return data || [];
}
