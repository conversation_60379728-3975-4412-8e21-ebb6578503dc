import axios from "axios";
import crypto from "crypto";
import { supabase } from "./supabase.js";
import logger from "./logger.js";

/**
 * Meta OAuth utility functions for WhatsApp, Messenger, and Instagram integration
 */

// OAuth scopes for each platform (WhatsApp excluded - uses system tokens)
const PLATFORM_SCOPES = {
  messenger: [
    'pages_messaging',
    'pages_manage_metadata',
    'pages_read_engagement'
  ],
  instagram: [
    'instagram_basic',
    'instagram_manage_messages',
    'pages_read_engagement'
  ]
};

/**
 * Generate OAuth authorization URL for Meta platforms
 */
export async function generateOAuthURL(authId, platform, redirectUri) {
  try {
    // Generate secure state token
    const stateToken = crypto.randomBytes(32).toString('hex');
    
    // Store state in database for security
    const { error: stateError } = await supabase
      .from('oauth_states')
      .insert({
        state_token: stateToken,
        auth_id: authId,
        platform: platform,
        redirect_uri: redirectUri,
      });

    if (stateError) {
      logger.error('Error storing OAuth state:', stateError);
      throw new Error('Failed to generate OAuth URL');
    }

    // Get platform-specific scopes
    const scopes = PLATFORM_SCOPES[platform] || [];
    
    // Build OAuth URL
    const params = new URLSearchParams({
      client_id: process.env.META_APP_ID,
      redirect_uri: redirectUri,
      state: stateToken,
      scope: scopes.join(','),
      response_type: 'code',
    });

    const oauthUrl = `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;
    
    return {
      success: true,
      url: oauthUrl,
      state: stateToken,
    };
  } catch (error) {
    logger.error('Error generating OAuth URL:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Exchange authorization code for access token
 */
export async function exchangeCodeForToken(code, state, redirectUri) {
  try {
    // Verify and get state information
    const { data: stateData, error: stateError } = await supabase
      .from('oauth_states')
      .select('*')
      .eq('state_token', state)
      .eq('used', false)
      .single();

    if (stateError || !stateData) {
      throw new Error('Invalid or expired OAuth state');
    }

    // Check if state has expired
    if (new Date(stateData.expires_at) < new Date()) {
      throw new Error('OAuth state has expired');
    }

    // Mark state as used
    await supabase
      .from('oauth_states')
      .update({ used: true })
      .eq('state_token', state);

    // Exchange code for token
    const tokenResponse = await axios.post('https://graph.facebook.com/v18.0/oauth/access_token', {
      client_id: process.env.META_APP_ID,
      client_secret: process.env.META_APP_SECRET,
      redirect_uri: redirectUri,
      code: code,
    });

    const { access_token, token_type, expires_in } = tokenResponse.data;

    // Get user information
    const userResponse = await axios.get('https://graph.facebook.com/v18.0/me', {
      params: {
        access_token: access_token,
        fields: 'id,name,email',
      },
    });

    const userData = userResponse.data;

    // Calculate token expiration
    const expiresAt = expires_in 
      ? new Date(Date.now() + expires_in * 1000)
      : null;

    return {
      success: true,
      tokenData: {
        access_token,
        token_type: token_type || 'Bearer',
        expires_at: expiresAt,
        user_id: userData.id,
        user_name: userData.name,
        user_email: userData.email,
      },
      stateData,
    };
  } catch (error) {
    logger.error('Error exchanging code for token:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Get long-lived access token from short-lived token
 */
export async function getLongLivedToken(shortLivedToken) {
  try {
    const response = await axios.get('https://graph.facebook.com/v18.0/oauth/access_token', {
      params: {
        grant_type: 'fb_exchange_token',
        client_id: process.env.META_APP_ID,
        client_secret: process.env.META_APP_SECRET,
        fb_exchange_token: shortLivedToken,
      },
    });

    const { access_token, expires_in } = response.data;
    
    const expiresAt = expires_in 
      ? new Date(Date.now() + expires_in * 1000)
      : null;

    return {
      success: true,
      access_token,
      expires_at: expiresAt,
    };
  } catch (error) {
    logger.error('Error getting long-lived token:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Get user's pages (for Messenger integration)
 */
export async function getUserPages(accessToken) {
  try {
    const response = await axios.get('https://graph.facebook.com/v18.0/me/accounts', {
      params: {
        access_token: accessToken,
        fields: 'id,name,access_token,category,tasks',
      },
    });

    return {
      success: true,
      pages: response.data.data,
    };
  } catch (error) {
    logger.error('Error getting user pages:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Get user's Instagram accounts
 */
export async function getUserInstagramAccounts(accessToken) {
  try {
    // First get user's pages
    const pagesResult = await getUserPages(accessToken);
    if (!pagesResult.success) {
      return pagesResult;
    }

    const instagramAccounts = [];

    // Get Instagram accounts for each page
    for (const page of pagesResult.pages) {
      try {
        const response = await axios.get(`https://graph.facebook.com/v18.0/${page.id}`, {
          params: {
            access_token: page.access_token,
            fields: 'instagram_business_account',
          },
        });

        if (response.data.instagram_business_account) {
          const igAccountId = response.data.instagram_business_account.id;
          
          // Get Instagram account details
          const igResponse = await axios.get(`https://graph.facebook.com/v18.0/${igAccountId}`, {
            params: {
              access_token: page.access_token,
              fields: 'id,username,name,profile_picture_url',
            },
          });

          instagramAccounts.push({
            ...igResponse.data,
            page_id: page.id,
            page_name: page.name,
            page_access_token: page.access_token,
          });
        }
      } catch (error) {
        logger.warn(`Error getting Instagram account for page ${page.id}:`, error.message);
      }
    }

    return {
      success: true,
      accounts: instagramAccounts,
    };
  } catch (error) {
    logger.error('Error getting Instagram accounts:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Get user's WhatsApp Business accounts
 */
export async function getUserWhatsAppAccounts(accessToken) {
  try {
    const response = await axios.get('https://graph.facebook.com/v18.0/me/businesses', {
      params: {
        access_token: accessToken,
        fields: 'id,name',
      },
    });

    const whatsappAccounts = [];

    // Get WhatsApp Business accounts for each business
    for (const business of response.data.data) {
      try {
        const wabResponse = await axios.get(`https://graph.facebook.com/v18.0/${business.id}/owned_whatsapp_business_accounts`, {
          params: {
            access_token: accessToken,
            fields: 'id,name,currency,timezone_offset_minutes',
          },
        });

        for (const waba of wabResponse.data.data) {
          // Get phone numbers for this WABA
          const phoneResponse = await axios.get(`https://graph.facebook.com/v18.0/${waba.id}/phone_numbers`, {
            params: {
              access_token: accessToken,
              fields: 'id,display_phone_number,verified_name,quality_rating',
            },
          });

          whatsappAccounts.push({
            ...waba,
            business_id: business.id,
            business_name: business.name,
            phone_numbers: phoneResponse.data.data,
          });
        }
      } catch (error) {
        logger.warn(`Error getting WhatsApp accounts for business ${business.id}:`, error.message);
      }
    }

    return {
      success: true,
      accounts: whatsappAccounts,
    };
  } catch (error) {
    logger.error('Error getting WhatsApp accounts:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Refresh access token
 */
export async function refreshAccessToken(refreshToken) {
  try {
    const response = await axios.post('https://graph.facebook.com/v18.0/oauth/access_token', {
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      client_id: process.env.META_APP_ID,
      client_secret: process.env.META_APP_SECRET,
    });

    const { access_token, expires_in } = response.data;
    
    const expiresAt = expires_in 
      ? new Date(Date.now() + expires_in * 1000)
      : null;

    return {
      success: true,
      access_token,
      expires_at: expiresAt,
    };
  } catch (error) {
    logger.error('Error refreshing access token:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}
