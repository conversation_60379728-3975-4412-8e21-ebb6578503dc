# WhatsApp Chatbot SaaS - Complete Business Solution

A comprehensive WhatsApp chatbot SaaS platform that integrates AI, order processing, and business automation.

## 🚀 Features

### Core Functionality

- 🤖 **AI-Powered Conversations**: OpenAI GPT-4 with context-aware responses
- 🧠 **Vector Knowledge Base**: Intelligent information retrieval using embeddings
- 💬 **Conversation Memory**: Maintains context across multiple messages
- 📱 **WhatsApp Business API**: Full Meta Cloud API integration
- 🔐 **Multi-tenant Architecture**: Supports unlimited customers

### Business Features

- 🛍️ **Smart Order Processing**: Multi-step order workflow with AI assistance
- 📊 **Google Sheets Integration**: Automatic order data synchronization
- 📈 **Analytics & Statistics**: Comprehensive reporting and insights
- 🎯 **Intelligent Product Search**: AI-powered product matching
- 🌐 **Multi-language Support**: English, Bahasa Malaysia, Chinese
- 📋 **Customer Management**: Contact tracking and interaction history
- 🔄 **Auto Follow-up System**: Automatically enabled for all contacts with intelligent follow-ups

### Advanced Capabilities

- ⚡ **Real-time Processing**: Instant message handling and responses
- 🔄 **Workflow Automation**: Customizable order and inquiry processes
- 📱 **Mobile-First Design**: Optimized for WhatsApp interface
- 🛡️ **Enterprise Security**: Row-level security and data isolation
- 📊 **Flexible Reporting**: Daily, monthly, and custom analytics
- 🔧 **Easy Configuration**: Simple setup and customization

## 📋 Prerequisites

1. **Node.js** (v16 or higher)
2. **Supabase** account with PostgreSQL database
3. **OpenAI** API key
4. **Meta WhatsApp Business** account and API access
5. **Google Cloud** service account (for Sheets integration)

## 🛠️ Quick Setup

### 1. Installation

```bash
git clone <your-repository>
cd chatbot-api
npm install
```

### 2. Environment Configuration

Create a `.env` file:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# WhatsApp Webhook Verification
VERIFY_TOKEN=your_custom_verification_token

# Server Configuration
PORT=3000
```

### 3. Database Setup

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database_schema.sql`
4. Execute the SQL to create all tables and functions
5. Enable the `pgvector` extension in Settings > Extensions

### 4. Start the Server

```bash
npm start
```

The server will start on `http://localhost:3000`

## 🔧 WhatsApp Business API Setup

### Get Your Credentials

1. Create a Meta Developer account at [developers.facebook.com](https://developers.facebook.com/)
2. Create a new Business app
3. Add WhatsApp product to your app
4. Get the following credentials:
   - Business Account ID
   - Phone Number ID
   - Access Token

### Configure Webhook

Set webhook URL to: `https://your-domain.com/api/whatsapp/webhook`

## 📱 Social Media Integrations

### WhatsApp Business API

Register your customer's WhatsApp Business:

```http
POST /api/ai/whatsapp/register-customer
Content-Type: application/json
x-auth-id: user_123

{
  "whatsappBusinessAccountId": "your_business_account_id",
  "whatsappPhoneNumberId": "your_phone_number_id",
  "systemAccessToken": "your_access_token"
}
```

### Messenger Integration

Register your Facebook Messenger integration:

```http
POST /api/ai/messenger/register
Content-Type: application/json
x-auth-id: user_123

{
  "messengerPageId": "your_page_id",
  "messengerAccessToken": "your_page_access_token",
  "messengerAppId": "your_app_id"
}
```

### Instagram Integration

Register your Instagram integration:

```http
POST /api/ai/instagram/register
Content-Type: application/json
x-auth-id: user_123

{
  "instagramAccountId": "your_instagram_account_id",
  "instagramAccessToken": "your_access_token"
}
```

## 🔗 Google Services Integration

### Google Sheets

Configure Google Sheets for order tracking:

```http
POST /api/ai/google/sheets
Content-Type: application/json
x-auth-id: user_123

{
  "spreadsheetUrl": "https://docs.google.com/spreadsheets/d/your-sheet-id",
  "worksheetName": "Orders",
  "spreadsheetColumns": [
    "Date", "Customer Phone", "Customer Name",
    "Product", "Variation", "Quantity", "Total Amount", "Status"
  ]
}
```

### Google Calendar

Configure Google Calendar for appointment management:

```http
POST /api/ai/google/calendar
Content-Type: application/json
x-auth-id: user_123

{
  "calendarId": "<EMAIL>",
  "calendarName": "Business Calendar"
}
```
```

## 🛍️ Product Management

### Add Products

```http
POST /api/products/add-product
Content-Type: application/json

{
  "authId": "user_123",
  "name": "iPhone 15 Pro",
  "description": "Latest iPhone with A17 Pro chip",
  "price": 4999.00,
  "variations": ["Natural Titanium", "Blue Titanium", "White Titanium"],
  "category": "Smartphones",
  "stockQuantity": 50
}
```

### Get Formatted Product List

```http
GET /api/products/chatbot-list?authId=user_123
```

## 🤖 Knowledge Base

### Add Knowledge

```http
POST /api/ai/add-knowledge
Content-Type: application/json

{
  "authId": "user_123",
  "title": "Store Hours",
  "content": "Open Mon-Fri 9AM-6PM, Sat 10AM-4PM, closed Sunday. 24/7 WhatsApp support available."
}
```

### Chat with AI

```http
POST /api/ai/chat
Content-Type: application/json

{
  "authId": "user_123",
  "phoneNumber": "+***********",
  "prompt": "What are your opening hours?"
}
```

## 🔄 Order Processing Workflow

The system automatically handles complete order workflows:

1. **Product Selection**: Customer expresses buying intent
2. **Product Matching**: AI matches customer request to products
3. **Variation Selection**: Choose colors, sizes, etc.
4. **Quantity Selection**: Specify how many items
5. **Customer Details**: Collect customer information
6. **Order Confirmation**: Review and confirm order
7. **Google Sheets Save**: Automatically save to spreadsheet
8. **Order Tracking**: Monitor order status and statistics
9. **Auto Follow-up**: Intelligent follow-ups for interested customers

### Example Flow:

```
Customer: "I want to buy iPhone"
Bot: Shows product list
Customer: "iPhone 15 Pro"
Bot: Shows color options
Customer: "Blue"
Bot: "How many would you like?"
Customer: "2"
Bot: "Please provide your full name"
Customer: "John Doe"
Bot: Shows order summary
Customer: "CONFIRM"
Bot: "Order confirmed! Saved to your Google Sheet."
```

## 📊 Google Sheets Integration

### Setup Service Account

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create project and enable Google Sheets API
3. Create service account and download JSON key
4. Share your Google Sheet with the service account email

### Column Mapping

The system supports flexible column mapping:

- **Date**: Order date
- **Customer Phone**: Customer's phone number
- **Customer Name**: Customer's full name
- **Product**: Product name
- **Variation**: Product variation (color, size, etc.)
- **Quantity**: Order quantity
- **Total Amount**: Total price
- **Status**: Order status

## 🔄 Auto Follow-up System

### Automatic Enablement

The follow-up system is **automatically enabled** for all new contacts when they first message your chatbot.

### Default Configuration

**Interested Customer Follow-up**: ✅ **Enabled by Default**
- Triggers when AI detects customer interest in products/services
- Delay: 4 hours after conversation
- Max follow-ups: 1 per customer
- Message: Friendly follow-up asking if they need more help

**Other Follow-up Types**: ❌ **Disabled by Default**
- Booking reminders: Can be enabled for appointment-based businesses
- Order follow-ups: Can be enabled for order confirmations
- Custom follow-ups: Fully customizable rules

### Smart Features

- **24-hour WhatsApp Compliance**: Respects WhatsApp's messaging window
- **AI Intent Detection**: Automatically categorizes customer conversations
- **Duplicate Prevention**: Won't send multiple follow-ups unnecessarily
- **Multi-language Support**: Works with English, Malay, and Chinese

## 📈 Analytics & Reports

### Order Statistics

```http
GET /api/whatsapp/order-stats?authId=user_123&period=daily
```

### Message Statistics

```http
GET /api/ai/list-knowledge?authId=user_123
```

## 🎯 Advanced Features

### Intelligent Product Search

```http
POST /api/products/search
Content-Type: application/json

{
  "authId": "user_123",
  "query": "red phone with good camera"
}
```

### Multi-step Order Processing

```http
POST /api/products/process-order-step
Content-Type: application/json

{
  "authId": "user_123",
  "customerPhone": "+***********",
  "message": "iPhone 15 Pro",
  "currentStep": "product_selection"
}
```

## 🔧 API Endpoints

### Authentication

- `POST /api/auth/signup` - Register new user
- `POST /api/auth/signin` - User login
- `GET /api/auth/user` - Get current user

### WhatsApp Integration

- `GET /api/whatsapp/webhook` - Webhook verification
- `POST /api/whatsapp/webhook` - Receive messages
- `POST /api/whatsapp/register-customer` - Register customer
- `GET /api/whatsapp/customer-config` - Get customer config
- `PUT /api/whatsapp/customer-config` - Update customer config
- `GET /api/whatsapp/order-stats` - Get order statistics

### AI & Knowledge Base

- `POST /api/ai/chat` - Chat with AI
- `POST /api/ai/add-knowledge` - Add knowledge entry
- `PUT /api/ai/update-knowledge` - Update knowledge entry
- `DELETE /api/ai/delete-knowledge` - Delete knowledge entry
- `GET /api/ai/list-knowledge` - List all knowledge entries
- `GET /api/ai/chatbot-settings` - Get chatbot settings
- `PUT /api/ai/chatbot-settings` - Update chatbot settings

### Product Management

- `POST /api/products/add-product` - Add product
- `GET /api/products/list` - List products
- `PUT /api/products/update` - Update product
- `DELETE /api/products/delete` - Delete product
- `GET /api/products/chatbot-list` - Get formatted product list
- `POST /api/products/search` - Intelligent product search
- `POST /api/products/process-order-step` - Process order step
- `GET /api/products/order-session` - Get order session
- `POST /api/products/cancel-order` - Cancel order
- `GET /api/products/orders` - Get order records

## 🧪 Testing

Run the comprehensive test suite:

```bash
node test_whatsapp_saas.js
```

Test individual components:

```bash
# Test authentication
node -e "require('./test_whatsapp_saas.js').testComponent('auth')"

# Test product search
node -e "require('./test_whatsapp_saas.js').testComponent('search')"

# Test order processing
node -e "require('./test_whatsapp_saas.js').testComponent('orders')"
```

## 🚀 Production Deployment

### 1. Environment Setup

```env
NODE_ENV=production
PORT=443
# Add SSL certificates and production URLs
```

### 2. Process Management

```bash
npm install -g pm2
pm2 start index.js --name "whatsapp-chatbot"
pm2 save
pm2 startup
```

### 3. SSL & Domain

- Configure SSL certificates
- Set up domain pointing to your server
- Update webhook URL in Meta Developer Console

## 🔍 Troubleshooting

### Common Issues

**Webhook not receiving messages:**

- Verify webhook URL is accessible
- Check VERIFY_TOKEN matches
- Ensure Meta app permissions are correct

**Google Sheets not updating:**

- Verify service account permissions
- Check spreadsheet ID and worksheet name
- Ensure service account has editor access

**AI responses not working:**

- Check OpenAI API key
- Verify Supabase connection
- Ensure knowledge base has content

**Order process stuck:**

- Check order session timeout (30 minutes)
- Verify product catalog has items
- Check session data in database

## 📞 Support & Maintenance

### Regular Tasks

- Monitor token usage and costs
- Update product catalogs
- Review order statistics
- Clean old chat history (automated)
- Backup customer data

### Performance Monitoring

- Response times
- Error rates
- Message volume
- Order conversion rates
- Customer satisfaction

## 🎉 Success!

Your WhatsApp chatbot SaaS is now ready to:

✅ Handle customer inquiries intelligently  
✅ Process orders step-by-step  
✅ Save data to Google Sheets automatically  
✅ Provide analytics and insights  
✅ Scale to unlimited customers  
✅ Support multiple languages  
✅ Integrate with existing business workflows

## 📚 Documentation

- [Complete Setup Tutorial](WHATSAPP_SETUP_TUTORIAL.md)
- [API Documentation](API_DOCS.md)
- [Database Schema](database_schema.sql)
- [Test Examples](test_whatsapp_saas.js)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🌟 Star this project if it helps your business!
