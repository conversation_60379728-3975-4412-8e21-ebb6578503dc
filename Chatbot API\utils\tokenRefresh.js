import { supabase } from "./supabase.js";
import { refreshAccessToken } from "./metaOAuth.js";
import logger from "./logger.js";

/**
 * Token Refresh System
 * Handles automatic token refresh for Meta OAuth integrations
 */

/**
 * Check and refresh expired tokens for all users
 */
export async function refreshExpiredTokens() {
  try {
    logger.info("Starting token refresh check...");

    // Get all integrations with OAuth tokens that are about to expire (within 24 hours)
    const expirationThreshold = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    
    const { data: integrations, error } = await supabase
      .from('social_media_integrations')
      .select('*')
      .eq('oauth_connected', true)
      .not('refresh_token', 'is', null)
      .lt('token_expires_at', expirationThreshold.toISOString())
      .eq('connection_status', 'connected');

    if (error) {
      logger.error("Error fetching integrations for token refresh:", error);
      return { success: false, error: error.message };
    }

    if (!integrations || integrations.length === 0) {
      logger.info("No tokens need refreshing");
      return { success: true, refreshed: 0 };
    }

    logger.info(`Found ${integrations.length} integrations with tokens needing refresh`);

    let refreshedCount = 0;
    let errorCount = 0;

    // Process each integration
    for (const integration of integrations) {
      try {
        const result = await refreshIntegrationToken(integration);
        if (result.success) {
          refreshedCount++;
          logger.info(`Successfully refreshed token for ${integration.platform} integration (user: ${integration.auth_id})`);
        } else {
          errorCount++;
          logger.error(`Failed to refresh token for ${integration.platform} integration (user: ${integration.auth_id}):`, result.error);
        }
      } catch (error) {
        errorCount++;
        logger.error(`Error refreshing token for ${integration.platform} integration (user: ${integration.auth_id}):`, error);
      }
    }

    logger.info(`Token refresh completed. Refreshed: ${refreshedCount}, Errors: ${errorCount}`);

    return {
      success: true,
      refreshed: refreshedCount,
      errors: errorCount,
      total: integrations.length,
    };
  } catch (error) {
    logger.error("Error in refreshExpiredTokens:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Refresh token for a specific integration
 */
export async function refreshIntegrationToken(integration) {
  try {
    if (!integration.refresh_token) {
      return { success: false, error: "No refresh token available" };
    }

    // Attempt to refresh the token
    const refreshResult = await refreshAccessToken(integration.refresh_token);

    if (!refreshResult.success) {
      // Mark integration as having token issues
      await supabase
        .from('social_media_integrations')
        .update({
          connection_status: 'expired',
          last_error_message: refreshResult.error,
          updated_at: new Date().toISOString(),
        })
        .eq('id', integration.id);

      return { success: false, error: refreshResult.error };
    }

    // Update integration with new token
    const { error: updateError } = await supabase
      .from('social_media_integrations')
      .update({
        access_token: refreshResult.access_token,
        token_expires_at: refreshResult.expires_at,
        connection_status: 'connected',
        last_error_message: null,
        last_sync_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', integration.id);

    if (updateError) {
      logger.error("Error updating integration with new token:", updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    logger.error("Error in refreshIntegrationToken:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Check if a token is expired or about to expire
 */
export function isTokenExpired(expiresAt, bufferMinutes = 60) {
  if (!expiresAt) {
    return false; // No expiration date means it doesn't expire
  }

  const expirationTime = new Date(expiresAt);
  const bufferTime = new Date(Date.now() + bufferMinutes * 60 * 1000);

  return expirationTime <= bufferTime;
}

/**
 * Get token status for an integration
 */
export async function getTokenStatus(authId, platform) {
  try {
    const { data: integration, error } = await supabase
      .from('social_media_integrations')
      .select('*')
      .eq('auth_id', authId)
      .eq('platform', platform)
      .single();

    if (error || !integration) {
      return {
        status: 'not_found',
        message: 'Integration not found',
      };
    }

    if (!integration.oauth_connected) {
      return {
        status: 'not_connected',
        message: 'OAuth not connected',
      };
    }

    if (!integration.access_token) {
      return {
        status: 'no_token',
        message: 'No access token available',
      };
    }

    if (isTokenExpired(integration.token_expires_at)) {
      return {
        status: 'expired',
        message: 'Token has expired',
        expires_at: integration.token_expires_at,
      };
    }

    if (isTokenExpired(integration.token_expires_at, 24 * 60)) { // 24 hours
      return {
        status: 'expiring_soon',
        message: 'Token expires within 24 hours',
        expires_at: integration.token_expires_at,
      };
    }

    return {
      status: 'valid',
      message: 'Token is valid',
      expires_at: integration.token_expires_at,
    };
  } catch (error) {
    logger.error("Error checking token status:", error);
    return {
      status: 'error',
      message: error.message,
    };
  }
}

/**
 * Refresh token for a specific user and platform
 */
export async function refreshUserToken(authId, platform) {
  try {
    const { data: integration, error } = await supabase
      .from('social_media_integrations')
      .select('*')
      .eq('auth_id', authId)
      .eq('platform', platform)
      .single();

    if (error || !integration) {
      return { success: false, error: 'Integration not found' };
    }

    return await refreshIntegrationToken(integration);
  } catch (error) {
    logger.error("Error in refreshUserToken:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Schedule token refresh check (to be called by a cron job or scheduler)
 */
export async function scheduleTokenRefresh() {
  try {
    // This function would typically be called by a cron job
    // For now, we'll just run the refresh check
    const result = await refreshExpiredTokens();
    
    // Log the results
    if (result.success) {
      logger.info(`Scheduled token refresh completed: ${result.refreshed} tokens refreshed, ${result.errors || 0} errors`);
    } else {
      logger.error("Scheduled token refresh failed:", result.error);
    }

    return result;
  } catch (error) {
    logger.error("Error in scheduleTokenRefresh:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Clean up expired OAuth states (security cleanup)
 */
export async function cleanupExpiredOAuthStates() {
  try {
    const { error } = await supabase
      .from('oauth_states')
      .delete()
      .or('expires_at.lt.now(),used.eq.true');

    if (error) {
      logger.error("Error cleaning up OAuth states:", error);
      return { success: false, error: error.message };
    }

    logger.info("OAuth states cleanup completed");
    return { success: true };
  } catch (error) {
    logger.error("Error in cleanupExpiredOAuthStates:", error);
    return { success: false, error: error.message };
  }
}
