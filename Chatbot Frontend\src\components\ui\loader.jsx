import React from "react";
import { Loader2 } from "lucide-react";
import { cn } from "../../lib/utils";

const Loader = ({
  size = "md",
  variant = "primary",
  className = "",
  showText = false,
  text = "Loading...",
  ...props
}) => {
  const sizeClasses = {
    xs: "w-3 h-3",
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12",
    "2xl": "w-16 h-16",
  };

  const variantClasses = {
    primary: "text-blue-600",
    secondary: "text-gray-600",
    white: "text-white",
    muted: "text-gray-400",
  };

  return (
    <div className={cn("flex items-center gap-2", className)} {...props}>
      <Loader2
        className={cn(
          "animate-spin",
          sizeClasses[size],
          variantClasses[variant],
        )}
      />
      {showText && (
        <span className={cn("text-sm font-medium", variantClasses[variant])}>
          {text}
        </span>
      )}
    </div>
  );
};

// Spinning wheel loader using CSS borders (alternative style)
const SpinLoader = ({
  size = "md",
  variant = "primary",
  className = "",
  ...props
}) => {
  const sizeClasses = {
    xs: "w-3 h-3 border",
    sm: "w-4 h-4 border-2",
    md: "w-6 h-6 border-2",
    lg: "w-8 h-8 border-2",
    xl: "w-12 h-12 border-2",
    "2xl": "w-16 h-16 border-4",
  };

  const variantClasses = {
    primary: "border-blue-200 border-t-blue-600",
    secondary: "border-gray-200 border-t-gray-600",
    white: "border-white/30 border-t-white",
    muted: "border-gray-300 border-t-gray-500",
  };

  return (
    <div
      className={cn(
        "rounded-full animate-spin",
        sizeClasses[size],
        variantClasses[variant],
        className,
      )}
      {...props}
    />
  );
};

// Full page loader component
const FullPageLoader = ({
  text = "Loading...",
  size = "xl",
  variant = "primary",
  className = "",
}) => {
  return (
    <div
      className={cn(
        "min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <Loader size={size} variant={variant} />
        <p className="text-gray-600 font-medium text-lg">{text}</p>
      </div>
    </div>
  );
};

// Inline loader for buttons and small spaces
const InlineLoader = ({ size = "sm", variant = "white", className = "" }) => {
  return <SpinLoader size={size} variant={variant} className={className} />;
};

// Center loader for cards and sections
const CenterLoader = ({
  size = "lg",
  variant = "primary",
  text,
  className = "",
}) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center py-8",
        className,
      )}
    >
      <Loader size={size} variant={variant} showText={!!text} text={text} />
    </div>
  );
};

export { Loader, SpinLoader, FullPageLoader, InlineLoader, CenterLoader };
export default Loader;
