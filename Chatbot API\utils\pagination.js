// Shared pagination utility functions

// Helper function for pagination
export function getPaginationParams(query) {
  const limit = Math.min(parseInt(query.limit) || 50, 100); // Max 100 items per page
  const page = Math.max(parseInt(query.page) || 1, 1); // Page numbers start from 1
  const offset = (page - 1) * limit;

  return {
    limit,
    page,
    offset,
  };
}

// Helper function to build pagination response
export function buildPaginatedResponse(data, total, page, limit) {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  return {
    success: true,
    data: data || [],
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : null,
      prevPage: hasPrev ? page - 1 : null,
    },
  };
}