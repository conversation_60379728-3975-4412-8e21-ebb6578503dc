import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useAlertMigration } from "../hooks/useAlertMigration";
import { InlineLoader } from "../components/ui/loader";
import Icon from "../components/ui/icon";

export default function NewPassword() {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [accessToken, setAccessToken] = useState("");
  const [refreshToken, setRefreshToken] = useState("");
  const [tokensLoaded, setTokensLoaded] = useState(false);
  const navigate = useNavigate();
  const { forgotPassword } = useAuth();
  const { showError } = useAlertMigration();

  // Extract tokens from URL hash fragment
  useEffect(() => {
    const hash = window.location.hash.substring(1); // Remove the # character
    const params = new URLSearchParams(hash);

    const token = params.get("access_token");
    const refresh = params.get("refresh_token");

    if (token) {
      setAccessToken(token);
    }
    if (refresh) {
      setRefreshToken(refresh);
    }

    // Mark that we've finished loading tokens
    setTokensLoaded(true);
  }, []);

  useEffect(() => {
    // Only check for tokens after we've attempted to load them
    if (tokensLoaded && !accessToken) {
      showError(
        "Invalid or missing reset link. Please request a new password reset.",
      );
    }
  }, [accessToken, tokensLoaded, showError]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Clear previous errors - not needed with global alerts

    // Validation
    if (newPassword.length < 6) {
      showError("Password must be at least 6 characters long");
      return;
    }

    if (newPassword !== confirmPassword) {
      showError("Passwords do not match");
      return;
    }

    if (!accessToken) {
      showError("Invalid reset link. Please request a new password reset.");
      return;
    }

    setLoading(true);

    try {
      await forgotPassword(newPassword, accessToken, refreshToken);
      setSuccess(true);
      // Redirect to login after success
      setTimeout(() => {
        navigate("/login");
      }, 3000);
    } catch (err) {
      showError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        {/* Success State */}
        {success ? (
          <div className="fade-in">
            {/* Success Header */}
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <Icon name="CheckCircle" className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Password reset complete!
              </h2>
              <p className="text-gray-600">
                Your password has been successfully updated
              </p>
            </div>

            {/* Success Card */}
            <div className="card">
              <div className="text-center space-y-6">
                <div className="bg-green-50 rounded-2xl p-6">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Icon
                      name="CheckCircle"
                      className="w-5 h-5 text-green-600"
                    />
                    <span className="text-green-800 font-semibold">
                      Password updated successfully!
                    </span>
                  </div>
                  <p className="text-green-700 text-sm leading-relaxed">
                    You can now sign in to your account using your new password.
                    You'll be redirected to the login page in a few seconds.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-3">
                    <Link
                      to="/login"
                      className="btn-primary w-full py-3 text-center block"
                    >
                      Sign In to Your Account
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Form State */
          <div className="fade-in">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <Icon name="Lock" className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Create new password
              </h2>
              <p className="text-gray-600 leading-relaxed">
                Enter your new password below to complete the reset process
              </p>
            </div>

            {/* Form Card */}
            <div className="card">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* New Password Input */}
                <div>
                  <label
                    htmlFor="newPassword"
                    className="block text-sm font-semibold text-gray-700 mb-3"
                  >
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      id="newPassword"
                      type="password"
                      className="input-field"
                      style={{ paddingLeft: "3rem" }}
                      placeholder="Enter your new password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      required
                      minLength={6}
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                      <Icon name="Lock" className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Confirm Password Input */}
                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-sm font-semibold text-gray-700 mb-3"
                  >
                    Confirm Password
                  </label>
                  <div className="relative">
                    <input
                      id="confirmPassword"
                      type="password"
                      className="input-field"
                      style={{ paddingLeft: "3rem" }}
                      placeholder="Confirm your new password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      minLength={6}
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                      <Icon name="Lock" className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Password Requirements */}
                <div className="bg-blue-50 rounded-2xl p-4">
                  <div className="flex items-start gap-3">
                    <Icon
                      name="Lock"
                      className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5"
                    />
                    <div className="text-sm text-blue-700">
                      <p className="font-medium mb-1">Password requirements:</p>
                      <ul className="space-y-1">
                        <li className="flex items-center gap-2">
                          <span
                            className={`w-1.5 h-1.5 rounded-full ${newPassword.length >= 6 ? "bg-green-500" : "bg-gray-400"}`}
                          ></span>
                          <span>At least 6 characters long</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span
                            className={`w-1.5 h-1.5 rounded-full ${newPassword === confirmPassword && newPassword.length > 0 ? "bg-green-500" : "bg-gray-400"}`}
                          ></span>
                          <span>Passwords must match</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Error messages now handled by global AlertContainer */}

                {/* Submit Button */}
                <button
                  type="submit"
                  className="btn-primary w-full text-lg py-4 relative overflow-hidden"
                  disabled={loading || !accessToken}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-3">
                      <InlineLoader size="sm" />
                      <span>Updating password...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <Icon name="Lock" className="w-5 h-5" />
                      <span>Update password</span>
                    </div>
                  )}
                </button>
              </form>
            </div>

            {/* Links */}
            <div className="mt-8 text-center space-y-4">
              <p className="text-gray-600">
                Remember your password?{" "}
                <Link
                  to="/login"
                  className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200 hover:underline"
                >
                  Sign in here
                </Link>
              </p>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 px-4 text-gray-500">
                    or
                  </span>
                </div>
              </div>

              <p className="text-gray-600">
                Need a new reset link?{" "}
                <Link
                  to="/reset-password"
                  className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200 hover:underline"
                >
                  Request here
                </Link>
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
