import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { aiService } from "../../services/ai";
import QuotaStatus from "../../components/QuotaStatus";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { getUserId, formatNumber, handleApiError } from "../../utils/common";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { BookPlus, BarChart2, Bot, Users } from "lucide-react";
import Icon from "../../components/ui/icon";

export default function Overview() {
  const { user } = useAuth();

  const [overview, setOverview] = useState(null);
  const [knowledgeCount, setKnowledgeCount] = useState(0);
  const [quota, setQuota] = useState(null);
  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);
  const { showError } = useAlertMigration();
  const [contacts, setContacts] = useState([]);

  const fetchOverview = async () => {
    const userId = getUserId(user);
    if (!userId) return;

    try {
      // Fetch overview statistics, contacts, and quota status in parallel
      const [overviewData, knowledgeData, quotaData, contactsData] =
        await Promise.all([
          aiService.getStatisticsOverview(userId),
          aiService.getKnowledgeBase({ authId: userId, limit: 1 }),
          aiService.getQuotaStatus(userId).catch(() => null), // Don't fail if quota API is not available
          aiService.getContacts({ authId: userId, limit: 100 }),
        ]);

      if (overviewData.success) {
        setOverview(overviewData.data);
      }

      if (knowledgeData.success) {
        setKnowledgeCount(knowledgeData.pagination?.total || 0);
      }

      if (quotaData?.success) {
        setQuota(quotaData.quota);
      }

      if (contactsData.success) {
        setContacts(contactsData.data || []);
      }
    } catch (error) {
      handleApiError(error, showError, "Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchOverview();
    }
  }, [user]);

  const formatTime = (ms) => {
    if (ms >= 1000) return (ms / 1000).toFixed(1) + "s";
    return ms + "ms";
  };

  if (showLoadingState) {
    return <PageSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.username || "User"}!
        </h2>
        <p className="text-gray-600">
          Here's an overview of your chatbot management dashboard.
        </p>
      </div>

      {/* Error messages now handled by global AlertContainer */}

      {/* Today's Stats */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Today's Activity
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Messages Today
                </p>
                <p className="text-3xl font-bold text-blue-600">
                  {formatNumber(overview?.today?.total_messages || 0)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {overview?.today?.incoming_messages || 0} in ·{" "}
                  {overview?.today?.outgoing_messages || 0} out
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Bot className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Active Contacts
                </p>
                <p className="text-3xl font-bold text-green-600">
                  {formatNumber(overview?.contacts?.active || 0)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  of{" "}
                  {formatNumber(
                    overview?.contacts?.total && overview.contacts.total > 0
                      ? overview.contacts.total
                      : contacts?.length || 0,
                  )}{" "}
                  total
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Avg Response Time
                </p>
                <p className="text-3xl font-bold text-purple-600">
                  {formatTime(overview?.performance?.avgResponseTime || 0)}
                </p>
                <p className="text-xs text-gray-500 mt-1">Last 7 days</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <BarChart2 className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Knowledge Entries
                </p>
                <p className="text-3xl font-bold text-orange-600">
                  {formatNumber(knowledgeCount)}
                </p>
                <p className="text-xs text-gray-500 mt-1">Total entries</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Icon name="BookText" className="w-5 h-5 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Monthly Quota Overview */}
        {quota && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Monthly Usage Overview
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="card bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-900">
                      Messages
                    </span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        quota.usagePercent.messages >= 90
                          ? "bg-red-100 text-red-700"
                          : quota.usagePercent.messages >= 80
                            ? "bg-yellow-100 text-yellow-700"
                            : "bg-green-100 text-green-700"
                      }`}
                    >
                      {quota.usagePercent.messages}%
                    </span>
                  </div>
                  <div className="text-lg font-bold text-blue-900">
                    {formatNumber(quota.usage.totalMessages)} /{" "}
                    {formatNumber(quota.limits.monthlyMessages)}
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        quota.usagePercent.messages >= 90
                          ? "bg-red-500"
                          : quota.usagePercent.messages >= 80
                            ? "bg-yellow-500"
                            : "bg-blue-500"
                      }`}
                      style={{
                        width: `${Math.min(quota.usagePercent.messages, 100)}%`,
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="card bg-gradient-to-r from-green-50 to-green-100 border-green-200">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-green-900">
                      Contacts
                    </span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        quota.usagePercent.contacts >= 90
                          ? "bg-red-100 text-red-700"
                          : quota.usagePercent.contacts >= 80
                            ? "bg-yellow-100 text-yellow-700"
                            : "bg-green-100 text-green-700"
                      }`}
                    >
                      {quota.usagePercent.contacts}%
                    </span>
                  </div>
                  <div className="text-lg font-bold text-green-900">
                    {formatNumber(
                      overview?.contacts?.total && overview.contacts.total > 0
                        ? overview.contacts.total
                        : contacts?.length || 0,
                    )}{" "}
                    / {formatNumber(quota.limits.totalContacts)}
                  </div>
                  <div className="w-full bg-green-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        quota.usagePercent.contacts >= 90
                          ? "bg-red-500"
                          : quota.usagePercent.contacts >= 80
                            ? "bg-yellow-500"
                            : "bg-green-500"
                      }`}
                      style={{
                        width: `${Math.min(quota.usagePercent.contacts, 100)}%`,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Monthly Summary */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          This Month's Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-600 mb-2">
                Total Messages
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {formatNumber(overview?.thisMonth?.total_messages || 0)}
              </p>
            </div>
          </div>

          <div className="card">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-600 mb-2">
                New Contacts
              </p>
              <p className="text-2xl font-bold text-green-600">
                {formatNumber(overview?.thisMonth?.new_contacts || 0)}
              </p>
            </div>
          </div>

          <div className="card">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-600 mb-2">
                Knowledge Hit Rate
              </p>
              <p className="text-2xl font-bold text-purple-600">
                {overview?.performance?.knowledgeHitRate || 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quota Status */}
      <QuotaStatus
        authId={user?.user?.id || user?.profile?.id || user?.id}
        className="lg:col-span-1"
      />
    </div>
  );
}
