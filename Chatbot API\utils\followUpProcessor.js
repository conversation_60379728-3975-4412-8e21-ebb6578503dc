import { processPendingFollowUps } from "./followUpScheduler.js";
import logger from "./logger.js";

/**
 * Background processor for automated follow-ups
 * Runs periodically to send scheduled follow-up messages
 */

class FollowUpProcessor {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.processingInterval = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Start the follow-up processor
   */
  start() {
    if (this.isRunning) {
      logger.warn("Follow-up processor is already running");
      return;
    }

    logger.info("🚀 Starting follow-up processor");
    this.isRunning = true;

    // Process immediately on start
    this.processFollowUps();

    // Set up recurring processing
    this.intervalId = setInterval(() => {
      this.processFollowUps();
    }, this.processingInterval);

    logger.info(`📅 Follow-up processor started | Interval: ${this.processingInterval / 1000}s`);
  }

  /**
   * Stop the follow-up processor
   */
  stop() {
    if (!this.isRunning) {
      logger.warn("Follow-up processor is not running");
      return;
    }

    logger.info("🛑 Stopping follow-up processor");
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    logger.info("Follow-up processor stopped");
  }

  /**
   * Process pending follow-ups
   */
  async processFollowUps() {
    if (!this.isRunning) {
      return;
    }

    try {
      logger.debug("🔄 Processing follow-ups batch");
      
      const result = await processPendingFollowUps(50); // Process up to 50 at a time
      
      if (result.success && result.processed > 0) {
        logger.info(`📤 Follow-up batch processed | Total: ${result.processed} | Success: ${result.successful} | Failed: ${result.failed}`);
      }

    } catch (error) {
      logger.error("Error in follow-up processor:", error);
    }
  }

  /**
   * Get processor status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      processingInterval: this.processingInterval,
      nextProcessingIn: this.isRunning ? this.processingInterval : null
    };
  }

  /**
   * Update processing interval
   * @param {number} intervalMs - New interval in milliseconds
   */
  setInterval(intervalMs) {
    if (intervalMs < 60000) { // Minimum 1 minute
      throw new Error("Processing interval must be at least 1 minute");
    }

    this.processingInterval = intervalMs;
    
    if (this.isRunning) {
      // Restart with new interval
      this.stop();
      this.start();
    }

    logger.info(`📅 Follow-up processor interval updated to ${intervalMs / 1000}s`);
  }
}

// Create singleton instance
const followUpProcessor = new FollowUpProcessor();

// Auto-start the processor when the module is loaded
// Only start if we're not in a test environment
if (process.env.NODE_ENV !== 'test') {
  followUpProcessor.start();
}

// Graceful shutdown handling
process.on('SIGINT', () => {
  logger.info('Received SIGINT, stopping follow-up processor...');
  followUpProcessor.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, stopping follow-up processor...');
  followUpProcessor.stop();
  process.exit(0);
});

export default followUpProcessor;

/**
 * Manual trigger for processing follow-ups (for API endpoints)
 */
export async function triggerFollowUpProcessing() {
  try {
    logger.info("🔄 Manual follow-up processing triggered");
    const result = await processPendingFollowUps(100); // Process more when manually triggered
    return result;
  } catch (error) {
    logger.error("Error in manual follow-up processing:", error);
    return { success: false, error: error.message };
  }
}
