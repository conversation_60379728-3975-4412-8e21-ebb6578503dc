import OpenAI from "openai";
import supabase from "./supabase.js";
import logger from "./logger.js";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * AI-powered contact tagging and analysis system
 * Analyzes conversation patterns to automatically assign tags and determine customer intent
 */

// Define the available intent categories with enhanced granularity
export const INTENT_CATEGORIES = {
  INTERESTED: 'interested',
  PLACED_ORDER: 'placed_order',
  NOT_INTERESTED: 'not_interested',
  NO_FOLLOW_UP: 'no_follow_up',
  BOOKING: 'booking',
  SUPPORT_INQUIRY: 'support_inquiry',
  PRICE_INQUIRY: 'price_inquiry',
  PRODUCT_RESEARCH: 'product_research',
  COMPARISON_SHOPPING: 'comparison_shopping',
  READY_TO_PURCHASE: 'ready_to_purchase'
};

// Define enhanced auto-tags with more granular customer insights
export const AUTO_TAGS = {
  HOT_LEAD: 'hot_lead',
  WARM_LEAD: 'warm_lead',
  COLD_LEAD: 'cold_lead',
  CUSTOMER: 'customer',
  PRICE_SENSITIVE: 'price_sensitive',
  NEEDS_FOLLOW_UP: 'needs_follow_up',
  READY_TO_BUY: 'ready_to_buy',
  JUST_BROWSING: 'just_browsing',
  REPEAT_CUSTOMER: 'repeat_customer',
  URGENT_INQUIRY: 'urgent_inquiry',
  HIGH_VALUE_PROSPECT: 'high_value_prospect',
  BUDGET_CONSCIOUS: 'budget_conscious',
  DECISION_MAKER: 'decision_maker',
  NEEDS_APPROVAL: 'needs_approval',
  TECHNICAL_QUESTIONS: 'technical_questions',
  COMPARISON_SHOPPER: 'comparison_shopper',
  IMPULSE_BUYER: 'impulse_buyer',
  RESEARCH_PHASE: 'research_phase',
  OBJECTION_HANDLING: 'objection_handling',
  FOLLOW_UP_RESPONSIVE: 'follow_up_responsive'
};

// Define urgency levels for better follow-up timing
export const URGENCY_LEVELS = {
  IMMEDIATE: 'immediate', // Within 1 hour
  HIGH: 'high',          // Within 4 hours
  MEDIUM: 'medium',      // Within 24 hours
  LOW: 'low',           // Within 48-72 hours
  NONE: 'none'          // No urgency
};

// Define customer journey stages
export const JOURNEY_STAGES = {
  AWARENESS: 'awareness',
  CONSIDERATION: 'consideration',
  DECISION: 'decision',
  PURCHASE: 'purchase',
  POST_PURCHASE: 'post_purchase',
  RETENTION: 'retention'
};

/**
 * Enhanced AI analysis with sophisticated conversation understanding
 * @param {string} authId - The business owner's auth ID
 * @param {string} phoneNumber - Customer's phone number
 * @param {string} contactId - Contact's UUID
 * @param {string} triggeredBy - What triggered this analysis
 * @returns {Object} Enhanced analysis results
 */
export async function analyzeContactConversation(authId, phoneNumber, contactId, triggeredBy = 'new_message') {
  try {
    logger.info(`🤖 Starting enhanced AI analysis | Contact: ${contactId} | Phone: ${phoneNumber} | Triggered by: ${triggeredBy}`);

    // Get comprehensive conversation history (last 30 messages for better context)
    const { data: chatHistory, error: chatError } = await supabase
      .from("chat_history")
      .select("role, content, created_at")
      .eq("auth_id", authId)
      .eq("phone_number", phoneNumber)
      .order("created_at", { ascending: false })
      .limit(30);

    if (chatError) {
      logger.error("Error fetching chat history for AI analysis:", chatError);
      return null;
    }

    if (!chatHistory || chatHistory.length === 0) {
      logger.info("No chat history found for analysis");
      return null;
    }

    // Get current contact info
    const { data: contact, error: contactError } = await supabase
      .from("contacts")
      .select("*")
      .eq("id", contactId)
      .single();

    if (contactError) {
      logger.error("Error fetching contact for AI analysis:", contactError);
      return null;
    }

    // Get comprehensive order history and patterns
    const { data: orders, error: ordersError } = await supabase
      .from("orders")
      .select("order_status, total_amount, created_at, items")
      .eq("auth_id", authId)
      .eq("phone_number", phoneNumber)
      .order("created_at", { ascending: false })
      .limit(10);

    // Get previous follow-up history for learning
    const { data: followUpHistory, error: followUpError } = await supabase
      .from("follow_up_schedules")
      .select("follow_up_type, status, created_at, executed_at")
      .eq("auth_id", authId)
      .eq("phone_number", phoneNumber)
      .order("created_at", { ascending: false })
      .limit(5);

    // Analyze conversation patterns and timing
    const conversationMetrics = analyzeConversationPatterns(chatHistory);

    // Prepare enhanced conversation context for AI analysis
    const conversationContext = chatHistory
      .reverse() // Show chronological order
      .map((msg, index) => {
        const timestamp = new Date(msg.created_at).toLocaleString();
        return `[${timestamp}] ${msg.role}: ${msg.content}`;
      })
      .join('\n');

    const orderContext = orders && orders.length > 0
      ? `Order History (${orders.length} orders):\n${orders.map(o =>
          `- ${o.order_status} | $${o.total_amount || 'N/A'} | ${new Date(o.created_at).toLocaleDateString()}`
        ).join('\n')}`
      : 'No previous orders';

    const followUpContext = followUpHistory && followUpHistory.length > 0
      ? `Follow-up History:\n${followUpHistory.map(f =>
          `- ${f.follow_up_type} | ${f.status} | ${new Date(f.created_at).toLocaleDateString()}`
        ).join('\n')}`
      : 'No previous follow-ups';

    // Create enhanced AI prompt for sophisticated analysis
    const analysisPrompt = `Analyze this WhatsApp conversation between a business and customer with advanced behavioral insights.

CONVERSATION HISTORY WITH TIMESTAMPS:
${conversationContext}

ORDER HISTORY:
${orderContext}

FOLLOW-UP HISTORY:
${followUpContext}

CONVERSATION METRICS:
- Total messages: ${contact.total_messages}
- First contact: ${new Date(contact.first_message_at).toLocaleDateString()}
- Last message: ${new Date(contact.last_message_at).toLocaleDateString()}
- Response time pattern: ${conversationMetrics.avgResponseTime}
- Message length pattern: ${conversationMetrics.avgMessageLength}
- Conversation frequency: ${conversationMetrics.conversationFrequency}
- Current tags: ${contact.tags ? contact.tags.join(', ') : 'None'}

ENHANCED ANALYSIS REQUIRED:
1. Intent Category (choose ONE most accurate):
   - "interested": Shows genuine interest, asks questions, wants to know more
   - "placed_order": Has placed an order or is actively ordering
   - "booking": Trying to book a service or appointment
   - "support_inquiry": Needs help with existing product/service
   - "price_inquiry": Primarily focused on pricing information
   - "product_research": Researching products/services, comparing options
   - "comparison_shopping": Comparing with competitors
   - "ready_to_purchase": Ready to buy, just needs final details
   - "not_interested": Clearly not interested, declined offers
   - "no_follow_up": Asked not to be contacted or conversation ended naturally

2. Engagement Score (0.0 to 1.0):
   - 0.0-0.2: Very low (one-word responses, dismissive)
   - 0.3-0.4: Low (short responses, minimal interest)
   - 0.5-0.6: Medium (some interest, basic questions)
   - 0.7-0.8: High (detailed questions, active participation)
   - 0.9-1.0: Very high (enthusiastic, ready to buy)

3. Enhanced Auto Tags (select all relevant):
   - hot_lead, warm_lead, cold_lead, customer, repeat_customer
   - price_sensitive, budget_conscious, high_value_prospect
   - ready_to_buy, just_browsing, research_phase, comparison_shopper
   - decision_maker, needs_approval, impulse_buyer
   - technical_questions, objection_handling, urgent_inquiry
   - needs_follow_up, follow_up_responsive

4. Urgency Level (choose ONE):
   - "immediate": Needs response within 1 hour (urgent inquiry, ready to buy)
   - "high": Needs response within 4 hours (high interest, time-sensitive)
   - "medium": Needs response within 24 hours (standard follow-up)
   - "low": Can wait 48-72 hours (casual inquiry, research phase)
   - "none": No urgency (not interested, no follow-up needed)

5. Journey Stage (choose ONE):
   - "awareness": Just learning about products/services
   - "consideration": Comparing options, evaluating features
   - "decision": Ready to make purchase decision
   - "purchase": Actively purchasing or just purchased
   - "post_purchase": After purchase, may need support
   - "retention": Existing customer, potential for repeat business

6. Optimal Follow-up Timing (hours from now):
   - Consider customer's response patterns, urgency, and engagement level
   - Range: 1-72 hours (or 0 for no follow-up)

7. Confidence Score (0.0 to 1.0): How confident you are in this analysis

8. Sentiment Score (-1.0 to 1.0):
   - -1.0 to -0.5: Negative (frustrated, angry, disappointed)
   - -0.4 to 0.4: Neutral (matter-of-fact, informational)
   - 0.5 to 1.0: Positive (happy, excited, satisfied)

IMPORTANT: Respond with ONLY valid JSON, no markdown formatting or code blocks.

Example response format:
{
  "intent_category": "ready_to_purchase",
  "engagement_score": 0.9,
  "auto_tags": ["hot_lead", "ready_to_buy", "decision_maker", "needs_follow_up"],
  "urgency_level": "high",
  "journey_stage": "decision",
  "optimal_follow_up_hours": 2,
  "confidence_score": 0.95,
  "sentiment_score": 0.8,
  "reasoning": "Customer has asked specific questions about pricing and availability, showing strong purchase intent. They mentioned needing to decide by tomorrow, indicating high urgency. Positive sentiment throughout conversation."
}`;

    // Get AI analysis
    const aiResponse = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert customer behavior analyst. Analyze conversations to determine customer intent and engagement accurately. CRITICAL: Respond with ONLY valid JSON - no markdown, no code blocks, no formatting. Just pure JSON."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      temperature: 0.1, // Low temperature for consistent analysis
      max_tokens: 500
    });

    const analysisText = aiResponse.choices[0].message.content.trim();

    // Clean the response - remove markdown code blocks if present
    let cleanedText = analysisText;
    if (cleanedText.startsWith('```json')) {
      cleanedText = cleanedText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (cleanedText.startsWith('```')) {
      cleanedText = cleanedText.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    // Parse AI response
    let analysis;
    try {
      analysis = JSON.parse(cleanedText);
    } catch (parseError) {
      logger.error("Error parsing AI analysis response:", parseError);
      logger.error("Original AI Response:", analysisText);
      logger.error("Cleaned Response:", cleanedText);
      return null;
    }

    // Validate enhanced analysis structure
    if (!analysis.intent_category || !analysis.engagement_score || !analysis.auto_tags ||
        !analysis.confidence_score || !analysis.urgency_level || !analysis.journey_stage) {
      logger.error("Invalid enhanced AI analysis structure:", analysis);
      return null;
    }

    // Update contact with enhanced AI analysis (graceful fallback for missing columns)
    const updateData = {
      auto_tags: analysis.auto_tags,
      ai_intent_category: analysis.intent_category,
      ai_engagement_score: analysis.engagement_score,
      updated_at: new Date().toISOString()
    };

    // Add enhanced fields if they exist in the database
    try {
      // Try to update with all enhanced fields first
      const { error: updateError } = await supabase
        .from("contacts")
        .update({
          ...updateData,
          ai_urgency_level: analysis.urgency_level,
          ai_journey_stage: analysis.journey_stage,
          ai_sentiment_score: analysis.sentiment_score || 0,
          ai_optimal_follow_up_hours: analysis.optimal_follow_up_hours || 24,
          ai_confidence_score: analysis.confidence_score
        })
        .eq("id", contactId);

      if (updateError) {
        // If enhanced fields don't exist, fall back to basic fields
        logger.warn("Enhanced AI fields not found, using basic fields only:", updateError.message);

        const { error: fallbackError } = await supabase
          .from("contacts")
          .update(updateData)
          .eq("id", contactId);

        if (fallbackError) {
          logger.error("Error updating contact with basic AI analysis:", fallbackError);
          return null;
        }

        logger.info("✅ Contact updated with basic AI analysis (enhanced fields not available)");
      } else {
        logger.info("✅ Contact updated with enhanced AI analysis");
      }
    } catch (error) {
      logger.error("Error updating contact with AI analysis:", error);
      return null;
    }

    logger.info(`🤖 Enhanced AI Analysis complete | Contact: ${contactId} | Intent: ${analysis.intent_category} | Engagement: ${analysis.engagement_score} | Urgency: ${analysis.urgency_level || 'N/A'} | Journey: ${analysis.journey_stage || 'N/A'} | Tags: ${analysis.auto_tags.join(', ')}`);

    return {
      ...analysis,
      contactId,
      phoneNumber,
      triggeredBy,
      conversationMetrics
    };

  } catch (error) {
    logger.error("Error in AI conversation analysis:", error);
    return null;
  }
}

/**
 * Determine if a contact should receive follow-up based on AI analysis
 * @param {Object} analysis - AI analysis results
 * @param {Object} contact - Contact information
 * @returns {boolean} Whether follow-up should be scheduled
 */
export function shouldScheduleFollowUp(analysis, contact) {
  if (!analysis || !contact) return false;

  // Don't follow up if explicitly requested not to
  if (analysis.intent_category === INTENT_CATEGORIES.NO_FOLLOW_UP) {
    return false;
  }

  // Don't follow up if not interested and low engagement
  if (analysis.intent_category === INTENT_CATEGORIES.NOT_INTERESTED && analysis.engagement_score < 0.3) {
    return false;
  }

  // Don't follow up if customer already placed order (unless it's for booking)
  if (analysis.intent_category === INTENT_CATEGORIES.PLACED_ORDER && analysis.intent_category !== INTENT_CATEGORIES.BOOKING) {
    return false;
  }

  // Follow up for interested customers with medium to high engagement
  if (analysis.intent_category === INTENT_CATEGORIES.INTERESTED && analysis.engagement_score >= 0.4) {
    return true;
  }

  // Follow up for booking inquiries
  if (analysis.intent_category === INTENT_CATEGORIES.BOOKING) {
    return true;
  }

  // Follow up if tagged as needs follow-up
  if (analysis.auto_tags && analysis.auto_tags.includes(AUTO_TAGS.NEEDS_FOLLOW_UP)) {
    return true;
  }

  return false;
}

/**
 * Get follow-up message template based on intent category
 * @param {string} intentCategory - The customer's intent category
 * @param {Object} contact - Contact information
 * @returns {string} Follow-up message template
 */
export function getFollowUpTemplate(intentCategory, contact) {
  const templates = {
    [INTENT_CATEGORIES.INTERESTED]: "Hi {name}! 👋 Just following up on our conversation. Do you have any other questions about our products/services? I'm here to help! 😊",
    [INTENT_CATEGORIES.BOOKING]: "Hi {name}! 📅 Following up on your booking inquiry. Would you like to proceed with scheduling? Let me know what works best for you!",
    [INTENT_CATEGORIES.NOT_INTERESTED]: "Hi {name}! 👋 Hope you're doing well. Just wanted to check if anything has changed or if you'd like to hear about any new offers we have! 😊"
  };

  const template = templates[intentCategory] || templates[INTENT_CATEGORIES.INTERESTED];
  const customerName = contact.name || 'there';
  
  return template.replace('{name}', customerName);
}

/**
 * Get enhanced booking reminder template with Google Sheets data
 * @param {Object} bookingData - Booking data from Google Sheets
 * @param {Object} contact - Contact information
 * @returns {string} Enhanced booking reminder message
 */
export function getBookingReminderTemplate(bookingData, contact) {
  const customerName = contact?.name || bookingData?.customerName || 'Valued Customer';

  // Default template if no specific booking data
  if (!bookingData || !bookingData.date) {
    return `Hi ${customerName}! 📅 Following up on your booking inquiry. Would you like to proceed with scheduling? Let me know what works best for you!`;
  }

  // Enhanced template with Google Sheets data
  let template = `Hi ${customerName}! 📅

This is a friendly reminder about your upcoming appointment:

📍 Date: ${bookingData.date}`;

  // Add time if available
  if (bookingData.time) {
    template += `\n⏰ Time: ${bookingData.time}`;
  }

  // Add status if available
  if (bookingData.status) {
    template += `\n✅ Status: ${bookingData.status}`;
  }

  // Add service/product if available
  if (bookingData.service || bookingData.product) {
    template += `\n🛍️ Service: ${bookingData.service || bookingData.product}`;
  }

  // Add location if available
  if (bookingData.location || bookingData.address) {
    template += `\n📍 Location: ${bookingData.location || bookingData.address}`;
  }

  // Add notes if available
  if (bookingData.notes || bookingData.remarks) {
    template += `\n📝 Notes: ${bookingData.notes || bookingData.remarks}`;
  }

  template += `\n\nPlease let us know if you need to reschedule or if you have any questions. We look forward to seeing you! 😊

Thank you! 🙏`;

  return template;
}

/**
 * Analyze conversation patterns for better insights
 * @param {Array} chatHistory - Array of chat messages
 * @returns {Object} Conversation metrics
 */
function analyzeConversationPatterns(chatHistory) {
  if (!chatHistory || chatHistory.length === 0) {
    return {
      avgResponseTime: 'No data',
      avgMessageLength: 'No data',
      conversationFrequency: 'No data'
    };
  }

  // Calculate average message length
  const messageLengths = chatHistory.map(msg => msg.content.length);
  const avgMessageLength = messageLengths.reduce((a, b) => a + b, 0) / messageLengths.length;

  // Calculate response times (time between customer and assistant messages)
  const responseTimes = [];
  for (let i = 1; i < chatHistory.length; i++) {
    const current = new Date(chatHistory[i].created_at);
    const previous = new Date(chatHistory[i - 1].created_at);
    const timeDiff = Math.abs(current - previous) / (1000 * 60); // in minutes

    // Only consider reasonable response times (less than 24 hours)
    if (timeDiff < 1440) {
      responseTimes.push(timeDiff);
    }
  }

  const avgResponseTime = responseTimes.length > 0
    ? `${Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)} minutes`
    : 'No data';

  // Calculate conversation frequency (messages per day)
  const firstMessage = new Date(chatHistory[chatHistory.length - 1].created_at);
  const lastMessage = new Date(chatHistory[0].created_at);
  const daysDiff = Math.max(1, (lastMessage - firstMessage) / (1000 * 60 * 60 * 24));
  const conversationFrequency = `${(chatHistory.length / daysDiff).toFixed(1)} messages/day`;

  return {
    avgResponseTime,
    avgMessageLength: `${Math.round(avgMessageLength)} characters`,
    conversationFrequency
  };
}

/**
 * Enhanced follow-up decision logic with AI insights
 * @param {Object} analysis - Enhanced AI analysis results
 * @param {Object} contact - Contact information
 * @returns {Object} Follow-up recommendation with timing and type
 */
export function getSmartFollowUpRecommendation(analysis, contact) {
  if (!analysis || !contact) {
    return { shouldFollowUp: false, reason: 'Missing analysis or contact data' };
  }

  // Don't follow up if explicitly requested not to
  if (analysis.intent_category === INTENT_CATEGORIES.NO_FOLLOW_UP) {
    return { shouldFollowUp: false, reason: 'Customer requested no follow-up' };
  }

  // Don't follow up if not interested and low engagement
  if (analysis.intent_category === INTENT_CATEGORIES.NOT_INTERESTED &&
      analysis.engagement_score < 0.3 &&
      analysis.sentiment_score < 0) {
    return { shouldFollowUp: false, reason: 'Low interest and negative sentiment' };
  }

  // High priority follow-ups
  if (analysis.urgency_level === URGENCY_LEVELS.IMMEDIATE ||
      analysis.urgency_level === URGENCY_LEVELS.HIGH) {
    return {
      shouldFollowUp: true,
      delayHours: analysis.optimal_follow_up_hours || (analysis.urgency_level === URGENCY_LEVELS.IMMEDIATE ? 1 : 4),
      priority: 'high',
      reason: `High urgency (${analysis.urgency_level}) with ${analysis.intent_category} intent`
    };
  }

  // Medium priority follow-ups
  if (analysis.engagement_score >= 0.5 &&
      (analysis.intent_category === INTENT_CATEGORIES.INTERESTED ||
       analysis.intent_category === INTENT_CATEGORIES.BOOKING ||
       analysis.intent_category === INTENT_CATEGORIES.READY_TO_PURCHASE)) {
    return {
      shouldFollowUp: true,
      delayHours: analysis.optimal_follow_up_hours || 24,
      priority: 'medium',
      reason: `Good engagement (${analysis.engagement_score}) with ${analysis.intent_category} intent`
    };
  }

  // Low priority follow-ups
  if (analysis.auto_tags && analysis.auto_tags.includes(AUTO_TAGS.NEEDS_FOLLOW_UP)) {
    return {
      shouldFollowUp: true,
      delayHours: analysis.optimal_follow_up_hours || 48,
      priority: 'low',
      reason: 'Tagged as needs follow-up'
    };
  }

  return { shouldFollowUp: false, reason: 'No follow-up criteria met' };
}
