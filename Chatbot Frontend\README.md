# Chilbee - AI Chatbot for Malaysian Businesses

An intelligent WhatsApp chatbot platform specifically designed for Malaysian small businesses including hawker stalls, kopitiams, restaurants, and local shops.

## 🚀 Features

- **24/7 Customer Support**: Automated responses to customer inquiries
- **Multi-language Support**: Works in Bahasa Malaysia and English
- **WhatsApp Integration**: Direct integration with WhatsApp Business API
- **Product Management**: Easy product catalog management
- **Contact Management**: Customer relationship management
- **Analytics Dashboard**: Track performance and customer interactions
- **Knowledge Base**: Custom AI training with business-specific information

## 🛠️ Tech Stack

- **Frontend**: React 19, Vite, TailwindCSS
- **Backend**: Supabase (Database & Authentication)
- **Routing**: React Router v7
- **HTTP Client**: Axios
- **UI Components**: Custom components with shadcn/ui design system

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Base UI components (buttons, cards, etc.)
│   ├── AuthForm.jsx     # Authentication form component
│   ├── Navbar.jsx       # Navigation component
│   └── PricingCard.jsx  # Pricing display component
├── contexts/            # React contexts
│   └── AuthContext.jsx  # Authentication state management
├── pages/               # Page components
│   ├── dashboard/       # Dashboard-related pages
│   │   ├── Analytics.jsx
│   │   ├── ChatbotConfig.jsx
│   │   ├── Contacts.jsx
│   │   ├── Dashboard.jsx
│   │   ├── KnowledgeBase.jsx
│   │   ├── Overview.jsx
│   │   ├── Products.jsx
│   │   └── WhatsAppIntegration.jsx
│   ├── Home.jsx         # Landing page
│   ├── Login.jsx        # Login page
│   ├── Register.jsx     # Registration page
│   ├── Pricing.jsx      # Pricing page
│   └── ...              # Other auth-related pages
├── services/            # API services
│   └── auth.js          # Authentication service
├── lib/                 # Utility functions
│   └── utils.ts         # Helper utilities
├── App.jsx              # Main app component
├── main.jsx             # App entry point
└── index.css            # Global styles
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd chatbot
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env
   ```

   Configure your Supabase credentials in the `.env` file.

4. Start the development server:

   ```bash
   npm run dev
   ```

5. Open [http://localhost:5173](http://localhost:5173) in your browser.

## 🎯 Target Audience

This platform is specifically designed for Malaysian small business owners:

- **Hawker Stalls**: Manage orders and customer inquiries
- **Kopitiams**: Handle menu questions and table reservations
- **Local Restaurants**: Automate order taking and customer service
- **Small Retail Shops**: Product inquiries and stock information
- **Service Providers**: Appointment booking and service information

## 🌟 Key Benefits

- **No Technical Knowledge Required**: Simple setup process
- **Local Support**: Customer support in Bahasa Malaysia and English
- **Cost-Effective**: Affordable pricing for small businesses
- **Mobile-First**: Optimized for mobile usage
- **Malaysian Context**: Understanding of local business culture and needs

## 📱 Mobile-Responsive Design

The application is fully responsive and optimized for:

- Desktop computers
- Tablets
- Mobile phones
- Touch interfaces

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Code Quality

The project uses:

- ESLint for code linting
- Prettier for code formatting (configured)
- Modern JavaScript/React patterns
- Component-based architecture

## 🚀 Deployment

The application can be deployed to any static hosting service:

- Vercel
- Netlify
- GitHub Pages
- Traditional web hosting

## 📄 License

This project is proprietary software for Malaysian business use.

## 🤝 Support

For support, please contact our Malaysian customer service team through the application or visit our website.

---

Made with ❤️ for Malaysian businesses
