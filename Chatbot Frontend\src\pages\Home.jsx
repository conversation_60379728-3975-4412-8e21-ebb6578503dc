import { Link } from "react-router-dom";
import {
  Bot,
  BookText,
  Package,
  Zap,
  MessageCircle,
  BarChart2,
  Check,
  Sparkle,
  BadgeCheck,
  MessageCircleHeart,
} from "lucide-react";
import Icon from "../components/ui/icon";

export default function Home() {
  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-blue-100/50 min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>

        <div className="relative container mx-auto px-6 py-24 lg:py-32">
          <div className="flex flex-col lg:flex-row items-center gap-16">
            {/* Left Content */}
            <div className="flex-1 text-center lg:text-left fade-in">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 px-6 py-3 rounded-full text-sm font-semibold mb-8 shadow-sm">
                <Sparkle className="w-5 h-5" />
                Perfect for Malaysian Businesses
                <span className="ml-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs">
                  LOCAL
                </span>
              </div>

              <h1 className="text-5xl lg:text-7xl font-extrabold text-gray-900 mb-8 leading-tight">
                Automate Your
                <span className="gradient-text block lg:inline"> Business</span>
                <br />
                With WhatsApp AI
              </h1>

              <p className="text-xl lg:text-2xl text-gray-600 mb-10 max-w-2xl leading-relaxed">
                Perfect for hawker stalls, kopitiams, restaurants, and small
                businesses in Malaysia.
                <span className="font-semibold text-blue-600">
                  {" "}
                  No technical knowledge needed
                </span>{" "}
                – setup in Chinese, Bahasa Malaysia or English.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <Link to="/register" className="btn-primary text-lg px-8 py-4">
                  <span className="flex items-center gap-2">
                    Get Started
                    <Icon name="ArrowRight" className="w-5 h-5" />
                  </span>
                </Link>
                <Link to="/pricing" className="btn-secondary text-lg px-8 py-4">
                  View Packages
                </Link>
                <a
                  href="https://wa.me/***********?text=Hello%20Chilbee%20Team!"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-secondary text-lg px-8 py-4 bg-green-500 hover:bg-green-600 text-white"
                >
                  <span className="flex items-center gap-2">
                    Contact Us via WhatsApp
                    <Icon name="MessageCircle" className="w-5 h-5" />
                  </span>
                </a>
              </div>

              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <Check className="w-5 h-5 text-green-500" />
                  No monthly subscription
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-5 h-5 text-green-500" />
                  Works in Chinese, BM & English
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-5 h-5 text-green-500" />
                  Local customer support
                </div>
              </div>
            </div>

            {/* Right Content - Enhanced Chat Interface */}
            <div className="flex-1 relative slide-up hidden md:block">
              <div className="relative max-w-md mx-auto">
                {/* Main Chat Container */}
                <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
                  {/* Chat Header */}
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white">
                    <div className="flex items-center gap-4">
                      <div className="w-13 h-13 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm p-1">
                        <Bot className="w-7 h-7" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-lg">Chilbee Kopitiam</h3>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                          <p className="text-sm opacity-90">Available 24/7</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Chat Messages */}
                  <div className="p-6 space-y-4 min-h-[300px]">
                    <div className="flex justify-end">
                      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl rounded-br-md p-4 max-w-[85%] shadow-lg">
                        <p className="text-sm">
                          Boss, today got prawns char kway teow?
                        </p>
                        <span className="text-xs text-blue-100 mt-1 block">
                          2:30 PM
                        </span>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="bg-gray-100 rounded-2xl rounded-bl-md p-4 max-w-[85%] shadow-sm">
                        <p className="text-sm text-gray-800">
                          Confirm got! RM8.50 for normal, RM10.50 with prawns 🦐
                        </p>
                        <span className="text-xs text-gray-500 mt-1 block">
                          2:30 PM
                        </span>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl rounded-br-md p-4 max-w-[85%] shadow-lg">
                        <p className="text-sm">
                          可以订一份送去Bukit Bintang吗?
                        </p>
                        <span className="text-xs text-blue-100 mt-1 block">
                          2:31 PM
                        </span>
                      </div>
                    </div>

                    <div className="flex">
                      <div className="bg-gray-100 rounded-2xl rounded-bl-md p-4 max-w-[85%] shadow-sm">
                        <p className="text-sm text-gray-800">
                          可以的, 总共RM10.50, 20分钟送到。
                        </p>
                        <span className="text-xs text-gray-500 mt-1 block">
                          2:31 PM
                        </span>
                      </div>
                    </div>

                    {/* Typing Indicator */}
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                      <span className="loading-dots">AI is typing</span>
                    </div>
                  </div>
                </div>

                {/* Floating elements with enhanced animations */}
                <div className="absolute -bottom-10 -left-6 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-4 shadow-lg animate-pulse">
                  <Icon
                    name="MessageCircleHeart"
                    className="w-8 h-8 text-green-600"
                  />
                </div>
                <div className="absolute top-1/2 -right-7 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl p-3 shadow-lg animate-ping">
                  <Icon name="Zap" className="w-4 h-4 text-purple-600" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="bg-gradient-to-br from-white via-blue-50/30 to-white py-24 lg:pt-32 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-pulse"></div>

        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-24">
            <h2 className="text-5xl lg:text-6xl font-extrabold text-gray-900 mb-8 leading-tight">
              Why Choose
              <span className="gradient-text"> Chilbee</span>?
            </h2>

            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12">
              Specially designed for Malaysian hawkers, kopitiams, restaurants,
              and small businesses.
              <span className="font-semibold text-blue-600">
                {" "}
                Built by locals, for locals
              </span>{" "}
              – we understand your unique needs.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {[
              {
                icon: "Bot",
                title: "Available 24/7",
                subtitle: "Never Miss a Customer",
                description:
                  "Your AI assistant never sleeps! Handle customer inquiries and messages around the clock, even when you're closed. Perfect for late-night makan sessions and early morning breakfast rushes.",
                benefits: [
                  "Respond to customers instantly, any time",
                  "Capture orders when you're busy cooking",
                  "Handle delivery inquiries after hours",
                  "Never lose potential customers again",
                ],
                color: "from-blue-500 to-blue-600",
                bgColor: "bg-blue-50",
                borderColor: "border-blue-200",
              },
              {
                icon: "BookText",
                title: "Easy Setup",
                subtitle: "Uncle & Auntie Friendly",
                description:
                  "No technical knowledge needed! Our team personally helps you set up everything in Chinese, Bahasa Malaysia, or English. Perfect for uncle, auntie business owners who want to focus on cooking, not technology.",
                benefits: [
                  "Basic setup assistance included",
                  "Training in your preferred language",
                  "Step-by-step guidance",
                  "Ready to use in 24 hours",
                ],
                color: "from-green-500 to-green-600",
                bgColor: "bg-green-50",
                borderColor: "border-green-200",
              },
              {
                icon: "Package",
                title: "Multiple Languages",
                subtitle: "Truly Malaysian",
                description:
                  "Understands how Malaysians actually speak! Responds naturally to 'Makcik nak order', 'Boss, ada tak?', 'I want to order', and '我要点菜'. No more language barriers with your diverse customers.",
                benefits: [
                  "Bahasa Malaysia conversations",
                  "English and Mandarin support",
                  "Understands local slang & context",
                  "Mixed language conversations",
                ],
                color: "from-purple-500 to-purple-600",
                bgColor: "bg-purple-50",
                borderColor: "border-purple-200",
              },
              {
                icon: "BarChart2",
                title: "Smart Knowledge Base",
                subtitle: "Your Digital Menu Expert",
                description:
                  "Automatically answers questions about your menu, ingredients, halal status, operating hours, and business info. Train it once with your menu details, and it becomes your smartest employee.",
                benefits: [
                  "Knows your complete menu by heart",
                  "Answers ingredient & allergy questions",
                  "Provides halal certification info",
                  "Shares operating hours & location",
                ],
                color: "from-indigo-500 to-indigo-600",
                bgColor: "bg-indigo-50",
                borderColor: "border-indigo-200",
              },
              {
                icon: "MessageCircle",
                title: "Order Taking",
                subtitle: "Take Order Anytime",
                description:
                  "Automatically take and organize orders for char kway teow, nasi lemak, wonton mee, and all your signature dishes. Seamlessly handle customizations, add-ons, and special requests from customers.",
                benefits: [
                  "Automatic order processing",
                  "Handle dish customizations",
                  "Manage add-ons & extras",
                  "Store order in Google Sheet",
                ],
                color: "from-orange-500 to-orange-600",
                bgColor: "bg-orange-50",
                borderColor: "border-orange-200",
              },
              {
                icon: "BarChart2",
                title: "Business Analytics",
                subtitle: "Coming Soon!",
                description:
                  "Advanced analytics, inventory tracking, customer loyalty programs, and growth insights specifically designed for Malaysian food businesses. Understand your customers better and increase profits.",
                benefits: [
                  "Customer behavior insights",
                  "Peak hour analysis",
                  "Popular dish tracking",
                  "Revenue optimization tips",
                ],
                color: "from-red-500 to-red-600",
                bgColor: "bg-red-50",
                borderColor: "border-red-200",
              },
            ].map((feature, index) => (
              <div
                key={index}
                className={`group relative ${feature.bgColor} border ${feature.borderColor} rounded-3xl p-8 transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 fade-in`}
              >
                {/* Icon */}
                <div
                  className={`w-24 h-24 bg-gradient-to-br ${feature.color} rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg group-hover:shadow-2xl`}
                >
                  <Icon name={feature.icon} className="w-12 h-12 text-white" />
                </div>

                {/* Content */}
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm font-semibold text-blue-600 mb-4">
                    {feature.subtitle}
                  </p>
                  <p className="text-gray-700 leading-relaxed text-base">
                    {feature.description}
                  </p>
                </div>

                {/* Benefits List */}
                <div className="space-y-3">
                  {feature.benefits.map((benefit, idx) => (
                    <div
                      key={idx}
                      className="flex items-start gap-3 text-sm text-gray-600"
                    >
                      <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="leading-relaxed">{benefit}</span>
                    </div>
                  ))}
                </div>

                {/* Hover overlay effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/0 to-white/30 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            ))}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-20">
            <div className="inline-flex items-center gap-4 bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
              <div className="text-4xl">🇲🇾</div>
              <div className="text-left">
                <h3 className="font-bold text-gray-900 text-lg">
                  Built for Malaysia, by Malaysians
                </h3>
                <p className="text-gray-600 text-sm">
                  We understand your business because we're from here too
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="bg-gradient-to-r from-gray-50 to-blue-50 py-16">
        <div className="container mx-auto px-6 text-center">
          <p className="text-gray-600 mb-8">
            Trusted by Malaysian businesses across Malaysia
          </p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {/* Mock Malaysian business types */}
            {[
              "Ah Beng Kopitiam",
              "Siti Nasi Lemak",
              "Uncle Wong Char Kway Teow",
              "Mak Cik Curry House",
              "Ravi Banana Leaf",
            ].map((business, index) => (
              <div
                key={index}
                className="bg-white px-6 py-3 rounded-lg font-semibold text-gray-700 shadow-sm"
              >
                {business}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 py-20 lg:py-32 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl opacity-30"></div>

        <div className="relative container mx-auto px-6 text-center">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-8 leading-tight">
            Ready to Boost Your Business?
          </h2>
          <p className="text-xl lg:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join hundreds of Malaysian hawkers and small business owners who are
            already using Chilbee to increase sales
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Link
              to="/register"
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center"
            >
              <span className="flex items-center gap-2 justify-center">
                Get Started Now
                <Icon name="ArrowRight" className="w-5 h-5" />
              </span>
            </Link>
            <Link
              to="/pricing"
              className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-200 flex items-center justify-center"
            >
              View Packages
            </Link>
          </div>

          <div className="flex flex-wrap justify-center items-center gap-8 text-blue-200 text-sm">
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5" />
              No credit card needed
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5" />
              No monthly subscription
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5" />
              Support in Chinese, BM & English
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
