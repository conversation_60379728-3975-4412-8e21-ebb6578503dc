import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { aiService } from "../../services/ai";
import { usePagination } from "../../hooks/usePagination";
import { Pagination } from "../../components/ui/pagination";
import { formatDate } from "../../utils/dateFormatter";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { getEffectiveAuthId } from "../../utils/common";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import Icon from "../../components/ui/icon";

export default function KnowledgeBase() {
  const { user, isImpersonating } = useAuth();

  // Knowledge Base State
  const [knowledgeBase, setKnowledgeBase] = useState([]);
  const { showError, showSuccess } = useAlertMigration();

  // Pagination hook
  const pagination = usePagination({
    initialLimit: 10,
    initialSortBy: "created_at",
    initialSortOrder: "desc",
  });

  const showLoadingState = useDelayedLoading(pagination.loading, 200);

  // Form State
  const [isEditing, setIsEditing] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
  });
  const [formLoading, setFormLoading] = useState(false);

  // Image State
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [removeImage, setRemoveImage] = useState(false);

  // Modal State
  const [showModal, setShowModal] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Removed expanded entries state and toggle functionality

  // Handle image selection
  const handleImageSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        showError("Please select a valid image file");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        showError("Image file size should be less than 5MB");
        return;
      }

      setSelectedImage(file);
      setRemoveImage(false);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Clear image selection
  const clearImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setRemoveImage(false);
    // Reset file input
    const fileInput = document.getElementById("image-upload");
    if (fileInput) {
      fileInput.value = "";
    }
  };

  // Handle image removal for existing entries
  const handleRemoveImage = () => {
    setRemoveImage(true);
    setImagePreview(null);
    setSelectedImage(null);
  };

  // Send image to customer
  const sendImageToCustomer = async (entry) => {
    if (!entry.has_image || !entry.image_url) {
      showError("This entry does not have an image to send");
      return;
    }

    // For now, we'll just copy the image URL to clipboard
    // In a real implementation, you might open a contact selection modal
    try {
      await navigator.clipboard.writeText(entry.image_url);
      showSuccess(
        "Image URL copied to clipboard! You can now send it to customers.",
      );
    } catch (err) {
      console.error("Failed to copy image URL:", err);
      showError("Failed to copy image URL");
    }
  };

  // Fetch Knowledge Base Entries with pagination
  const fetchKnowledgeBase = async () => {
    const userId = getEffectiveAuthId(user, isImpersonating);
    if (!userId) {
      pagination.setLoading(false);
      return;
    }

    try {
      pagination.setLoading(true);
      pagination.setError("");

      const params = {
        ...pagination.getQueryParams(),
        authId: userId,
      };

      const response = await aiService.getKnowledgeBase(params);

      if (response.success) {
        setKnowledgeBase(response.data);
        pagination.updatePagination(response);
      } else {
        pagination.setError(response.error);
      }
    } catch (error) {
      pagination.setError("Failed to fetch knowledge base entries");
      console.error("Error fetching knowledge base:", error);
    } finally {
      pagination.setLoading(false);
    }
  };

  // Effect to fetch data when pagination parameters change (with debounced search)
  useEffect(() => {
    if (user) {
      fetchKnowledgeBase();
    }
  }, [
    user,
    pagination.page,
    pagination.limit,
    pagination.sortBy,
    pagination.sortOrder,
  ]);

  // Debounced search effect
  useEffect(() => {
    if (!user) return;

    const timeoutId = setTimeout(() => {
      fetchKnowledgeBase();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [pagination.search]);

  // Handle Form Submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);

    try {
      const userId = getEffectiveAuthId(user, isImpersonating);
      if (!userId) {
        showError("User ID not available");
        return;
      }

      let response;

      // Check if we have an image to handle
      if (selectedImage || removeImage) {
        // Use form data for image upload
        const formDataToSend = new FormData();
        formDataToSend.append("title", formData.title);
        formDataToSend.append("content", formData.content);
        formDataToSend.append("authId", userId);

        if (selectedImage) {
          formDataToSend.append("image", selectedImage);
        }

        if (isEditing) {
          formDataToSend.append("id", selectedEntry.id);
          if (removeImage) {
            formDataToSend.append("removeImage", "true");
          }
          response =
            await aiService.updateKnowledgeEntryWithImage(formDataToSend);
        } else {
          response = await aiService.addKnowledgeEntryWithImage(formDataToSend);
        }
      } else {
        // Use regular JSON for text-only updates
        const requestData = {
          ...formData,
          authId: userId,
          ...(isEditing && { id: selectedEntry.id }),
        };

        if (isEditing) {
          response = await aiService.updateKnowledgeEntry(requestData);
        } else {
          response = await aiService.addKnowledgeEntry(requestData);
        }
      }

      if (response.success) {
        showSuccess(
          isEditing
            ? "Entry updated successfully!"
            : "Entry added successfully!",
        );
        fetchKnowledgeBase();
        resetForm();
      } else {
        showError(response.error);
      }
    } catch (error) {
      showError("Failed to save knowledge base entry");
      console.error("Error saving knowledge base entry:", error);
    } finally {
      setFormLoading(false);
    }
  };

  // Handle Delete
  const handleDelete = async () => {
    setDeleteLoading(true);
    try {
      const userId = getEffectiveAuthId(user, isImpersonating);
      if (!userId) {
        showError("User ID not available");
        return;
      }

      const response = await aiService.deleteKnowledgeEntry({
        id: deleteId,
        authId: userId,
      });

      if (response.success) {
        showSuccess("Entry deleted successfully!");
        fetchKnowledgeBase();
        setShowModal(false);
      } else {
        showError(response.error);
      }
    } catch (error) {
      showError("Failed to delete knowledge base entry");
      console.error("Error deleting knowledge base entry:", error);
    } finally {
      setDeleteLoading(false);
    }
  };

  // Reset Form
  const resetForm = () => {
    setFormData({ title: "", content: "" });
    setIsEditing(false);
    setSelectedEntry(null);
    clearImage();
  };

  // Edit Entry
  const editEntry = (entry) => {
    setFormData({
      title: entry.title,
      content: entry.content,
    });
    setSelectedEntry(entry);
    setIsEditing(true);

    // Handle existing image
    if (entry.has_image && entry.image_url) {
      setImagePreview(entry.image_url);
    } else {
      setImagePreview(null);
    }
    setSelectedImage(null);
    setRemoveImage(false);
  };

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (showModal) {
      // Store original values
      const originalStyle = window.getComputedStyle(document.body);
      const originalOverflow = originalStyle.overflow;
      const originalPosition = originalStyle.position;

      // Apply modal styles
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.top = "0";
      document.body.style.left = "0";
      document.body.style.width = "100%";
      document.body.style.height = "100%";

      // Cleanup function
      return () => {
        document.body.style.overflow = originalOverflow;
        document.body.style.position = originalPosition;
        document.body.style.top = "";
        document.body.style.left = "";
        document.body.style.width = "";
        document.body.style.height = "";
      };
    }
  }, [showModal]);

  // Note: Filtering and sorting is now handled server-side through pagination
  // We use the knowledgeBase data directly as it comes pre-filtered and sorted from the API

  return (
    <div className="h-[calc(100vh-9rem)] flex flex-col">
      {/* Header Section - Fixed Height */}
      <div className="flex-shrink-0 pb-3">
        <div className="flex justify-between items-start pb-3">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Knowledge Base
            </h2>
            <p className="text-gray-600">
              Manage your chatbot's knowledge and training data.
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => {
                resetForm();
                document
                  .getElementById("knowledgeForm")
                  ?.scrollIntoView({ behavior: "smooth" });
              }}
              className="btn-primary flex items-center gap-2 px-6 py-3"
              disabled={formLoading}
            >
              {formLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Icon name="Plus" className="w-4 h-4" />
                  Add Entry
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - Flexible Height */}
      <div className="flex-1 flex flex-col lg:grid lg:grid-cols-3 gap-3 lg:gap-4 min-h-0">
        {/* Knowledge Base List */}
        <div className="lg:col-span-2 flex flex-col min-h-0">
          {/* Search and Filter - Fixed Height */}
          <div className="card flex-shrink-0 mb-3 p-3">
            <div className="flex items-center gap-2 mb-3">
              <Icon name="Filter" className="h-5 w-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
            </div>
            <div className="relative space-y-2">
              <label
                htmlFor="search"
                className="block text-sm font-medium text-gray-700"
              >
                Search Knowledge Base
              </label>
              <input
                type="search"
                placeholder="Search knowledge base entries..."
                className="input-field"
                value={pagination.search}
                onChange={(e) => pagination.updateSearch(e.target.value)}
              />
            </div>
          </div>

          {/* Entries List - Flexible Height */}
          <div className="card h-[calc(100vh-21rem)] flex flex-col min-h-0 p-4">
            <div className="flex justify-between items-center mb-3 flex-shrink-0">
              <h3 className="text-base font-semibold text-gray-900">
                Knowledge Base Entries
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500 whitespace-nowrap">
                  {pagination.totalItems} total
                </span>
                <Select
                  value={pagination.sortBy}
                  onValueChange={(value) => pagination.updateSort(value)}
                >
                  <SelectTrigger className="w-[120px] h-8 text-sm">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Newest First</SelectItem>
                    <SelectItem value="updated_at">Recently Updated</SelectItem>
                    <SelectItem value="title">A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Content Area - Scrollable */}
            <div className="flex-1 flex flex-col min-h-0">
              {showLoadingState ? (
                <PageSkeleton
                  showHeader={false}
                  cardType="knowledge"
                  cardCount={6}
                />
              ) : knowledgeBase.length === 0 ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    {pagination.search.trim() ? (
                      <>
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Icon
                            name="Search"
                            className="w-8 h-8 text-gray-400"
                          />
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-2">
                          No matching entries
                        </h4>
                        <p className="text-gray-600">
                          Try adjusting your search query
                        </p>
                      </>
                    ) : (
                      <>
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Icon name="Book" className="w-8 h-8 text-gray-400" />
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-2">
                          No entries yet
                        </h4>
                        <p className="text-gray-600">
                          Start by adding your first knowledge base entry!
                        </p>
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  {/* Scrollable Entries List */}
                  <div className="flex-1 overflow-y-auto space-y-3 pr-1">
                    {knowledgeBase.map((entry) => (
                      <div
                        key={entry.id}
                        className="group bg-white border border-gray-100 rounded-lg p-3 hover:shadow-md hover:border-blue-200 transition-all duration-200"
                      >
                        <div className="flex justify-between items-start gap-4">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-gray-900 mb-1 truncate">
                              {entry.title}
                            </h4>
                            <p className="text-sm text-gray-500 flex items-center gap-2">
                              <Icon name="Book" className="w-4 h-4" />
                              {formatDate(entry.created_at)}
                              {entry.updated_at &&
                                entry.updated_at !== entry.created_at && (
                                  <span className="flex items-center gap-1">
                                    <span>·</span>
                                    <Icon name="Clock" className="w-4 h-4" />
                                    Updated: {formatDate(entry.updated_at)}
                                  </span>
                                )}
                            </p>
                          </div>
                          <div className="flex gap-2 transition-opacity">
                            <button
                              onClick={() => editEntry(entry)}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                              title="Edit entry"
                            >
                              <Icon name="Pencil" className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => {
                                setDeleteId(entry.id);
                                setShowModal(true);
                              }}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              title="Delete entry"
                            >
                              <Icon name="Trash" className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                        <div className="mt-3 text-gray-600 whitespace-pre-wrap">
                          {entry.content}
                        </div>

                        {/* Display image if available */}
                        {entry.has_image && entry.image_url && (
                          <div className="mt-3">
                            <img
                              src={entry.image_url}
                              alt={entry.title}
                              className="w-full max-w-full h-48 object-cover rounded-lg border border-gray-200"
                            />
                            <button
                              onClick={() => sendImageToCustomer(entry)}
                              className="mt-2 px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded-md transition-colors w-full"
                            >
                              Copy URL
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Pagination Component - Fixed at bottom */}
                  <div className="flex-shrink-0 pt-3 border-t border-gray-100">
                    <Pagination
                      currentPage={pagination.page}
                      totalPages={pagination.totalPages}
                      totalItems={pagination.totalItems}
                      itemsPerPage={pagination.limit}
                      onPageChange={pagination.goToPage}
                      onItemsPerPageChange={pagination.changeLimit}
                      showItemsPerPage={false}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="lg:col-span-1 flex flex-col h-[calc(100vh-15rem)] mt-3 lg:mt-0">
          <div
            id="knowledgeForm"
            className="card flex-1 flex flex-col overflow-hidden p-4"
          >
            <div className="flex items-center justify-between mb-3 flex-shrink-0">
              <h3 className="text-base font-semibold text-gray-900">
                {isEditing ? "Edit Entry" : "Add New Entry"}
              </h3>
              {isEditing && (
                <button
                  onClick={resetForm}
                  className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded"
                  disabled={formLoading}
                >
                  Cancel
                </button>
              )}
            </div>

            <form
              onSubmit={handleSubmit}
              className="flex-1 flex flex-col space-y-3 overflow-y-auto"
            >
              <div>
                <label
                  htmlFor="title"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Title
                </label>
                <input
                  type="text"
                  id="title"
                  className="input-field"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, title: e.target.value }))
                  }
                  placeholder="Enter a descriptive title..."
                  required
                  disabled={formLoading}
                />
              </div>

              <div className="flex-2 md:mb-15">
                <label
                  htmlFor="content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Content
                </label>
                <textarea
                  id="content"
                  rows="4"
                  className="input-field resize-none h-[85%]"
                  value={formData.content}
                  onChange={(e) => {
                    setFormData((prev) => ({
                      ...prev,
                      content: e.target.value,
                    }));
                  }}
                  placeholder="Enter the knowledge base content..."
                  required
                  disabled={formLoading}
                />
                <p className="mt-2 text-xs text-gray-500">
                  Tip: Write detailed information for your chatbot.
                </p>
              </div>

              {/* Image Upload Section */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Image (Optional)
                </label>

                {/* Image Preview */}
                {imagePreview && (
                  <div className="mb-3 relative w-full h-48">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-full object-cover rounded-lg border border-gray-200"
                      style={{ objectFit: "cover" }}
                    />
                    <button
                      type="button"
                      onClick={isEditing ? handleRemoveImage : clearImage}
                      className="absolute top-2 right-2 text-red-500"
                      disabled={formLoading}
                    >
                      <Icon name="Trash" className="w-6 h-6" />
                    </button>
                  </div>
                )}

                {/* File Input */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    id="image-upload"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                    disabled={formLoading}
                  />
                  <label
                    htmlFor="image-upload"
                    className="cursor-pointer flex flex-col items-center justify-center text-center"
                  >
                    <Icon name="Image" className="w-8 h-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">
                      Click to upload an image
                    </span>
                    <span className="text-xs text-gray-500 mt-1">
                      PNG, JPG, GIF up to 5MB
                    </span>
                  </label>
                </div>

                <p className="mt-2 text-xs text-gray-500">
                  Tip: Add an image to make your knowledge base entry more
                  visual and informative.
                </p>
              </div>

              {/* Error and success messages now handled by global AlertContainer */}

              <div className="flex-shrink-0 pt-3 border-t border-gray-100">
                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white rounded-lg flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed px-4 py-2 text-sm"
                  disabled={formLoading}
                >
                  {formLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      {isEditing ? "Updating..." : "Adding..."}
                    </>
                  ) : (
                    <>
                      <Icon name="Plus" className="w-4 h-4" />
                      {isEditing ? "Update Entry" : "Save Entry"}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showModal && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 w-full h-full min-w-full min-h-full bg-black/20 backdrop-blur-sm transition-opacity z-[9999] overflow-hidden"
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              width: "100vw",
              height: "100vh",
              minWidth: "100vw",
              minHeight: "100vh",
              zIndex: 9999,
              backgroundColor: "rgba(0, 0, 0, 0.2)",
              backdropFilter: "blur(8px)",
              WebkitBackdropFilter: "blur(8px)",
            }}
            onClick={() => {
              if (!deleteLoading) {
                setShowModal(false);
                setDeleteId(null);
              }
            }}
          />
          {/* Modal */}
          <div className="fixed inset-0 flex items-center justify-center z-[10000] p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-2xl transform transition-all">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Confirm Delete
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this knowledge base entry? This
                action cannot be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => handleDelete()}
                  className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  disabled={deleteLoading}
                >
                  {deleteLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Icon name="Trash" className="w-4 h-4" />
                      Delete Entry
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={deleteLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
