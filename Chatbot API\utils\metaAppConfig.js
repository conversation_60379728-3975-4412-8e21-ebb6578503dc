import axios from "axios";
import { supabase } from "./supabase.js";
import logger from "./logger.js";

/**
 * Meta App Configuration Helper
 * Handles webhook subscriptions, permissions, and account linking
 */

/**
 * Subscribe to webhooks for a platform
 */
export async function subscribeToWebhooks(platform, accessToken, accountId, webhookUrl, verifyToken) {
  try {
    let subscriptionUrl;
    let subscriptionData;

    switch (platform) {
      case 'whatsapp':
        subscriptionUrl = `https://graph.facebook.com/v18.0/${accountId}/subscribed_apps`;
        subscriptionData = {
          subscribed_fields: ['messages'],
        };
        break;

      case 'messenger':
        subscriptionUrl = `https://graph.facebook.com/v18.0/${accountId}/subscribed_apps`;
        subscriptionData = {
          subscribed_fields: ['messages', 'messaging_postbacks', 'messaging_optins'],
        };
        break;

      case 'instagram':
        subscriptionUrl = `https://graph.facebook.com/v18.0/${accountId}/subscribed_apps`;
        subscriptionData = {
          subscribed_fields: ['messages'],
        };
        break;

      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    const response = await axios.post(subscriptionUrl, subscriptionData, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    logger.error(`Error subscribing to ${platform} webhooks:`, error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Verify webhook subscription
 */
export async function verifyWebhookSubscription(platform, accessToken, accountId) {
  try {
    let verificationUrl;

    switch (platform) {
      case 'whatsapp':
        verificationUrl = `https://graph.facebook.com/v18.0/${accountId}/subscribed_apps`;
        break;
      case 'messenger':
        verificationUrl = `https://graph.facebook.com/v18.0/${accountId}/subscribed_apps`;
        break;
      case 'instagram':
        verificationUrl = `https://graph.facebook.com/v18.0/${accountId}/subscribed_apps`;
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    const response = await axios.get(verificationUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    return {
      success: true,
      subscriptions: response.data.data || [],
    };
  } catch (error) {
    logger.error(`Error verifying ${platform} webhook subscription:`, error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Get app permissions for a user
 */
export async function getAppPermissions(accessToken, userId = 'me') {
  try {
    const response = await axios.get(`https://graph.facebook.com/v18.0/${userId}/permissions`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    const permissions = response.data.data || [];
    const grantedPermissions = permissions
      .filter(p => p.status === 'granted')
      .map(p => p.permission);

    return {
      success: true,
      permissions: grantedPermissions,
      allPermissions: permissions,
    };
  } catch (error) {
    logger.error('Error getting app permissions:', error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Validate platform-specific requirements
 */
export async function validatePlatformRequirements(platform, accessToken, accountData) {
  try {
    const requirements = {
      valid: true,
      missing: [],
      warnings: [],
    };

    // Get current permissions
    const permissionsResult = await getAppPermissions(accessToken);
    if (!permissionsResult.success) {
      requirements.valid = false;
      requirements.missing.push('Unable to verify permissions');
      return requirements;
    }

    const grantedPermissions = permissionsResult.permissions;

    switch (platform) {
      case 'whatsapp':
        // Check required permissions
        const whatsappRequiredPerms = ['whatsapp_business_management', 'whatsapp_business_messaging'];
        for (const perm of whatsappRequiredPerms) {
          if (!grantedPermissions.includes(perm)) {
            requirements.valid = false;
            requirements.missing.push(`Missing permission: ${perm}`);
          }
        }

        // Check if business account and phone number are selected
        if (!accountData.businessAccountId) {
          requirements.valid = false;
          requirements.missing.push('WhatsApp Business Account not selected');
        }
        if (!accountData.phoneNumberId) {
          requirements.valid = false;
          requirements.missing.push('WhatsApp Phone Number not selected');
        }
        break;

      case 'messenger':
        // Check required permissions
        const messengerRequiredPerms = ['pages_messaging', 'pages_manage_metadata'];
        for (const perm of messengerRequiredPerms) {
          if (!grantedPermissions.includes(perm)) {
            requirements.valid = false;
            requirements.missing.push(`Missing permission: ${perm}`);
          }
        }

        // Check if page is selected
        if (!accountData.pageId) {
          requirements.valid = false;
          requirements.missing.push('Facebook Page not selected');
        }
        break;

      case 'instagram':
        // Check required permissions
        const instagramRequiredPerms = ['instagram_basic', 'instagram_manage_messages'];
        for (const perm of instagramRequiredPerms) {
          if (!grantedPermissions.includes(perm)) {
            requirements.valid = false;
            requirements.missing.push(`Missing permission: ${perm}`);
          }
        }

        // Check if Instagram account is selected
        if (!accountData.accountId) {
          requirements.valid = false;
          requirements.missing.push('Instagram Business Account not selected');
        }
        break;
    }

    return requirements;
  } catch (error) {
    logger.error(`Error validating ${platform} requirements:`, error);
    return {
      valid: false,
      missing: ['Validation failed: ' + error.message],
      warnings: [],
    };
  }
}

/**
 * Setup webhook configuration for platform
 */
export async function setupPlatformWebhooks(authId, platform, integration) {
  try {
    const webhookUrl = `${process.env.BASE_URL || 'https://your-domain.com'}/api/ai/${platform}/webhook`;
    const verifyToken = process.env.VERIFY_TOKEN;

    let accountId;
    switch (platform) {
      case 'whatsapp':
        accountId = integration.whatsapp_business_account_id;
        break;
      case 'messenger':
        accountId = integration.messenger_page_id;
        break;
      case 'instagram':
        accountId = integration.instagram_account_id;
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    if (!accountId) {
      throw new Error(`Account ID not found for ${platform}`);
    }

    // Subscribe to webhooks
    const subscriptionResult = await subscribeToWebhooks(
      platform,
      integration.access_token,
      accountId,
      webhookUrl,
      verifyToken
    );

    if (!subscriptionResult.success) {
      throw new Error(`Failed to subscribe to webhooks: ${subscriptionResult.error}`);
    }

    // Verify subscription
    const verificationResult = await verifyWebhookSubscription(
      platform,
      integration.access_token,
      accountId
    );

    // Update integration with webhook status
    await supabase
      .from('social_media_integrations')
      .update({
        webhook_verified: verificationResult.success,
        updated_at: new Date().toISOString(),
      })
      .eq('auth_id', authId)
      .eq('platform', platform);

    return {
      success: true,
      webhookUrl,
      verifyToken,
      subscription: subscriptionResult.data,
      verification: verificationResult,
    };
  } catch (error) {
    logger.error(`Error setting up ${platform} webhooks:`, error);
    
    // Update integration with error status
    await supabase
      .from('social_media_integrations')
      .update({
        connection_status: 'error',
        last_error_message: error.message,
        updated_at: new Date().toISOString(),
      })
      .eq('auth_id', authId)
      .eq('platform', platform);

    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Get platform connection status and health
 */
export async function getPlatformConnectionHealth(authId, platform) {
  try {
    const { data: integration, error } = await supabase
      .from('social_media_integrations')
      .select('*')
      .eq('auth_id', authId)
      .eq('platform', platform)
      .single();

    if (error || !integration) {
      return {
        connected: false,
        status: 'not_configured',
        message: 'Integration not found',
      };
    }

    if (!integration.oauth_connected) {
      return {
        connected: false,
        status: 'not_connected',
        message: 'OAuth not connected',
      };
    }

    // Check token expiration
    if (integration.token_expires_at && new Date(integration.token_expires_at) < new Date()) {
      return {
        connected: false,
        status: 'token_expired',
        message: 'Access token has expired',
        integration,
      };
    }

    // Check webhook verification
    if (!integration.webhook_verified) {
      return {
        connected: true,
        status: 'webhook_not_verified',
        message: 'Webhooks not properly configured',
        integration,
      };
    }

    return {
      connected: true,
      status: 'healthy',
      message: 'All systems operational',
      integration,
    };
  } catch (error) {
    logger.error(`Error checking ${platform} connection health:`, error);
    return {
      connected: false,
      status: 'error',
      message: error.message,
    };
  }
}
