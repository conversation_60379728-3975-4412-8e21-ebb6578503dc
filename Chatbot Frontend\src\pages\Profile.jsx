import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { formatDate } from "../utils/dateFormatter";
import { useAlertMigration } from "../hooks/useAlertMigration";
import { FullPageLoader, InlineLoader } from "../components/ui/loader";
import { User, BadgeCheck, CircleCheck } from "lucide-react";
import Icon from "../components/ui/icon";

export default function Profile() {
  const {
    user,
    isAuthenticated,
    loading,
    changePassword,
    changeDisplayName,
    updateUser,
  } = useAuth();
  const navigate = useNavigate();

  // State for change password form
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [passwordSuccess, setPasswordSuccess] = useState("");
  const [passwordLoading, setPasswordLoading] = useState(false);

  // State for profile details
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState("");
  const [profileLoading, setProfileLoading] = useState(false);
  const { showSuccess, showError } = useAlertMigration();

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, loading, navigate]);

  // Initialize form data
  useEffect(() => {
    if (user) {
      setDisplayName(user.username || user.user?.email || "");
    }
  }, [user]);

  // Generate user initials
  const getUserInitials = () => {
    const username = user?.username || "User";
    return username
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Handle password change
  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setPasswordError("");
    setPasswordSuccess("");

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      setPasswordError("All password fields are required");
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError("New password must be at least 6 characters long");
      return;
    }

    if (currentPassword === newPassword) {
      setPasswordError("New password must be different from current password");
      return;
    }

    setPasswordLoading(true);
    try {
      await changePassword(currentPassword, newPassword);
      setPasswordSuccess("Password changed successfully!");
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error) {
      console.error("Password change error:", error);
      setPasswordError(error.message || "Failed to change password");
    } finally {
      setPasswordLoading(false);
    }
  };

  // Handle profile update
  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    // Clear previous alerts - not needed with global alerts

    if (!displayName.trim()) {
      showError("Display name is required");
      return;
    }

    setProfileLoading(true);
    try {
      const result = await changeDisplayName(displayName.trim());

      // Update user data in context with the new display name
      const updatedUser = {
        ...user,
        username: result.displayName || displayName.trim(),
        profile: result.profile,
      };
      updateUser(updatedUser);
      showSuccess("Profile updated successfully!");
      setIsEditing(false);
    } catch (error) {
      console.error("Display name change error:", error);
      showError(error.message || "Failed to update profile");
    } finally {
      setProfileLoading(false);
    }
  };

  if (loading || !user) {
    return <FullPageLoader text="Loading profile..." />;
  }

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-12">
      <div className="container mx-auto px-6 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8 fade-in">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Icon name="User" className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
            Profile Settings
          </h1>
          <p className="text-gray-600">
            Manage your account details and security settings
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Profile Overview Card */}
          <div className="lg:col-span-1">
            <div className="card">
              <div className="text-center">
                {/* User Avatar */}
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg mx-auto mb-4">
                  {getUserInitials()}
                </div>

                <h2 className="text-xl font-semibold text-gray-900 mb-1">
                  {user?.username || "User"}
                </h2>

                <p className="text-gray-500 text-sm mb-4">
                  {user?.user?.email || user?.email || "No email available"}
                </p>

                <div className="bg-green-50 border border-green-200 rounded-xl p-3 flex items-center justify-center gap-2">
                  <Icon name="CheckCircle" className="w-5 h-5 text-green-600" />
                  <span className="text-green-700 text-sm font-medium">
                    Account Active
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="card mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Account Stats
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Account Plan</span>
                  <span className="font-medium text-blue-600 flex items-center gap-1">
                    {(() => {
                      switch (user?.profile?.plan) {
                        case "free":
                          return "Kedai Free";
                        case "kecil":
                          return "Kedai Kecil";
                        case "popular":
                          return "Kedai Popular";
                        case "besar":
                          return "Kedai Besar";
                        case "admin":
                          return "Kedai Admin";
                        default:
                          return "Kedai Free";
                      }
                    })()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Member Since</span>
                  <span className="font-medium text-gray-900">
                    {formatDate(user?.user?.created_at, "en-MY", {
                      year: "numeric",
                      month: "short",
                    })}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Status</span>
                  <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs bg-green-100 text-green-700">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    Active
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Main Settings */}
          <div className="lg:col-span-2 space-y-8">
            {/* Profile Information */}
            <div className="card">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Profile Information
                </h3>
                {!isEditing && (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="btn-secondary"
                  >
                    <Icon name="Edit" className="w-4 h-4 mr-2" />
                    Edit
                  </button>
                )}
              </div>

              {isEditing ? (
                <form onSubmit={handleProfileUpdate} className="space-y-6">
                  <div>
                    <label
                      htmlFor="displayName"
                      className="block text-sm font-semibold text-gray-700 mb-2"
                    >
                      Display Name
                    </label>
                    <input
                      id="displayName"
                      type="text"
                      className="input-field"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      placeholder="Enter your display name"
                      required
                    />
                  </div>

                  {/* Profile alerts now handled by global AlertContainer */}

                  <div className="flex gap-4">
                    <button
                      type="submit"
                      className="btn-primary"
                      disabled={profileLoading}
                    >
                      {profileLoading ? (
                        <div className="flex items-center gap-2">
                          <InlineLoader />
                          Saving...
                        </div>
                      ) : (
                        "Save Changes"
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setIsEditing(false);
                        setDisplayName(
                          user?.username || user?.user?.email || "",
                        );
                      }}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      Display Name
                    </label>
                    <p className="text-gray-900 font-medium">
                      {user?.username || "Not set"}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      Email
                    </label>
                    <p className="text-gray-900">
                      {user?.user?.email || user?.email || "Not available"}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Password Change */}
            <div className="card">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Change Password
              </h3>

              <form onSubmit={handlePasswordChange} className="space-y-6">
                <div>
                  <label
                    htmlFor="currentPassword"
                    className="block text-sm font-semibold text-gray-700 mb-2"
                  >
                    Current Password
                  </label>
                  <input
                    id="currentPassword"
                    type="password"
                    className="input-field"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder="Enter your current password"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="newPassword"
                    className="block text-sm font-semibold text-gray-700 mb-2"
                  >
                    New Password
                  </label>
                  <input
                    id="newPassword"
                    type="password"
                    className="input-field"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter your new password"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-sm font-semibold text-gray-700 mb-2"
                  >
                    Confirm New Password
                  </label>
                  <input
                    id="confirmPassword"
                    type="password"
                    className="input-field"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your new password"
                    required
                  />
                </div>

                {passwordError && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center gap-3">
                      <Icon
                        name="ExclamationCircle"
                        className="w-5 h-5 text-red-500 flex-shrink-0"
                      />
                      <span className="text-red-700 text-sm font-medium">
                        {passwordError}
                      </span>
                    </div>
                  </div>
                )}

                {passwordSuccess && (
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <div className="flex items-center gap-3">
                      <Icon
                        name="CheckCircle"
                        className="w-5 h-5 text-green-500 flex-shrink-0"
                      />
                      <span className="text-green-700 text-sm font-medium">
                        {passwordSuccess}
                      </span>
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  className="btn-primary"
                  disabled={passwordLoading}
                >
                  {passwordLoading ? (
                    <div className="flex items-center gap-2">
                      <InlineLoader />
                      Changing Password...
                    </div>
                  ) : (
                    "Change Password"
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
