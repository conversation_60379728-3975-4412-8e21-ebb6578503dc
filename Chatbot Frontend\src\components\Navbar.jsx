import { Link, useLocation, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import Icon from "./ui/icon";

// Main navigation links for public pages
const navLinks = [
  {
    to: "/",
    label: "Home",
    icon: "Home",
  },
  {
    to: "/pricing",
    label: "Pricing",
    icon: "Star",
  },
  {
    to: "/showcase",
    label: "Showcase",
    icon: "Sparkle",
  },
];

// Authenticated user navigation links
const authLinks = [
  {
    to: "/dashboard",
    label: "Dashboard",
    icon: "LayoutDashboard",
  },
];

/**
 * Navigation Component
 * Responsive navbar with authentication state and scroll effects
 * Features: Mobile menu, user dropdown, scroll-based styling
 */
export default function Navbar() {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const {
    user,
    isAuthenticated,
    logout,
    isImpersonating,
    stopImpersonation,
    originalAdminUser,
    impersonationSession,
  } = useAuth();

  // Handle navbar styling based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Handle user logout
  const handleLogout = async () => {
    await logout();
    navigate("/");
    setIsMenuOpen(false);
  };

  // Generate user initials for avatar display
  const getUserInitials = () => {
    const username = user?.username || "User";
    return username
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <nav
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white/98 backdrop-blur-xl shadow-lg border-b border-gray-200/50"
          : "bg-white/95 backdrop-blur-lg shadow-sm border-b border-gray-100"
      }`}
    >
      <div className="container mx-auto">
        <div className="flex items-center justify-between px-6 py-4 h-20">
          {/* Logo Section */}
          <Link
            to="/"
            className="absolute left-5 flex items-center space-x-3 group flex-shrink-0"
          >
            <div className="relative">
              <img
                src="https://qjuaiipsdgafcitrqyzy.supabase.co/storage/v1/object/public/source//ChilBee-Logo_V2_with-Shadow.png"
                alt="Chilbee Logo"
                className="w-11 h-11"
              />
            </div>
            <div className="min-w-0">
              <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent whitespace-nowrap">
                Chilbee
              </span>
              <p className="text-xs text-gray-500 -mt-1 font-medium whitespace-nowrap">
                AI Solution for Malaysian Businesses
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center flex-1 justify-center">
            <div className="flex items-center gap-1">
              {/* Public navigation links */}
              <div className="mx-25"></div>
              {navLinks.map((link) => {
                return (
                  <Link
                    key={link.to}
                    to={link.to}
                    className={`relative flex items-center gap-2 font-medium transition-all duration-300 px-4 py-2.5 rounded-xl whitespace-nowrap min-w-0 ${
                      location.pathname === link.to
                        ? "text-blue-600 bg-blue-50"
                        : "text-gray-700 hover:text-blue-600 hover:bg-gray-50/80"
                    }`}
                  >
                    <Icon name={link.icon} className="w-4 h-4 flex-shrink-0" />
                    <span className="flex-shrink-0">{link.label}</span>
                    {location.pathname === link.to && (
                      <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>
                    )}
                  </Link>
                );
              })}

              {/* Authenticated user navigation links */}
              {isAuthenticated &&
                authLinks.map((link) => {
                  return (
                    <Link
                      key={link.to}
                      to={link.to}
                      className={`relative flex items-center gap-2 font-medium transition-all duration-300 px-4 py-2.5 rounded-xl whitespace-nowrap min-w-0 ${
                        location.pathname.startsWith(link.to)
                          ? "text-blue-600 bg-blue-50"
                          : "text-gray-700 hover:text-blue-600 hover:bg-gray-50/80"
                      }`}
                    >
                      <Icon
                        name={link.icon}
                        className="w-4 h-4 flex-shrink-0"
                      />
                      <span className="flex-shrink-0">{link.label}</span>
                      {location.pathname.startsWith(link.to) && (
                        <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>
                      )}
                    </Link>
                  );
                })}
            </div>
          </div>

          {/* Impersonation Indicator */}
          {isImpersonating && (
            <div className="hidden md:flex items-center gap-2 mr-4 px-3 py-1.5 bg-orange-100 border border-orange-200 rounded-lg">
              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
              <div className="flex flex-col">
                <span className="text-orange-800 text-xs font-medium">
                  Impersonating:{" "}
                  {user?.username || user?.profile?.display_name || "User"}
                </span>
                <span className="text-orange-600 text-xs">
                  Admin:{" "}
                  {originalAdminUser?.username ||
                    originalAdminUser?.profile?.display_name ||
                    "Admin"}
                  {impersonationSession?.started_at && (
                    <span className="ml-1">
                      • Started{" "}
                      {new Date(
                        impersonationSession.started_at,
                      ).toLocaleTimeString()}
                    </span>
                  )}
                </span>
              </div>
              <button
                onClick={async () => {
                  try {
                    await stopImpersonation();
                  } catch (error) {
                    console.error("Failed to stop impersonation:", error);
                  }
                }}
                className="ml-2 text-orange-600 hover:text-orange-800 text-xs underline"
              >
                Exit
              </button>
            </div>
          )}

          {/* Desktop Authentication Section */}
          <div className="hidden md:flex items-center flex-shrink-0">
            <div className="flex items-center gap-3 pl-6 border-l border-gray-200">
              {isAuthenticated ? (
                // User dropdown menu for authenticated users
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex items-center gap-3 bg-gray-50 hover:bg-gray-100 rounded-xl px-3 py-2 transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500/20 min-w-0">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold shadow-sm group-hover:shadow-md transition-shadow duration-200 flex-shrink-0">
                        {getUserInitials()}
                      </div>
                      <span className="text-gray-700 text-sm font-medium hidden lg:block group-hover:text-blue-600 transition-colors duration-200 truncate max-w-[120px]">
                        {user?.username || "User"}
                      </span>
                      <Icon
                        name="ChevronDown"
                        className="w-4 h-4 text-gray-500 group-hover:text-gray-700 transition-colors flex-shrink-0"
                      />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user?.username || "User"}
                        </p>
                        <p className="text-xs leading-none text-gray-500">
                          {user?.user?.email || user?.email || "No email"}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link
                        to="/profile"
                        className="flex items-center gap-2 cursor-pointer w-full"
                      >
                        <Icon name="User" className="w-4 h-4" />
                        Profile Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        to="/dashboard"
                        className="flex items-center gap-2 cursor-pointer w-full"
                      >
                        <Icon name="LayoutDashboard" className="w-4 h-4" />
                        Dashboard
                      </Link>
                    </DropdownMenuItem>
                    {(user?.profile?.plan === "admin" ||
                      user?.profile?.role === "admin") && (
                      <DropdownMenuItem asChild>
                        <Link
                          to="/dashboard/admin"
                          className="flex items-center gap-2 cursor-pointer w-full"
                        >
                          <Icon name="Shield" className="w-4 h-4" />
                          Admin Panel
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={handleLogout}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                    >
                      <Icon name="LogOut" className="w-4 h-4 mr-2" />
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                // Login/Register buttons for non-authenticated users
                <div className="flex items-center gap-3">
                  <Link
                    to="/login"
                    className="text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 px-4 py-2 rounded-lg hover:bg-gray-50 whitespace-nowrap"
                  >
                    Sign In
                  </Link>
                  <Link
                    to="/register"
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold px-5 py-2.5 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 whitespace-nowrap"
                  >
                    Get Started
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Mobile Menu Toggle Button */}
          <button
            className="md:hidden absolute right-5 p-2.5 rounded-xl transition-all duration-200 group flex-shrink-0"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <div className="relative w-6 h-6">
              <span
                className={`absolute h-0.5 w-6 bg-gray-700 transform transition-all duration-300 ${
                  isMenuOpen ? "rotate-45 top-3" : "top-1"
                }`}
              ></span>
              <span
                className={`absolute h-0.5 w-6 bg-gray-700 transform transition-all duration-300 top-3 ${
                  isMenuOpen ? "opacity-0" : "opacity-100"
                }`}
              ></span>
              <span
                className={`absolute h-0.5 w-6 bg-gray-700 transform transition-all duration-300 ${
                  isMenuOpen ? "-rotate-45 top-3" : "top-5"
                }`}
              ></span>
            </div>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`md:hidden overflow-hidden transition-all duration-300 ${
          isMenuOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div className="bg-white/98 backdrop-blur-xl border-t border-gray-100 shadow-xl">
          <div className="px-6 py-6 space-y-4">
            {/* Mobile Impersonation Indicator */}
            {isImpersonating && (
              <div className="flex items-center gap-2 p-3 bg-orange-100 border border-orange-200 rounded-lg mb-4">
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                <div className="flex-1">
                  <div className="text-orange-800 text-sm font-medium">
                    Impersonating:{" "}
                    {user?.username || user?.profile?.display_name || "User"}
                  </div>
                  <div className="text-orange-600 text-xs">
                    Admin:{" "}
                    {originalAdminUser?.username ||
                      originalAdminUser?.profile?.display_name ||
                      "Admin"}
                  </div>
                </div>
                <button
                  onClick={async () => {
                    try {
                      await stopImpersonation();
                      setIsMenuOpen(false);
                    } catch (error) {
                      console.error("Failed to stop impersonation:", error);
                    }
                  }}
                  className="text-orange-600 hover:text-orange-800 text-sm underline"
                >
                  Exit
                </button>
              </div>
            )}
            {/* Mobile navigation links */}
            {navLinks.map((link) => {
              return (
                <Link
                  key={link.to}
                  to={link.to}
                  className={`flex items-center gap-3 font-medium transition-all duration-200 px-4 py-3 rounded-xl ${
                    location.pathname === link.to
                      ? "text-blue-600 bg-blue-50"
                      : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Icon name={link.icon} className="w-4 h-4 flex-shrink-0" />
                  {link.label}
                </Link>
              );
            })}

            {/* Mobile authenticated links */}
            {isAuthenticated &&
              authLinks.map((link) => {
                return (
                  <Link
                    key={link.to}
                    to={link.to}
                    className={`flex items-center gap-3 font-medium transition-all duration-200 px-4 py-3 rounded-xl ${
                      location.pathname.startsWith(link.to)
                        ? "text-blue-600 bg-blue-50"
                        : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon name={link.icon} className="w-4 h-4 flex-shrink-0" />
                    {link.label}
                  </Link>
                );
              })}

            {/* Mobile authentication section */}
            <div className="pt-4 border-t border-gray-100">
              {isAuthenticated ? (
                <div className="space-y-3">
                  <Link
                    to="/profile"
                    className="flex items-center gap-3 px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white text-sm font-semibold shadow-sm flex-shrink-0">
                      {getUserInitials()}
                    </div>
                    <div className="min-w-0">
                      <p className="text-gray-900 font-medium truncate">
                        {user?.username || "User"}
                      </p>
                      <p className="text-gray-500 text-sm">View Profile</p>
                    </div>
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex items-center gap-3 w-full text-left text-gray-700 hover:text-red-600 font-medium transition-all duration-200 px-4 py-3 rounded-xl hover:bg-red-50"
                  >
                    <Icon name="LogOut" className="w-6 h-6 flex-shrink-0" />
                    Logout
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <Link
                    to="/login"
                    className="flex items-center gap-3 text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 px-4 py-3 rounded-xl hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon name="User" className="w-5 h-5 flex-shrink-0" />
                    Sign In
                  </Link>
                  <Link
                    to="/register"
                    className="flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold px-4 py-3 rounded-xl transition-all duration-300 shadow-lg"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon name="User" className="w-5 h-5 flex-shrink-0" />
                    Get Started
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
