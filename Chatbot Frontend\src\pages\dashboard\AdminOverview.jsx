import { <PERSON> } from "react-router-dom";
import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { PageSkeleton } from "../../components/ui/skeleton";
import { Users, RefreshCw, CreditCard } from "lucide-react";
import Icon from "../../components/ui/icon";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

export default function AdminOverview({
  systemOverview,
  users,
  loading,
  onRefreshData,
}) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefreshData();
    } finally {
      setIsRefreshing(false);
    }
  };
  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num?.toString() || "0";
  };

  // MetricCard component for dashboard metrics
  const MetricCard = ({ title, value, icon, color, subtitle }) => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <div
          className={`p-2 rounded-lg ${
            color === "blue"
              ? "bg-blue-50"
              : color === "green"
                ? "bg-green-50"
                : color === "red"
                  ? "bg-red-50"
                  : color === "yellow"
                    ? "bg-yellow-50"
                    : color === "purple"
                      ? "bg-purple-50"
                      : color === "orange"
                        ? "bg-orange-50"
                        : "bg-gray-50"
          }`}
        >
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">
          {formatNumber(value)}
        </div>
        <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
      </CardContent>
    </Card>
  );

  if (loading && !systemOverview) {
    return <PageSkeleton cardType="admin-metrics" />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              System Overview
            </h1>
            <p className="text-gray-600">
              Monitor system performance and key metrics
            </p>
          </div>
          <div className="flex flex-wrap gap-3">
            <button className="btn-secondary" onClick={handleRefresh}>
              <RefreshCw
                className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
              />
              Refresh All Data
            </button>
          </div>
        </div>
      </div>

      {/* Alert Messages - Handled by parent AdminDashboard */}

      {systemOverview && (
        <div className="space-y-8">
          {/* System Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Users"
              value={systemOverview?.data?.overview?.totalUsers || 0}
              icon={<Users className="w-6 h-6 text-blue-600" />}
              color="blue"
              subtitle="Platform users"
            />
            <MetricCard
              title="Total Messages"
              value={systemOverview?.data?.overview?.totalMessages || 0}
              icon={
                <Icon name="MessageCircle" className="w-5 h-5 text-green-600" />
              }
              color="green"
              subtitle="Messages sent"
            />
            <MetricCard
              title="Total Contacts"
              value={systemOverview?.data?.overview?.totalContacts || 0}
              icon={<Icon name="Users" className="w-5 h-5 text-purple-600" />}
              color="purple"
              subtitle="Total contacts"
            />
            <MetricCard
              title="Total Tokens"
              value={systemOverview?.data?.overview?.totalTokens || 0}
              icon={
                <Icon name="BadgeCent" className="w-5 h-5 text-orange-600" />
              }
              color="orange"
              subtitle="AI tokens used"
            />
          </div>

          {/* Plan Distribution & Today's Activity */}
          <div className="grid lg:grid-cols-2 gap-8">
            <Card className="bg-white border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Plan Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                {(() => {
                  const planStats = systemOverview?.data?.planStatistics || [];
                  const planNames = {
                    free: "Kedai Free",
                    admin: "Kedai Admin",
                    besar: "Kedai Besar",
                    popular: "Kedai Popular",
                    kecil: "Kedai Kecil",
                    trial: "Kedai Trial",
                  };

                  const colors = {
                    free: "rgba(156, 163, 175, 0.8)",
                    admin: "rgba(239, 68, 68, 0.8)",
                    besar: "rgba(147, 51, 234, 0.8)",
                    popular: "rgba(59, 130, 246, 0.8)",
                    kecil: "rgba(34, 197, 94, 0.8)",
                    trial: "rgba(234, 179, 8, 0.8)",
                  };

                  const chartData = {
                    labels: planStats.map(
                      (stat) => planNames[stat.plan] || stat.plan,
                    ),
                    datasets: [
                      {
                        label: "Active Users",
                        data: planStats.map((stat) => stat.activeUsers),
                        backgroundColor: planStats.map(
                          (stat) =>
                            colors[stat.plan] || "rgba(156, 163, 175, 0.8)",
                        ),
                        borderColor: planStats.map(
                          (stat) =>
                            colors[stat.plan]?.replace("0.8", "1") ||
                            "rgba(156, 163, 175, 1)",
                        ),
                        borderWidth: 1,
                        borderRadius: 4,
                      },
                    ],
                  };

                  const chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        callbacks: {
                          label: function (context) {
                            const totalUsers =
                              systemOverview?.data?.overview?.totalUsers || 1;
                            const percentage = (
                              (context.parsed.y / totalUsers) *
                              100
                            ).toFixed(1);
                            return `${context.parsed.y} users (${percentage}%)`;
                          },
                        },
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        grid: {
                          color: "rgba(0, 0, 0, 0.1)",
                        },
                        ticks: {
                          color: "rgba(107, 114, 128, 1)",
                        },
                      },
                      x: {
                        grid: {
                          display: false,
                        },
                        ticks: {
                          color: "rgba(107, 114, 128, 1)",
                          maxRotation: 45,
                        },
                      },
                    },
                  };

                  return (
                    <div className="h-95">
                      <Bar data={chartData} options={chartOptions} />
                    </div>
                  );
                })()}
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg">
              <CardHeader>
                <CardTitle>System Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Icon
                          name="ShieldCheck"
                          className="w-5 h-5 text-blue-600"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          Recent Messages
                        </p>
                        <p className="text-sm text-gray-600">Last 30 days</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-blue-600">
                      {formatNumber(
                        systemOverview?.data?.messageBreakdown
                          ?.recentMessages || 0,
                      )}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Icon
                          name="ShieldX"
                          className="w-5 h-5 text-green-600"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          Expired Subscriptions
                        </p>
                        <p className="text-sm text-gray-600">Need attention</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-green-600">
                      {formatNumber(
                        systemOverview?.data?.alerts?.expiredSubscriptions || 0,
                      )}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Icon
                          name="ShieldAlert"
                          className="w-5 h-5 text-purple-600"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          Quota Violations
                        </p>
                        <p className="text-sm text-gray-600">
                          Today's violations
                        </p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-purple-600">
                      {formatNumber(
                        systemOverview?.data?.alerts?.todayQuotaViolations || 0,
                      )}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Icon
                          name="Shield"
                          className="w-5 h-5 text-orange-600"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          Avg Tokens/Message
                        </p>
                        <p className="text-sm text-gray-600">
                          Efficiency metric
                        </p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-orange-600">
                      {formatNumber(
                        systemOverview?.data?.messageBreakdown
                          ?.averageTokensPerMessage || 0,
                      )}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Users & System Status Grid */}
          <div className="grid lg:grid-cols-2 gap-8">
            <div className="card">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Recent Registrations
              </h3>
              <div className="overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Joined
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {users.slice(0, 5).map((userData) => (
                      <tr key={userData.auth_id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-blue-600">
                                {(userData.display_name || "U")
                                  .charAt(0)
                                  .toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">
                                {userData.display_name} <br />(
                                {userData.auth_id || "No ID"})
                              </p>
                              <p className="text-sm text-gray-500">
                                {(() => {
                                  switch (userData.plan) {
                                    case "free":
                                      return "Kedai Free Plan";
                                    case "admin":
                                      return "Kedai Admin Plan";
                                    case "besar":
                                      return "Kedai Besar Plan";
                                    case "popular":
                                      return "Kedai Popular Plan";
                                    case "kecil":
                                      return "Kedai Kecil Plan";
                                    case "trial":
                                      return "Kedai Trial Plan";
                                    default:
                                      return userData.plan;
                                  }
                                })()}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {(() => {
                              switch (userData.plan) {
                                case "free":
                                  return "Kedai Free Plan";
                                case "admin":
                                  return "Kedai Admin Plan";
                                case "besar":
                                  return "Kedai Besar Plan";
                                case "popular":
                                  return "Kedai Popular Plan";
                                case "kecil":
                                  return "Kedai Kecil Plan";
                                case "trial":
                                  return "Kedai Trial Plan";
                                default:
                                  return userData.plan;
                              }
                            })()}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {userData.created_at
                            ? new Date(userData.created_at).toLocaleDateString()
                            : "No date"}
                        </td>
                      </tr>
                    ))}
                    {users.length === 0 && (
                      <tr>
                        <td
                          colSpan="3"
                          className="px-6 py-4 text-center text-gray-500"
                        >
                          No users found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="card">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                System Status
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="font-medium text-gray-900">
                      API Status
                    </span>
                  </div>
                  <span className="text-sm text-green-600 font-medium">
                    Operational
                  </span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="font-medium text-gray-900">Database</span>
                  </div>
                  <span className="text-sm text-green-600 font-medium">
                    Connected
                  </span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="font-medium text-gray-900">
                      WhatsApp Service
                    </span>
                  </div>
                  <span className="text-sm text-blue-600 font-medium">
                    Active
                  </span>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span className="font-medium text-gray-900">
                      AI Service
                    </span>
                  </div>
                  <span className="text-sm text-purple-600 font-medium">
                    Running
                  </span>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <Icon name="Shield" className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">
                      System Information
                    </span>
                  </div>
                  <div className="text-sm text-blue-700 space-y-1">
                    <p>Last updated: {new Date().toLocaleString()}</p>
                    <p>Admin session: Active</p>
                    <p>Server time: {new Date().toLocaleTimeString()}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
