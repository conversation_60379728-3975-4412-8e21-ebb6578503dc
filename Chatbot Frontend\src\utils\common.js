// Common utility functions to reduce code duplication

/**
 * Extract user ID from various user object structures
 * @param {Object} user - User object from auth context
 * @returns {string|null} - User ID or null if not found
 */
export const getUserId = (user) => {
  return user?.user?.id || user?.profile?.id || user?.id || null;
};

/**
 * Get the effective auth ID - impersonated user ID if impersonating, otherwise current user ID
 * This is crucial for admin impersonation feature to work correctly
 * @param {Object} user - User object from auth context
 * @param {boolean} isImpersonating - Whether currently impersonating
 * @returns {string|null} - Effective auth ID to use for API calls
 */
export const getEffectiveAuthId = (user, isImpersonating = false) => {
  if (isImpersonating && user?.isImpersonated) {
    // When impersonating, use the impersonated user's ID
    return user?.user?.id || user?.profile?.auth_id || null;
  }
  // Normal case: use current user ID
  return getUserId(user);
};

/**
 * Common error handler for API calls
 * @param {Error} error - Error object
 * @param {Function} showError - Error display function
 * @param {string} defaultMessage - Default error message
 */
export const handleApiError = (
  error,
  showError,
  defaultMessage = "An error occurred",
) => {
  const message =
    error?.response?.data?.message || error?.message || defaultMessage;
  showError(message);
};

/**
 * Safe async wrapper for API calls with error handling
 * @param {Function} apiCall - Async function to execute
 * @param {Function} showError - Error display function
 * @param {string} errorMessage - Error message to display
 * @returns {Object|null} - API response or null on error
 */
export const safeApiCall = async (
  apiCall,
  showError,
  errorMessage = "Operation failed",
) => {
  try {
    return await apiCall();
  } catch (error) {
    handleApiError(error, showError, errorMessage);
    return null;
  }
};

/**
 * Common data fetcher with loading state management
 * @param {Function} fetchFn - Function that fetches data
 * @param {Function} setData - Function to set the fetched data
 * @param {Function} setLoading - Function to set loading state
 * @param {Function} showError - Error display function
 * @param {string} errorMessage - Error message on failure
 */
export const fetchWithLoading = async (
  fetchFn,
  setData,
  setLoading,
  showError,
  errorMessage = "Failed to fetch data",
) => {
  try {
    setLoading(true);
    const result = await fetchFn();
    if (result?.success) {
      setData(result.data);
    } else {
      showError(result?.error || errorMessage);
    }
  } catch (error) {
    handleApiError(error, showError, errorMessage);
  } finally {
    setLoading(false);
  }
};

/**
 * Format large numbers for display
 * @param {number} num - Number to format
 * @returns {string} - Formatted number string
 */
export const formatNumber = (num) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
  if (num >= 1000) return (num / 1000).toFixed(1) + "K";
  return num?.toString() || "0";
};

/**
 * Debounce function for search inputs
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, delay = 500) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Validate required form fields
 * @param {Object} data - Form data object
 * @param {Array} requiredFields - Array of required field names
 * @param {Function} showError - Error display function
 * @returns {boolean} - True if validation passes
 */
export const validateRequired = (data, requiredFields, showError) => {
  for (const field of requiredFields) {
    if (
      !data[field] ||
      (typeof data[field] === "string" && !data[field].trim())
    ) {
      showError(
        `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      );
      return false;
    }
  }
  return true;
};
