import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu";
import Icon from "../../components/ui/icon";

export default function UserManagement({
  users,
  onFetchUsers,
  onFetchUserDetails,
  onChangePlan,
  onSuspendUser,
  onResetUserQuota,
  onImpersonate,
  userDetails,
  selectedUser,
  subscriptionHistory,
  subscriptionHistoryLoading,
  onCloseUserDetails,
}) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle refresh with loading state
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onFetchUsers();
    } finally {
      // Continue spinning for a short period after refresh completes
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000); // Continue spinning for 1 second after completion
    }
  };

  // User row component with improved styling
  const UserRow = ({
    user: userData,
    onViewDetails,
    onChangePlan,
    onSuspend,
    onResetQuota,
    onImpersonate,
  }) => {
    const [selectedPlan, setSelectedPlan] = useState(userData.plan);

    const formatEndDate = (endDate) => {
      if (!endDate) return "No expiry";
      const date = new Date(endDate);
      const today = new Date();
      const diffTime = date - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) return "Expired";
      if (diffDays === 0) return "Expires today";
      if (diffDays === 1) return "Expires tomorrow";
      return `${diffDays} days left`;
    };

    const getPlanBadgeColor = (plan) => {
      switch (plan) {
        case "admin":
          return "bg-red-100 text-red-800";
        case "besar":
          return "bg-purple-100 text-purple-800";
        case "popular":
          return "bg-blue-100 text-blue-800";
        case "kecil":
          return "bg-green-100 text-green-800";
        case "trial":
          return "bg-yellow-100 text-yellow-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    };

    return (
      <tr className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
        <td className="px-6 py-4">
          <div className="flex items-center gap-3">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-medium ${
                userData.plan === "admin"
                  ? "bg-red-500"
                  : userData.plan === "besar"
                    ? "bg-purple-500"
                    : userData.plan === "popular"
                      ? "bg-blue-500"
                      : userData.plan === "kecil"
                        ? "bg-green-500"
                        : userData.plan === "trial"
                          ? "bg-yellow-500"
                          : "bg-gray-500"
              }`}
            >
              {(userData.display_name || userData.email || userData.auth_id)
                .charAt(0)
                .toUpperCase()}
            </div>
            <div>
              <p className="font-medium text-gray-900">
                {userData.display_name || userData.auth_id}
              </p>
              <p className="text-sm text-gray-500">
                {userData.email || "No email"}
              </p>
            </div>
          </div>
        </td>
        <td className="px-6 py-4">
          <Badge className={getPlanBadgeColor(userData.plan)}>
            {(() => {
              switch (userData.plan) {
                case "free":
                  return "Kedai Free Plan";
                case "admin":
                  return "Kedai Admin Plan";
                case "besar":
                  return "Kedai Besar Plan";
                case "popular":
                  return "Kedai Popular Plan";
                case "kecil":
                  return "Kedai Kecil Plan";
                case "trial":
                  return "Kedai Trial Plan";
                default:
                  return userData.plan;
              }
            })()}
          </Badge>
        </td>
        <td className="px-6 py-4">
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${userData.is_suspended ? "bg-red-500" : "bg-green-500"}`}
            ></div>
            <span
              className={`text-sm ${userData.is_suspended ? "text-red-600" : "text-green-600"}`}
            >
              {userData.is_suspended ? "Suspended" : "Active"}
            </span>
          </div>
        </td>
        <td className="px-6 py-4 text-center">
          <div className="text-sm">
            {userData.subscription_end_date ? (
              <>
                <p
                  className={`font-medium ${
                    userData.days_remaining > 7
                      ? "text-green-600"
                      : userData.days_remaining > 3
                        ? "text-yellow-600"
                        : "text-red-600"
                  }`}
                >
                  {formatEndDate(userData.subscription_end_date)}
                </p>
                <p className="text-gray-500">
                  {new Date(
                    userData.subscription_end_date,
                  ).toLocaleDateString()}
                </p>
              </>
            ) : (
              <p className="text-gray-500">No expiry</p>
            )}
          </div>
        </td>
        <td className="px-6 py-4">
          <Select
            value={selectedPlan}
            onValueChange={(value) => {
              setSelectedPlan(value);
              onChangePlan(userData.auth_id, value);
            }}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="free">Free</SelectItem>
              <SelectItem value="kecil">Kecil</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
              <SelectItem value="besar">Besar</SelectItem>
              <SelectItem value="trial">Trial</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
            </SelectContent>
          </Select>
        </td>
        <td className="px-6 py-4 text-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100"
              >
                <Icon name="MoreHorizontal" className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem
                onClick={() => onViewDetails(userData.auth_id)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <Icon name="Eye" className="w-4 h-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  onSuspend(userData.auth_id, !userData.is_suspended)
                }
                className={`flex items-center gap-2 cursor-pointer ${
                  userData.is_suspended
                    ? "text-green-600 hover:text-green-700 hover:bg-green-50"
                    : "text-red-600 hover:text-red-700 hover:bg-red-50"
                }`}
              >
                {userData.is_suspended ? (
                  <>
                    <Icon name="Check" className="w-4 h-4" />
                    Unsuspend User
                  </>
                ) : (
                  <>
                    <Icon name="AlertCircle" className="w-4 h-4" />
                    Suspend User
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onResetQuota(userData.auth_id)}
                className="flex items-center gap-2 cursor-pointer text-orange-600 hover:text-orange-700 hover:bg-orange-50"
              >
                <Icon name="RefreshCw" className="w-4 h-4" />
                Reset Quota
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() =>
                  onImpersonate &&
                  onImpersonate(
                    userData.auth_id,
                    userData.display_name || userData.email,
                  )
                }
                className={`flex items-center gap-2 cursor-pointer text-purple-600 hover:text-purple-700 hover:bg-purple-50 ${
                  userData.is_suspended ? "opacity-50 cursor-not-allowed" : ""
                }`}
                disabled={userData.is_suspended}
                title={
                  userData.is_suspended
                    ? "Cannot impersonate suspended user"
                    : "Impersonate this user"
                }
              >
                <Icon name="UserCheck" className="w-4 h-4" />
                Impersonate
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </td>
      </tr>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              User Management
            </h1>
            <p className="text-gray-600">
              Manage user accounts, plans, and permissions
            </p>
          </div>
          <div className="flex flex-wrap gap-3">
            <button className="btn-secondary" onClick={handleRefresh}>
              <Icon
                name="RefreshCw"
                className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
              />
              Refresh All Data
            </button>
          </div>
        </div>
      </div>

      {/* Alert Messages - Handled by parent AdminDashboard */}

      <Card className="bg-white border-0 shadow-lg">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>All Users ({users.length})</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subscription
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Change Plan
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {users.map((userData) => (
                  <UserRow
                    key={userData.auth_id}
                    user={userData}
                    onViewDetails={onFetchUserDetails}
                    onChangePlan={onChangePlan}
                    onSuspend={onSuspendUser}
                    onResetQuota={onResetUserQuota}
                    onImpersonate={onImpersonate}
                  />
                ))}
                {users.length === 0 && (
                  <tr>
                    <td
                      colSpan="6"
                      className="px-6 py-4 text-center text-gray-500"
                    >
                      No users found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* User Details Modal */}
      {userDetails && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={onCloseUserDetails}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-5 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Icon name="Users" className="w-5 h-5" />
                  User Details - {selectedUser}
                </h3>
                <button
                  onClick={onCloseUserDetails}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Icon name="X" className="h-5 w-5" />
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-xl p-4">
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Icon name="Users" className="w-4 h-4 text-blue-600" />
                      Profile Information
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <span className="text-gray-600 text-sm">Name:</span>
                        <span className="font-medium text-gray-900">
                          {userDetails?.profile?.display_name || "N/A"}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <span className="text-gray-600 text-sm">Email:</span>
                        <span className="font-medium text-gray-900">
                          {userDetails?.profile?.email || "N/A"}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <span className="text-gray-600 text-sm">Plan:</span>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            userDetails?.profile?.plan === "admin"
                              ? "bg-red-100 text-red-800"
                              : userDetails?.profile?.plan === "besar"
                                ? "bg-purple-100 text-purple-800"
                                : userDetails?.profile?.plan === "popular"
                                  ? "bg-blue-100 text-blue-800"
                                  : userDetails?.profile?.plan === "kecil"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {(() => {
                            switch (userDetails?.profile?.plan) {
                              case "free":
                                return "Kedai Free Plan";
                              case "admin":
                                return "Kedai Admin Plan";
                              case "besar":
                                return "Kedai Besar Plan";
                              case "popular":
                                return "Kedai Popular Plan";
                              case "kecil":
                                return "Kedai Kecil Plan";
                              case "trial":
                                return "Kedai Trial Plan";
                              default:
                                return userDetails?.profile?.plan;
                            }
                          })()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <span className="text-gray-600 text-sm">Phone:</span>
                        <span className="font-medium text-gray-900">
                          {userDetails?.profile?.phone || "N/A"}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600 text-sm">Joined:</span>
                        <span className="font-medium text-gray-900">
                          {userDetails?.profile?.created_at
                            ? new Date(
                                userDetails.profile.created_at,
                              ).toLocaleDateString()
                            : "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-xl p-4">
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Icon name="Users" className="w-4 h-4 text-green-600" />
                      Subscription Information
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <span className="text-gray-600 text-sm">
                          Current Plan:
                        </span>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            userDetails?.subscription?.plan === "admin"
                              ? "bg-red-100 text-red-800"
                              : userDetails?.subscription?.plan === "besar"
                                ? "bg-purple-100 text-purple-800"
                                : userDetails?.subscription?.plan === "popular"
                                  ? "bg-blue-100 text-blue-800"
                                  : userDetails?.subscription?.plan === "kecil"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {(() => {
                            switch (userDetails?.subscription?.plan) {
                              case "free":
                                return "Kedai Free Plan";
                              case "admin":
                                return "Kedai Admin Plan";
                              case "besar":
                                return "Kedai Besar Plan";
                              case "popular":
                                return "Kedai Popular Plan";
                              case "kecil":
                                return "Kedai Kecil Plan";
                              case "trial":
                                return "Kedai Trial Plan";
                              default:
                                return userDetails?.subscription?.plan;
                            }
                          })()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <span className="text-gray-600 text-sm">End Date:</span>
                        <span className="font-medium text-gray-900">
                          {userDetails?.subscription?.endDate &&
                          userDetails.subscription.endDate !== "N/A"
                            ? new Date(
                                userDetails.subscription.endDate,
                              ).toLocaleDateString()
                            : "No expiry"}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600 text-sm">Status:</span>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            userDetails?.subscription?.isExpired
                              ? "bg-red-100 text-red-800"
                              : userDetails?.subscription?.isSuspended
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-green-100 text-green-800"
                          }`}
                        >
                          {userDetails?.subscription?.isExpired
                            ? "Expired"
                            : userDetails?.subscription?.isSuspended
                              ? "Suspended"
                              : "Active"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-xl p-4">
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Icon name="Users" className="w-4 h-4 text-purple-600" />
                      Usage Statistics
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-blue-200">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-gray-700 text-sm">
                            Knowledge Entries
                          </span>
                        </div>
                        <span className="text-lg font-bold text-blue-600">
                          {userDetails?.statistics?.knowledgeEntries || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-200">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-gray-700 text-sm">
                            Contacts
                          </span>
                        </div>
                        <span className="text-lg font-bold text-green-600">
                          {userDetails?.statistics?.contacts || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-200">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                          <span className="text-gray-700 text-sm">Orders</span>
                        </div>
                        <span className="text-lg font-bold text-purple-600">
                          {userDetails?.statistics?.orders || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-orange-200">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                          <span className="text-gray-700 text-sm">
                            Total Tokens
                          </span>
                        </div>
                        <span className="text-lg font-bold text-orange-600">
                          {userDetails?.statistics?.totalTokens || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-teal-200">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-teal-500 rounded-full"></div>
                          <span className="text-gray-700 text-sm">
                            This Month Tokens
                          </span>
                        </div>
                        <span className="text-lg font-bold text-teal-600">
                          {userDetails?.statistics?.thisMonthTokens || 0}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Debug Information */}
                  {import.meta.env.DEV && (
                    <div className="bg-gray-50 rounded-xl p-4">
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <Icon
                          name="AlertCircle"
                          className="w-4 h-4 text-yellow-600"
                        />
                        Debug Info (Dev Only)
                      </h4>
                      <details className="cursor-pointer">
                        <summary className="text-sm text-gray-600 hover:text-gray-800 py-2">
                          View Raw API Response
                        </summary>
                        <pre className="mt-2 text-xs bg-white p-3 rounded-lg border overflow-auto max-h-64 font-mono">
                          {JSON.stringify(userDetails?.raw, null, 2)}
                        </pre>
                      </details>
                      {userDetails?.apiError && (
                        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                          API Error: {userDetails.apiError}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Subscription History Section */}
              <div className="mt-8 border-t border-gray-200 pt-6">
                <div className="bg-gray-50 rounded-xl p-4">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Icon name="Users" className="w-4 h-4 text-indigo-600" />
                    Subscription History
                  </h4>

                  {subscriptionHistoryLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="w-6 h-6 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                      <span className="ml-2 text-gray-600">
                        Loading subscription history...
                      </span>
                    </div>
                  ) : subscriptionHistory && subscriptionHistory.length > 0 ? (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {subscriptionHistory.map((entry, index) => (
                        <div
                          key={index}
                          className="bg-white rounded-lg p-4 border border-gray-200 hover:border-indigo-200 transition-colors"
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span
                                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    entry.action?.includes("upgrade") ||
                                    entry.action?.includes("set")
                                      ? "bg-green-100 text-green-800"
                                      : entry.action?.includes("downgrade") ||
                                          entry.action?.includes("cancel")
                                        ? "bg-red-100 text-red-800"
                                        : entry.action?.includes("extend")
                                          ? "bg-blue-100 text-blue-800"
                                          : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {entry.action ||
                                    entry.event_type ||
                                    "Unknown Action"}
                                </span>
                                {entry.plan && (
                                  <span
                                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                      entry.plan === "admin"
                                        ? "bg-red-100 text-red-800"
                                        : entry.plan === "besar"
                                          ? "bg-purple-100 text-purple-800"
                                          : entry.plan === "popular"
                                            ? "bg-blue-100 text-blue-800"
                                            : entry.plan === "kecil"
                                              ? "bg-green-100 text-green-800"
                                              : "bg-gray-100 text-gray-800"
                                    }`}
                                  >
                                    {(() => {
                                      switch (entry.plan) {
                                        case "free":
                                          return "Kedai Free Plan";
                                        case "admin":
                                          return "Kedai Admin Plan";
                                        case "besar":
                                          return "Kedai Besar Plan";
                                        case "popular":
                                          return "Kedai Popular Plan";
                                        case "kecil":
                                          return "Kedai Kecil Plan";
                                        case "trial":
                                          return "Kedai Trial Plan";
                                        default:
                                          return entry.plan;
                                      }
                                    })()}
                                  </span>
                                )}
                              </div>
                              {entry.notes && (
                                <p className="text-sm text-gray-600 mb-1">
                                  {entry.notes}
                                </p>
                              )}
                              {entry.reason && (
                                <p className="text-sm text-gray-600 mb-1">
                                  Reason: {entry.reason}
                                </p>
                              )}
                              {entry.details && (
                                <p className="text-xs text-gray-500">
                                  {entry.details}
                                </p>
                              )}
                            </div>
                            <div className="text-right text-xs text-gray-500 ml-4">
                              <div>
                                {entry.created_at
                                  ? new Date(
                                      entry.created_at,
                                    ).toLocaleDateString()
                                  : "N/A"}
                              </div>
                              <div>
                                {entry.created_at
                                  ? new Date(
                                      entry.created_at,
                                    ).toLocaleTimeString()
                                  : ""}
                              </div>
                              {entry.admin_id && (
                                <div className="text-indigo-600 font-medium">
                                  by {entry.admin_id}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Icon
                        name="Users"
                        className="w-12 h-12 mx-auto mb-4 text-gray-300"
                      />
                      <p>No subscription history available</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-200 space-y-3">
                <Button
                  onClick={() => onResetUserQuota(selectedUser)}
                  variant="outline"
                  className="w-full border-orange-200 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                >
                  <Icon name="RefreshCw" className="w-4 h-4 mr-2" />
                  Reset User Quota Usage
                </Button>
                <button
                  onClick={onCloseUserDetails}
                  className="w-full bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
