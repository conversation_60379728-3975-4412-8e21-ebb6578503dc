// Media processing utilities extracted from routes/ai.js for better modularity

import fs from "fs";
import path from "path";
import os from "os";
import axios from "axios";
import FormData from "form-data";
import OpenAI from "openai";
import logger from "./logger.js";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Prepare audio file for Whisper API processing
 */
export function prepareAudioForWhisper(audioBuffer, mimeType) {
  try {
    logger.debug(`Preparing audio for Whisper | Size: ${audioBuffer.length} bytes`);
    
    // Validate audio buffer
    if (!audioBuffer || audioBuffer.length === 0) {
      throw new Error('Empty audio buffer received');
    }

    // File size validation
    const maxSize = 25 * 1024 * 1024; // 25MB (OpenAI's limit)
    if (audioBuffer.length > maxSize) {
      throw new Error(`Audio file too large: ${audioBuffer.length} bytes. Maximum allowed: ${maxSize} bytes`);
    }

    // Minimum size validation
    if (audioBuffer.length < 1024) {
      throw new Error('Audio file too small (minimum 1KB required)');
    }

    // Enhanced MIME type to extension mapping
    const audioExtensions = {
      'audio/mpeg': '.mp3',
      'audio/mp3': '.mp3',
      'audio/wav': '.wav',
      'audio/wave': '.wav',
      'audio/x-wav': '.wav',
      'audio/vnd.wave': '.wav',
      'audio/m4a': '.m4a',
      'audio/mp4': '.m4a',
      'audio/aac': '.aac',
      'audio/x-aac': '.aac',
      'audio/ogg': '.ogg',
      'audio/opus': '.ogg',
      'audio/webm': '.webm',
      'audio/flac': '.flac',
      'audio/x-flac': '.flac',
      'audio/amr': '.amr',
      'audio/3gpp': '.3gp',
      'audio/3gpp2': '.3gp2'
    };

    // Get file extension, default to .ogg for WhatsApp voice messages
    const extension = audioExtensions[mimeType] || '.ogg';
    
    logger.debug(`Audio format detection | MIME: ${mimeType} | Extension: ${extension}`);

    // Generate secure temporary filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const tempFilePath = path.join(os.tmpdir(), `whatsapp_voice_${timestamp}_${randomId}${extension}`);

    // Write buffer to temporary file
    fs.writeFileSync(tempFilePath, audioBuffer);
    logger.debug(`Audio file created: ${tempFilePath}`);

    // Verify file was created successfully
    const stats = fs.statSync(tempFilePath);
    if (stats.size !== audioBuffer.length) {
      throw new Error(`File size mismatch: expected ${audioBuffer.length}, got ${stats.size}`);
    }

    logger.debug(`Audio file prepared successfully | Size: ${stats.size} bytes`);
    return tempFilePath;

  } catch (error) {
    logger.error('Error preparing audio for Whisper:', error);
    throw new Error(`Audio preparation failed: ${error.message}`);
  }
}

/**
 * Process voice message using OpenAI Whisper API
 */
export async function processVoiceMessageWithAI(audioBuffer, mimeType) {
  const processingStartTime = Date.now();
  let tempFilePath = null;
  
  try {
    logger.debug(`Starting voice message processing | Size: ${audioBuffer.length} bytes`);
    
    // Prepare audio file for processing
    tempFilePath = prepareAudioForWhisper(audioBuffer, mimeType);
    
    logger.debug(`Sending to OpenAI Whisper | File: ${path.basename(tempFilePath)}`);
    
    // Verify file exists before processing
    if (!fs.existsSync(tempFilePath)) {
      throw new Error(`Temporary audio file not found: ${tempFilePath}`);
    }
    
    const fileStats = fs.statSync(tempFilePath);
    logger.debug(`Preparing audio file for Whisper API: ${tempFilePath} (${fileStats.size} bytes)`);
    
    // Use form-data to create a proper multipart form for OpenAI API
    const formData = new FormData();
    formData.append('file', fs.createReadStream(tempFilePath), {
      filename: path.basename(tempFilePath),
      contentType: mimeType || 'audio/ogg'
    });
    formData.append('model', 'whisper-1');
    formData.append('response_format', 'json');
    formData.append('temperature', '0.1');
    formData.append('prompt', 'This is a voice message from a customer to a business in Malaysia. Please transcribe accurately in the original language, paying special attention to Malaysian English patterns including: common particles like "lah", "lor", "meh", "leh", "ah", "mah"; code-switching between English and local languages (Malay, Chinese, Tamil); Malaysian slang terms like "alamak", "aiyo", "wah", "habis", "can or not", "how much ah", "got or not", "like that also can", "cannot lah"; colloquial expressions and shortened forms; product names, numbers, addresses, and business-related terms; and mixed language phrases that are common in Malaysian conversation.');
    
    logger.debug(`Sending multipart form to OpenAI Whisper API`);
    
    // Make direct HTTP request to OpenAI API
    const response = await axios.post(
      'https://api.openai.com/v1/audio/transcriptions',
      formData,
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Accept': 'application/json',
          ...formData.getHeaders(),
        },
        timeout: 60000, // 60 second timeout for longer audio files
        maxContentLength: 25 * 1024 * 1024, // 25MB limit
      }
    );

    if (!response.data || !response.data.text) {
      throw new Error('Invalid response from Whisper API: Missing transcription text');
    }

    logger.info(`Whisper API call successful`);
    
    const processingTime = Date.now() - processingStartTime;
    logger.info(`Voice transcription completed in ${processingTime}ms`);
    
    const transcriptionText = response.data.text.trim();
    const detectedLanguage = response.data.language || 'unknown';
    const duration = response.data.duration || 0;

    logger.debug(`Transcription result | Language: ${detectedLanguage} | Duration: ${duration}s | Length: ${transcriptionText.length} chars`);
    logger.debug(`Transcribed text: "${transcriptionText.substring(0, 100)}${transcriptionText.length > 100 ? '...' : ''}"`);

    return {
      text: `[Voice message transcribed] ${transcriptionText}`,
      rawTranscription: transcriptionText, // For knowledge base search
      language: detectedLanguage,
      duration: duration,
      processingTime: processingTime
    };

  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    logger.error(`Voice processing failed in ${processingTime}ms:`, error);
    
    // Return a fallback message for voice messages that couldn't be processed
    return {
      text: "[Voice message - transcription failed]",
      rawTranscription: null,
      language: 'unknown',
      duration: 0,
      processingTime: processingTime,
      error: error.message
    };
  } finally {
    // Always clean up temporary file
    if (tempFilePath) {
      cleanupTempFile(tempFilePath);
    }
  }
}

/**
 * Process image using OpenAI GPT-4 Vision API
 */
export async function processImageWithAI(imageBuffer, mimeType) {
  try {
    // Convert buffer to base64
    const base64Image = imageBuffer.toString('base64');
    
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this image and describe what you see. If it contains text, transcribe it accurately. If it shows products, describe them in detail. Focus on any business-relevant information like prices, promotions, contact details, or product features."
            },
            {
              type: "image_url",
              image_url: {
                url: `data:${mimeType};base64,${base64Image}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 500,
      temperature: 0.1,
    });

    const description = response.choices[0].message.content;
    return `[Image description] ${description}`;
    
  } catch (error) {
    logger.error("Error processing image with AI:", error);
    return "[Image received - analysis failed]";
  }
}

/**
 * Clean up temporary files
 */
export function cleanupTempFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.debug(`Temporary file cleaned up: ${filePath}`);
    }
  } catch (error) {
    logger.warn(`Failed to cleanup temporary file ${filePath}:`, error);
  }
}

/**
 * Enhanced media content processor
 */
export async function processMediaContentEnhanced(messageContent, messageType, accessToken, authId = null, phoneNumber = null) {
  const processingStartTime = Date.now();
  
  try {
    logger.debug(`Processing ${messageType} content | Media ID: ${messageContent.id}`);
    
    // Download media from WhatsApp
    const { downloadWhatsAppMedia } = await import('./whatsapp.js');
    const mediaData = await downloadWhatsAppMedia(messageContent.id, accessToken);
    
    let processedText = '';
    let rawTranscription = null;
    let mediaInfo = null;
    
    // Store media info for chat history
    if (authId && phoneNumber) {
      const timestamp = Date.now();
      const random = Math.round(Math.random() * 1E9);
      let extension = '.bin';
      
      // Determine extension based on mime type
      if (mediaData.mimeType.includes('jpeg') || mediaData.mimeType.includes('jpg')) extension = '.jpg';
      else if (mediaData.mimeType.includes('png')) extension = '.png';
      else if (mediaData.mimeType.includes('gif')) extension = '.gif';
      else if (mediaData.mimeType.includes('webp')) extension = '.webp';
      else if (mediaData.mimeType.includes('mp3')) extension = '.mp3';
      else if (mediaData.mimeType.includes('wav')) extension = '.wav';
      else if (mediaData.mimeType.includes('ogg')) extension = '.ogg';
      else if (mediaData.mimeType.includes('m4a')) extension = '.m4a';
      else if (mediaData.mimeType.includes('aac')) extension = '.aac';
      else if (mediaData.mimeType.includes('mp4')) extension = '.mp4';
      else if (mediaData.mimeType.includes('webm')) extension = '.webm';
      
      const filename = `whatsapp-${messageType}-${timestamp}-${random}${extension}`;
      mediaInfo = {
        filename,
        mediaType: messageType,
        mimeType: mediaData.mimeType,
        fileSize: mediaData.fileSize,
        fileUrl: null // Would be set after storage upload
      };
    }
    
    // Process based on media type
    switch (messageType) {
      case 'image':
        logger.debug(`Processing image with AI vision`);
        processedText = await processImageWithAI(mediaData.buffer, mediaData.mimeType);
        break;
        
      case 'audio':
        logger.debug(`Processing voice message with enhanced AI`);
        const voiceResult = await processVoiceMessageWithAI(mediaData.buffer, mediaData.mimeType);
        processedText = voiceResult.text;
        rawTranscription = voiceResult.rawTranscription; // For knowledge base search
        
        logger.debug(`Voice processing complete | Duration: ${voiceResult.duration}s | Language: ${voiceResult.language}`);
        logger.debug(`Raw transcription for KB search: "${rawTranscription}"`);
        break;
        
      case 'video':
        logger.debug(`Processing video content`);
        processedText = "[Video message received - video analysis not yet implemented]";
        break;
        
      default:
        logger.debug(`Processing ${messageType} content`);
        processedText = `[${messageType} message received]`;
    }
    
    const processingTime = Date.now() - processingStartTime;
    logger.debug(`Media processing completed in ${processingTime}ms | Type: ${messageType} | Result length: ${processedText.length}`);
    
    return {
      processedText,
      rawTranscription,
      mediaInfo,
      processingTime
    };
    
  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    logger.error(`Media processing failed in ${processingTime}ms:`, error);
    
    return {
      processedText: `[${messageType} message - processing failed]`,
      rawTranscription: null,
      mediaInfo: null,
      processingTime,
      error: error.message
    };
  }
}

// Legacy function for backward compatibility
export async function processAudioWithAI(audioBuffer, mimeType) {
  try {
    const result = await processVoiceMessageWithAI(audioBuffer, mimeType);
    return result.text;
  } catch (error) {
    logger.error("Legacy processAudioWithAI error:", error);
    throw error;
  }
} 