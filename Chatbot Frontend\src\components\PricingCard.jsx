import { Check } from "lucide-react";

export default function PricingCard({
  plan,
  price,
  period,
  originalPrice,
  description,
  features,
  highlight,
  popular,
  buttonText = "Choose Plan",
}) {
  return (
    <div
      className={`relative rounded-3xl p-8 transition-all duration-300 hover:scale-105 flex flex-col min-h-[600px] card hover:shadow-2xl border border-gray-200`}
    >
      {/* Popular Badge */}
      {popular ? (
        <div className="w-1/2 mx-auto text-center mb-5 h-10">
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
            Most Popular
          </div>
        </div>
      ) : (
        <div className="w-1/2 mx-auto text-center mb-5 h-10"></div>
      )}

      {/* Plan Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan}</h3>
        <p className="text-gray-600 text-sm leading-relaxed mb-6">
          {description}
        </p>

        {/* Pricing */}
        <div className="mb-6">
          <div className="flex items-center justify-center gap-2 mb-2">
            <span className="text-5xl font-extrabold gradient-text">
              {price}
            </span>
            {period && <span className="text-gray-500 text-lg">{period}</span>}
          </div>
          {originalPrice && (
            <div className="flex items-center justify-center gap-2">
              <span className="text-gray-400 text-lg line-through">
                {originalPrice}
                {period}
              </span>
              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-semibold">
                Save 50%
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Features */}
      <div className="mb-8 flex-grow">
        <ul className="space-y-4">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center mt-0.5">
                <Check className="w-3 h-3 text-white" />
              </div>
              <span className="text-gray-700 text-sm leading-relaxed">
                {feature}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* CTA Button - Sticks to bottom */}
      <div className="text-center mt-auto">
        <button
          className={`w-full transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 ${
            highlight
              ? "btn-primary text-lg py-4"
              : "btn-secondary text-lg py-4"
          }`}
        >
          {buttonText}
        </button>

        {plan !== "Enterprise" && (
          <p className="text-gray-500 text-xs mt-3">No credit card required</p>
        )}
      </div>

      {/* Background Decoration for Highlighted Card */}
      {highlight && (
        <>
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>
          <div className="absolute bottom-0 left-0 w-32 h-32 bg-indigo-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>
        </>
      )}
    </div>
  );
}
