import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { aiService } from "../../services/ai";
import { useMobileAlertMigration } from "../../contexts/MobileAlertContext";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { Search, MessageCircle, Phone, RefreshCw, Edit, X } from "lucide-react";
import { LazyAvatar, useDebounce, usePerformanceMonitor } from "../../components/LazyImage";


/**
 * Mobile Contacts Component
 * WhatsApp-like contacts list with search and last message preview
 */
export default function MobileContacts({ user }) {
  const navigate = useNavigate();
  const { showError, showQuickSuccess, showQuickError } = useMobileAlertMigration();

  // Performance monitoring
  usePerformanceMonitor('MobileContacts');
  
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const showLoadingState = useDelayedLoading(loading, 200);

  // Pagination and performance
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const CONTACTS_PER_PAGE = 20;

  // Contact management state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingContact, setEditingContact] = useState(null);
  const [editForm, setEditForm] = useState({
    name: "",
    notes: "",
    tags: ""
  });

  // Fetch contacts with pagination
  const fetchContacts = async (pageNum = 1, append = false) => {
    if (!append) setLoading(true);
    else setLoadingMore(true);

    try {


      const userId = user?.user?.id || user?.profile?.id || user?.id;
      if (!userId) return;

      const data = await aiService.getContacts({
        authId: userId,
        limit: CONTACTS_PER_PAGE,
        offset: (pageNum - 1) * CONTACTS_PER_PAGE
      });

      if (data.success) {
        // Sort contacts by last message time (most recent first)
        const sortedContacts = data.data.sort((a, b) => {
          const aTime = new Date(a.last_message_at || a.created_at);
          const bTime = new Date(b.last_message_at || b.created_at);
          return bTime - aTime;
        });

        if (append) {
          setContacts(prev => [...prev, ...sortedContacts]);
        } else {
          setContacts(sortedContacts);
        }

        // Check if there are more contacts
        setHasMore(sortedContacts.length === CONTACTS_PER_PAGE);
        setPage(pageNum);
      } else {
        showError(data.error);
      }
    } catch {
      showError("Failed to fetch contacts");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more contacts
  const loadMoreContacts = () => {
    if (!loadingMore && hasMore) {
      fetchContacts(page + 1, true);
    }
  };

  // Refresh contacts (for pull-to-refresh)
  const refreshContacts = async () => {
    setRefreshing(true);
    setPage(1);
    setHasMore(true);
    await fetchContacts(1, false);
    setTimeout(() => setRefreshing(false), 1000); // Spin for 1 second
  };

  // Handle scroll for infinite loading
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

    if (isNearBottom && hasMore && !loadingMore) {
      loadMoreContacts();
    }
  };

  useEffect(() => {
    if (user) {
      fetchContacts();
    }
  }, [user]);

  // Filter contacts based on debounced search query
  const filteredContacts = contacts.filter(contact =>
    contact.name?.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
    contact.phone_number?.includes(debouncedSearchQuery)
  );

  // Format time for last message
  const formatLastMessageTime = (timestamp) => {
    if (!timestamp) return "";
    
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInHours = (now - messageTime) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - messageTime) / (1000 * 60));
      return diffInMinutes < 1 ? "now" : `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return diffInDays === 1 ? "1d" : `${diffInDays}d`;
    }
  };

  // Handle contact click - navigate to chat
  const handleContactClick = (contact) => {
    navigate(`/dashboard/contacts/chat/${contact.id}`);
  };

  // Handle edit contact directly from list
  const handleEditContact = (contact, event) => {
    event.stopPropagation();
    setEditingContact(contact);
    setEditForm({
      name: contact.name || "",
      notes: contact.notes || "",
      tags: contact.tags ? contact.tags.join(", ") : ""
    });
    setShowEditModal(true);
  };

  // Save contact changes
  const saveContactChanges = async () => {
    if (!editingContact) return;

    try {
      const userId = user?.user?.id || user?.profile?.id || user?.id;
      const updateData = {
        authId: userId,
        name: editForm.name.trim() || null,
        notes: editForm.notes.trim() || null,
        tags: editForm.tags.split(",").map(tag => tag.trim()).filter(tag => tag)
      };

      const data = await aiService.updateContact(editingContact.id, updateData);

      if (data.success) {
        // Update local contacts list
        setContacts(prev => prev.map(contact =>
          contact.id === editingContact.id
            ? { ...contact, ...updateData }
            : contact
        ));
        showQuickSuccess("Contact updated!");
        setShowEditModal(false);
        setEditingContact(null);
      } else {
        showQuickError(data.error || "Failed to update contact");
      }
    } catch {
      showError("Failed to update contact");
    }
  };



  if (showLoadingState) {
    return (
      <div className="p-4 space-y-3">
        {/* Search skeleton */}
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded-lg mb-4"></div>
          {/* Contact skeletons */}
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex items-center gap-3 p-3">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-3 bg-gray-200 rounded w-8"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col bg-white overflow-hidden">
      {/* Custom CSS for animations */}
      <style jsx="true">{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .contact-item {
          animation: fadeInUp 0.3s ease-out forwards;
          opacity: 0;
        }
      `}</style>
      {/* Search Bar */}
      <div className="px-4 py-3 border-b border-gray-100 sticky top-0 bg-white z-10 shadow-sm">
        <div className="flex items-center gap-3">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-field"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 hover:bg-gray-200 rounded-full"
              >
                <X className="w-4 h-4 text-gray-400" />
              </button>
            )}
          </div>
          <button
            onClick={refreshContacts}
            disabled={refreshing}
            className="flex items-center justify-center p-2.5 hover:bg-gray-100 rounded-full transition-all duration-200 shadow-sm bg-white border border-gray-200"
          >
            <RefreshCw className={`w-5 h-5 text-gray-600 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Contacts List */}
      <div className="flex-1 overflow-y-auto" onScroll={handleScroll}>
        {filteredContacts.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            {contacts.length === 0 ? (
              <>
                <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6 shadow-lg">
                  <MessageCircle className="w-10 h-10 text-blue-500" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  No contacts yet
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed max-w-sm">
                  Contacts will appear here when people start messaging your chatbot. Share your chatbot link to get started!
                </p>
              </>
            ) : (
              <>
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6 shadow-lg">
                  <Search className="w-10 h-10 text-gray-500" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  No matching contacts
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed max-w-sm">
                  Try adjusting your search terms or check the spelling
                </p>
              </>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredContacts.map((contact, index) => (
              <div
                key={contact.id}
                onClick={() => handleContactClick(contact)}
                className="contact-item flex items-center gap-4 p-4 hover:bg-gray-50 active:bg-gray-100 transition-all duration-200 cursor-pointer group border-l-4 border-transparent hover:border-blue-500"
                style={{
                  animationDelay: `${index * 50}ms`
                }}
              >
                {/* Contact Avatar */}
                <div className="relative flex-shrink-0">
                  <LazyAvatar
                    name={contact.name || contact.phone_number}
                    className="w-14 h-14 shadow-sm"
                  />
                  {/* Online status indicator */}
                  {contact.is_active && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full shadow-sm"></div>
                  )}
                </div>

                {/* Contact Info */}
                <div className="flex-1 min-w-0 py-1">
                  {/* Name and Time Row */}
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-semibold text-gray-900 truncate text-base">
                      {contact.name || "Unknown Contact"}
                    </h4>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <span className="text-xs text-gray-500">
                        {formatLastMessageTime(contact.last_message_at)}
                      </span>
                      {contact.unread_count > 0 && (
                        <div className="min-w-[20px] h-5 bg-blue-600 rounded-full flex items-center justify-center px-1.5">
                          <span className="text-xs text-white font-medium">
                            {contact.unread_count > 99 ? "99+" : contact.unread_count}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Phone Number Row */}
                  <div className="flex items-center gap-2 mb-1">
                    <Phone className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                    <span className="text-sm text-gray-600 truncate font-mono">
                      +{contact.phone_number}
                    </span>
                  </div>

                  {/* Last message preview */}
                  {contact.last_message && (
                    <p className="text-sm text-gray-500 truncate leading-relaxed">
                      {contact.last_message}
                    </p>
                  )}

                  {/* Tags */}
                  {contact.tags && contact.tags.length > 0 && (
                    <div className="flex gap-1 mt-2">
                      {contact.tags.slice(0, 2).map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="inline-block px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                      {contact.tags.length > 2 && (
                        <span className="text-xs text-gray-500 px-1 py-1">
                          +{contact.tags.length - 2} more
                        </span>
                      )}
                    </div>
                  )}
                </div>

                {/* Action button */}
                <div className="flex-shrink-0">
                  <button
                    onClick={(e) => handleEditContact(contact, e)}
                    className=""
                    title="Edit contact"
                  >
                    <Edit className="ml-5 w-5 h-5 text-blue-600" />
                  </button>
                </div>
              </div>
            ))}

            {/* Load more indicator */}
            {loadingMore && (
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                <span className="ml-2 text-gray-600 text-sm">Loading more contacts...</span>
              </div>
            )}

            {/* End of list indicator */}
            {!hasMore && contacts.length > 0 && (
              <div className="text-center p-4 text-gray-500 text-sm">
                No more contacts to load
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action Sheet Modal removed - Edit functionality moved to contact list */}

      {/* Edit Contact Modal */}
      {showEditModal && editingContact && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white w-full max-w-md rounded-xl p-6 mobile-bounce-in">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Contact</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Contact name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={editForm.notes}
                  onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add notes about this contact"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <input
                  type="text"
                  value={editForm.tags}
                  onChange={(e) => setEditForm(prev => ({ ...prev, tags: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="VIP, Regular, New (comma separated)"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={saveContactChanges}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
