-- =====================================================
-- SUPABASE MIGRATION: Enhanced AI Analysis Fields
-- =====================================================
-- Copy and paste these commands into your Supabase SQL Editor
-- Execute them one by one or all at once

-- 1. Add enhanced AI analysis fields to contacts table
ALTER TABLE contacts
ADD COLUMN IF NOT EXISTS ai_urgency_level TEXT,
ADD COLUMN IF NOT EXISTS ai_journey_stage TEXT,
ADD COLUMN IF NOT EXISTS ai_sentiment_score DECIMAL(4,3),
ADD COLUMN IF NOT EXISTS ai_optimal_follow_up_hours INTEGER,
ADD COLUMN IF NOT EXISTS ai_confidence_score DECIMAL(4,3);

-- 2. Update follow_up_schedules table to allow more follow-up types
ALTER TABLE follow_up_schedules
DROP CONSTRAINT IF EXISTS follow_up_schedules_follow_up_type_check;

ALTER TABLE follow_up_schedules
ADD CONSTRAINT follow_up_schedules_follow_up_type_check
CHECK (follow_up_type IN (
  'booking_reminder',
  'order_follow_up',
  'engagement_check',
  'price_follow_up',
  'support_follow_up',
  'purchase_assistance'
));

-- 2. Add comments for documentation
COMMENT ON COLUMN contacts.ai_urgency_level IS 'AI-determined urgency level (immediate, high, medium, low, none)';
COMMENT ON COLUMN contacts.ai_journey_stage IS 'AI-determined customer journey stage (awareness, consideration, decision, purchase, post_purchase, retention)';
COMMENT ON COLUMN contacts.ai_sentiment_score IS 'AI-calculated sentiment score (-1.000 to 1.000)';
COMMENT ON COLUMN contacts.ai_optimal_follow_up_hours IS 'AI-recommended follow-up timing in hours';
COMMENT ON COLUMN contacts.ai_confidence_score IS 'AI confidence in analysis (0.000 to 1.000)';

-- 3. Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_contacts_ai_urgency_level ON contacts(ai_urgency_level);
CREATE INDEX IF NOT EXISTS idx_contacts_ai_journey_stage ON contacts(ai_journey_stage);
CREATE INDEX IF NOT EXISTS idx_contacts_ai_sentiment_score ON contacts(ai_sentiment_score);
CREATE INDEX IF NOT EXISTS idx_contacts_ai_confidence_score ON contacts(ai_confidence_score);

-- 4. Template performance tracking table
CREATE TABLE IF NOT EXISTS template_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    template_id TEXT NOT NULL,
    variant TEXT NOT NULL,
    intent_category TEXT NOT NULL,
    outcome TEXT NOT NULL, -- 'sent', 'responded', 'converted'
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Template settings table
CREATE TABLE IF NOT EXISTS template_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ab_testing_enabled BOOLEAN DEFAULT true,
    preferred_variant TEXT,
    custom_templates JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(auth_id)
);

-- 6. Follow-up analytics table
CREATE TABLE IF NOT EXISTS follow_up_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    follow_up_id UUID REFERENCES follow_up_schedules(id) ON DELETE CASCADE,
    outcome TEXT NOT NULL, -- 'sent', 'responded', 'converted', 'failed'
    response_time_hours DECIMAL(8,2),
    conversion_value DECIMAL(10,2),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Indexes for performance
CREATE INDEX IF NOT EXISTS idx_template_performance_auth_id ON template_performance(auth_id);
CREATE INDEX IF NOT EXISTS idx_template_performance_variant ON template_performance(auth_id, variant);
CREATE INDEX IF NOT EXISTS idx_template_performance_intent ON template_performance(auth_id, intent_category);
CREATE INDEX IF NOT EXISTS idx_template_performance_outcome ON template_performance(auth_id, outcome);
CREATE INDEX IF NOT EXISTS idx_template_performance_created_at ON template_performance(created_at);

CREATE INDEX IF NOT EXISTS idx_follow_up_analytics_auth_id ON follow_up_analytics(auth_id);
CREATE INDEX IF NOT EXISTS idx_follow_up_analytics_outcome ON follow_up_analytics(auth_id, outcome);
CREATE INDEX IF NOT EXISTS idx_follow_up_analytics_created_at ON follow_up_analytics(created_at);

-- 8. Function to track template performance
CREATE OR REPLACE FUNCTION track_template_performance(
    p_template_id TEXT,
    p_auth_id UUID,
    p_outcome TEXT
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO template_performance (template_id, auth_id, outcome)
    VALUES (p_template_id, p_auth_id, p_outcome);
END;
$$;

-- 9. Function to track follow-up outcome
CREATE OR REPLACE FUNCTION track_follow_up_outcome(
    p_follow_up_id UUID,
    p_auth_id UUID,
    p_outcome TEXT,
    p_metadata JSONB DEFAULT '{}',
    p_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO follow_up_analytics (follow_up_id, auth_id, outcome, metadata, created_at)
    VALUES (p_follow_up_id, p_auth_id, p_outcome, p_metadata, p_timestamp);
END;
$$;

-- 10. Function to get follow-up overview metrics
CREATE OR REPLACE FUNCTION get_follow_up_overview_metrics(
    p_auth_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE,
    p_end_date TIMESTAMP WITH TIME ZONE
)
RETURNS TABLE (
    total_scheduled BIGINT,
    total_sent BIGINT,
    total_responded BIGINT,
    total_converted BIGINT,
    response_rate DECIMAL(5,4),
    conversion_rate DECIMAL(5,4),
    avg_response_time DECIMAL(8,2)
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE fs.status IN ('pending', 'executed', 'failed')) as total_scheduled,
        COUNT(*) FILTER (WHERE fs.status = 'executed') as total_sent,
        COUNT(*) FILTER (WHERE fa.outcome = 'responded') as total_responded,
        COUNT(*) FILTER (WHERE fa.outcome = 'converted') as total_converted,
        CASE 
            WHEN COUNT(*) FILTER (WHERE fs.status = 'executed') > 0 
            THEN COUNT(*) FILTER (WHERE fa.outcome = 'responded')::DECIMAL / COUNT(*) FILTER (WHERE fs.status = 'executed')
            ELSE 0
        END as response_rate,
        CASE 
            WHEN COUNT(*) FILTER (WHERE fa.outcome = 'responded') > 0 
            THEN COUNT(*) FILTER (WHERE fa.outcome = 'converted')::DECIMAL / COUNT(*) FILTER (WHERE fa.outcome = 'responded')
            ELSE 0
        END as conversion_rate,
        AVG(fa.response_time_hours) FILTER (WHERE fa.response_time_hours IS NOT NULL) as avg_response_time
    FROM follow_up_schedules fs
    LEFT JOIN follow_up_analytics fa ON fs.id = fa.follow_up_id
    WHERE fs.auth_id = p_auth_id
    AND fs.created_at BETWEEN p_start_date AND p_end_date;
END;
$$;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these to verify the migration was successful

-- Check if new columns exist
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'contacts' 
AND column_name LIKE 'ai_%'
ORDER BY column_name;

-- Check if new tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('template_performance', 'template_settings', 'follow_up_analytics')
AND table_schema = 'public';

-- Test the new columns (should return without error)
SELECT id, ai_urgency_level, ai_journey_stage, ai_sentiment_score, ai_optimal_follow_up_hours, ai_confidence_score
FROM contacts 
LIMIT 1;
