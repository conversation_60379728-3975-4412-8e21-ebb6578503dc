// Test script for AI-powered image and message improvements
import { OpenAI } from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Test the AI image decision logic
async function testImageDecision(customerMessage, sectionsWithImages) {
  try {
    console.log(`\n🧪 Testing image decision for: "${customerMessage}"`);
    
    // Don't send images for very basic greetings or simple questions
    const basicGreetings = /^(hi|hello|hey|good morning|good afternoon|good evening|thanks|thank you|ok|okay|yes|no|ya|不是|是|好|谢谢|你好|嗨)$/i;
    if (basicGreetings.test(customerMessage.trim())) {
      console.log(`❌ Basic greeting detected - no images`);
      return false;
    }

    // Check if customer is explicitly asking for visual content
    const visualKeywords = /\b(show|see|picture|image|photo|look|view|display|visual|pic|img|pics|images|photos|看|显示|图片|照片|相片|gambar|foto|tengok|lihat|papar)\b/i;
    if (visualKeywords.test(customerMessage)) {
      console.log(`✅ Visual keywords detected - send images`);
      return true;
    }

    // Use AI to make intelligent decision
    const imageDecisionResponse = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `Determine if sending images would be helpful and appropriate for the customer's message.

SEND IMAGES (reply "YES") when:
- Customer asks about products, services, or specific items
- Customer wants recommendations or suggestions
- Customer asks "what do you have" or similar
- Customer is inquiring about visual aspects
- Customer seems to be browsing or exploring options

DON'T SEND IMAGES (reply "NO") when:
- Customer is just greeting or being polite
- Customer is asking about prices, delivery, or contact info only
- Customer is placing a specific order (already knows what they want)
- Customer is asking about policies, hours, or general info
- Customer message is very short or unclear
- Customer is asking about order status or tracking

Reply only "YES" or "NO".`
        },
        {
          role: "user",
          content: `Customer message: "${customerMessage}"
Available images: ${sectionsWithImages.map(s => s.title).join(', ')}
Should I send images?`
        }
      ],
      max_tokens: 10,
      temperature: 0.1,
    });

    const decision = imageDecisionResponse.choices[0].message.content.trim().toUpperCase();
    console.log(`🤖 AI decision: ${decision}`);
    
    return decision === 'YES';
    
  } catch (error) {
    console.error("Error in AI image decision:", error);
    return false;
  }
}

// Test the message splitting logic
async function testMessageSplitting(message) {
  try {
    console.log(`\n🧪 Testing message splitting for: "${message.substring(0, 50)}..."`);
    
    // Check if should split
    const shouldSplit = message.length > 100 && !message.includes('|||') && 
      (/(\n-|\n\*|\n\d+\.|\n•)/.test(message) || 
       message.split('\n\n').length > 2 || 
       /\b(also|additionally|furthermore|moreover|another|next|finally)\b/i.test(message) ||
       message.length > 400);
    
    if (!shouldSplit) {
      console.log(`❌ No splitting needed`);
      return [message];
    }

    console.log(`✅ Should split - using AI`);

    const splitResponse = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `Split the message into logical parts for better WhatsApp delivery. Rules:

1. Each part should be complete and make sense on its own
2. Split at natural breakpoints (paragraphs, lists, topics)
3. Keep related information together
4. Maximum 3-4 parts to avoid overwhelming
5. Maintain the original tone and emojis
6. Return as JSON array of strings

Example:
Input: "Hi! We have 3 promotions today. First is 20% off all items. Second is buy 2 get 1 free. Third is free delivery over RM50. Which one interests you?"

Output: ["Hi! We have 3 promotions today 🎉", "First: 20% off all items ✨", "Second: Buy 2 get 1 free 🎁", "Third: Free delivery over RM50 🚚 Which one interests you?"]`
        },
        {
          role: "user",
          content: `Split this message: "${message}"`
        }
      ],
      max_tokens: 500,
      temperature: 0.1,
    });

    const splitResult = splitResponse.choices[0].message.content.trim();
    
    try {
      const parsedSplit = JSON.parse(splitResult);
      
      if (Array.isArray(parsedSplit) && parsedSplit.length > 1 && parsedSplit.length <= 4) {
        console.log(`🤖 AI split into ${parsedSplit.length} parts:`);
        parsedSplit.forEach((part, i) => console.log(`   ${i + 1}. "${part}"`));
        return parsedSplit;
      }
    } catch (parseError) {
      console.log(`❌ AI split failed, using fallback`);
    }

    // Fallback
    let parts = message.split('\n\n').filter(part => part.trim().length > 0);
    if (parts.length === 1 && message.length > 300) {
      const sentences = message.split(/[.!?]+/).filter(s => s.trim().length > 0);
      if (sentences.length > 2) {
        const midPoint = Math.ceil(sentences.length / 2);
        parts = [
          sentences.slice(0, midPoint).join('. ') + '.',
          sentences.slice(midPoint).join('. ') + '.'
        ];
      }
    }
    
    console.log(`📝 Fallback split into ${parts.length} parts`);
    return parts.length > 1 ? parts : [message];
    
  } catch (error) {
    console.error("Error in message splitting test:", error);
    return [message];
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Testing AI-powered chatbot improvements\n');

  // Test image decisions
  const mockSections = [
    { title: 'Promotion Banner', image_url: 'test.jpg' },
    { title: 'Product Catalog', image_url: 'catalog.jpg' }
  ];

  await testImageDecision('hi', mockSections);
  await testImageDecision('can i order', mockSections);
  await testImageDecision('show me your products', mockSections);
  await testImageDecision('what promotions do you have', mockSections);
  await testImageDecision('what time do you close', mockSections);

  // Test message splitting
  await testMessageSplitting('Hi there!');
  await testMessageSplitting('We have amazing promotions today! First, get 20% off all electronics. Second, buy 2 get 1 free on fashion items. Third, free delivery for orders above RM100. Additionally, we have a special weekend deal. Which promotion interests you the most?');
  await testMessageSplitting('Our menu includes:\n- Nasi Lemak RM8\n- Mee Goreng RM7\n- Roti Canai RM3\n- Teh Tarik RM2.50\n\nAll items are freshly prepared daily. We also offer catering services for events.');

  console.log('\n✅ Tests completed!');
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { testImageDecision, testMessageSplitting };
