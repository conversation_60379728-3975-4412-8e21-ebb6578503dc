/**
 * Utility functions for date formatting
 */

export const formatDate = (dateString, locale = "en-MY", options = {}) => {
  if (!dateString) return "N/A";

  const defaultOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    ...options,
  };

  return new Date(dateString).toLocaleDateString(locale, defaultOptions);
};

export const formatDateWithTime = (dateString, locale = "en-MY") => {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  const now = new Date();
  const timeDiff = now - date;
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

  if (daysDiff === 0) {
    return date.toLocaleTimeString(locale, {
      hour: "2-digit",
      minute: "2-digit",
    });
  } else if (daysDiff < 7) {
    return date.toLocaleDateString(locale, {
      weekday: "short",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  return date.toLocaleDateString(locale, {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export const formatAnalyticsDate = (dateString) => {
  return formatDate(dateString, "en-US", {
    month: "short",
    day: "numeric",
  });
};

export const formatSubscriptionDate = (dateString) => {
  return formatDate(dateString, "en-MY");
};
