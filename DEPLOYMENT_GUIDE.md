# 🚀 Social Media & Google Integrations Deployment Guide

This guide will help you deploy the new social media integrations (Messenger, Instagram) and Google services integration features.

## 📋 Prerequisites

- Node.js and npm installed
- Supabase database access
- Meta Developer Account (for Messenger/Instagram)
- Google Cloud Console access (for Google Sheets/Calendar)

## 🗄️ Database Setup

### 1. Run Database Migration

The new features require additional database tables. Run the SQL commands from `database_schema.sql`:

```sql
-- Create social_media_integrations table
-- Create google_integrations table
-- Add indexes and triggers
```

### 2. Migrate Existing WhatsApp Data

If you have existing WhatsApp customers, run the migration:

```bash
# Option 1: Via API endpoint (recommended)
curl -X POST http://localhost:3000/api/ai/migrate/whatsapp-to-social-media \
  -H "x-auth-id: your-admin-user-id"

# Option 2: Via script
cd "Chatbot API"
node scripts/migrate-whatsapp-to-social-media.js
```

## 🔧 Environment Variables

Add these to your `.env` file:

```env
# Existing variables...
VERIFY_TOKEN=your_webhook_verify_token

# Meta/Facebook App Settings (for OAuth integration)
META_APP_ID=your_meta_app_id
META_APP_SECRET=your_meta_app_secret
BASE_URL=https://your-domain.com

# Google Service Account (for Sheets/Calendar)
GOOGLE_SERVICE_ACCOUNT_JSON={"type":"service_account",...}
```

## 📱 Meta OAuth Setup

### Create Meta App
1. Go to [Meta for Developers](https://developers.facebook.com/)
2. Create a new app or select existing app
3. Add the following products:
   - **WhatsApp Business API** (for WhatsApp integration)
   - **Messenger** (for Messenger integration)
   - **Instagram Basic Display** (for Instagram integration)

### Configure OAuth Settings
1. In your Meta app, go to **App Settings > Basic**
2. Add your domain to **App Domains**
3. Set **Valid OAuth Redirect URIs**:
   ```
   https://your-domain.com/dashboard/oauth/callback
   ```

### Configure Webhooks
1. For each product (WhatsApp, Messenger, Instagram):
2. Set webhook URL: `https://your-domain.com/api/ai/{platform}/webhook`
3. Set verify token from your `.env` file
4. Subscribe to `messages` events

### App Review (Production)
- For production use, submit your app for review
- Request permissions for each platform you want to use
- Provide use case documentation

## 🔗 Google Services Setup

### Google Sheets
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a service account
3. Download the JSON key file
4. Add the JSON content to `GOOGLE_SERVICE_ACCOUNT_JSON` env variable
5. Share your spreadsheets with the service account email

### Google Calendar (Future Feature)
- Framework is ready for calendar integration
- Will be activated in future updates

## 🎯 Frontend Features

### New OAuth Features:
- **One-Click Connection**: Users can connect platforms with OAuth (no manual token entry)
- **Automatic Token Refresh**: Long-lived tokens are automatically refreshed
- **Secure Authentication**: OAuth flow handles all security aspects
- **Account Selection**: Users can choose which pages/accounts to connect

### New Pages Added:
- **Social Media Integrations** (`/dashboard/social-media`)
  - OAuth connection buttons for each platform
  - Toggle platforms on/off
  - Real-time connection status

- **Google Services** (`/dashboard/google`)
  - Configure Google Sheets integration
  - Set up spreadsheet columns
  - Manage Google Calendar (coming soon)

- **OAuth Callback** (`/dashboard/oauth/callback`)
  - Handles OAuth authorization responses
  - Provides user feedback during connection process

### Updated Pages:
- **WhatsApp Integration** (`/dashboard/whatsapp`)
  - Google Sheets configuration moved to Google Services page
  - Can now use OAuth or manual configuration
  - Streamlined WhatsApp-specific settings

## 🔄 API Endpoints

### Social Media Management
```
GET    /api/ai/social-media/integrations     # Get all integrations
GET    /api/ai/social-media/enabled          # Get enabled platforms
POST   /api/ai/social-media/bulk-toggle     # Toggle multiple platforms

# Platform-specific endpoints
POST   /api/ai/messenger/register           # Configure Messenger
POST   /api/ai/instagram/register           # Configure Instagram
POST   /api/ai/{platform}/toggle            # Toggle platform on/off
```

### Google Services
```
GET    /api/ai/google/integrations          # Get all Google integrations
POST   /api/ai/google/sheets               # Configure Google Sheets
POST   /api/ai/google/calendar             # Configure Google Calendar
POST   /api/ai/google/{service}/toggle     # Toggle service on/off
```

## 🧪 Testing

### 1. Test Backend
```bash
cd "Chatbot API"
npm start
# Server should start without errors
```

### 2. Test Frontend
```bash
cd "Chatbot Frontend"
npm run dev
# Check new pages load correctly
```

### 3. Test Integrations
1. Visit `/dashboard/social-media` - configure platforms
2. Visit `/dashboard/google` - set up Google Sheets
3. Test webhook endpoints with platform-specific tools

## 🔒 Security Notes

- Never expose access tokens in frontend
- Use environment variables for sensitive data
- Validate webhook signatures for production
- Implement rate limiting for API endpoints

## 🐛 Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure all utility functions are properly exported
2. **Database Errors**: Run migration scripts and check table creation
3. **Webhook Failures**: Verify URLs and verify tokens match
4. **Permission Errors**: Check Google service account permissions

### Debug Steps:
1. Check server logs for detailed error messages
2. Verify environment variables are loaded
3. Test API endpoints with curl/Postman
4. Check browser console for frontend errors

## 📞 Support

If you encounter issues:
1. Check the server logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure database migrations completed successfully
4. Test individual API endpoints to isolate issues

---

🎉 **Congratulations!** Your AI chatbot now supports multiple social media platforms and Google services integration!
