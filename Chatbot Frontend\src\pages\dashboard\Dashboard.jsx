import { useEffect, useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import {
  useNavigate,
  Routes,
  Route,
  Link,
  useLocation,
} from "react-router-dom";
import {
  LayoutDashboard,
  Bot,
  BookText,
  Package,
  Users,
  MessageCircle,
  MessageSquare,
  Link as LinkIcon,
  BarChart2,
  ShieldCheck,
  Clock,
  Menu,
} from "lucide-react";
import Icon from "../../components/ui/icon";

// Import mobile components
import MobileDashboard from "./MobileDashboard";

// Dashboard page components
import Overview from "./Overview";
import ChatbotConfig from "./ChatbotConfig";
import KnowledgeBase from "./KnowledgeBase";
import Contacts from "./Contacts";
import FollowUps from "./FollowUps";
import Analytics from "./Analytics";
import Products from "./Products";
import WhatsAppIntegration from "./WhatsAppIntegration";
import SocialMediaIntegrations from "./SocialMediaIntegrations";
import GoogleIntegrations from "./GoogleIntegrations";
import AdminDashboard from "./AdminDashboard";
import OAuthCallback from "../OAuthCallback";

// Access control components
import LockedDashboard from "../../components/LockedDashboard";
import { FullPageLoader } from "../../components/ui/loader";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";

/**
 * Main Dashboard Component
 * Provides navigation sidebar and routing for all dashboard features
 * Protected route - redirects to login if user is not authenticated
 * Includes access control for free/suspended users
 */
export default function Dashboard() {
  const { isAuthenticated, loading: authLoading, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [accessLoading, setAccessLoading] = useState(true);
  const showLoadingState = useDelayedLoading(authLoading || accessLoading, 200);

  // Mobile detection state
  const [isMobile, setIsMobile] = useState(false);
  // Sidebar collapse state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(window.innerWidth < 1024); // collapsed by default on <lg

  // Check user access permissions
  const checkUserAccess = () => {
    if (!user || !user.profile) {
      setAccessLoading(false);
      return;
    }

    setAccessLoading(false);
  };

  // Get user plan and suspension status
  const getUserPlan = () => user?.profile?.plan || "free";
  const isUserSuspended = () => user?.profile?.is_suspended === true;
  const isAdmin = () => {
    const plan = getUserPlan();
    return plan === "admin" || user?.profile?.role === "admin";
  };

  // Check if user has access to dashboard features
  const hasFullAccess = () => {
    const plan = getUserPlan();
    return !isUserSuspended() && (plan !== "free" || isAdmin());
  };

  // Dashboard navigation sections configuration
  const sidebarSections = [
    {
      id: "overview",
      label: "Overview",
      path: "/dashboard",
      icon: "LayoutDashboard",
      description: "Dashboard overview",
    },
    {
      id: "chatbot",
      label: "Chatbot Config",
      path: "/dashboard/chatbot",
      icon: "Bot",
      description: "AI configuration",
    },
    {
      id: "knowledge",
      label: "Knowledge Base",
      path: "/dashboard/knowledge",
      icon: "BookText",
      description: "Manage content",
    },
    {
      id: "products",
      label: "Products",
      path: "/dashboard/products",
      icon: "Package",
      description: "Product catalog",
    },
    {
      id: "contacts",
      label: "Contacts",
      path: "/dashboard/contacts",
      icon: "Users",
      description: "Customer management",
    },
    {
      id: "follow-ups",
      label: "Follow-ups",
      path: "/dashboard/follow-ups",
      icon: "Clock",
      description: "AI & follow-up settings",
    },
    {
      id: "whatsapp",
      label: "WhatsApp",
      path: "/dashboard/whatsapp",
      icon: "MessageCircle",
      description: "WhatsApp integration",
    },
    {
      id: "social-media",
      label: "Social Media",
      path: "/dashboard/social-media",
      icon: "MessageSquare",
      description: "Messenger & Instagram",
    },
    {
      id: "google",
      label: "Google Services",
      path: "/dashboard/google",
      icon: "LinkIcon",
      description: "Sheets & Calendar",
    },
    {
      id: "analytics",
      label: "Analytics",
      path: "/dashboard/analytics",
      icon: "BarChart2",
      description: "Reports & insights",
    },
  ];

  // Add admin section if user is admin
  if (isAdmin()) {
    sidebarSections.push({
      id: "admin",
      label: "Admin Panel",
      path: "/dashboard/admin",
      icon: "ShieldCheck",
      description: "System management",
    });
  }

  // Get plan display info
  const getPlanDisplayInfo = () => {
    const plan = getUserPlan();
    switch (plan) {
      case "free":
        return {
          name: "Kedai Free Plan",
          color: "bg-gray-100 text-gray-700",
          dotColor: "bg-gray-500",
        };
      case "kecil":
        return {
          name: "Kedai Kecil Plan",
          color: "bg-green-100 text-green-700",
          dotColor: "bg-green-500",
        };
      case "popular":
        return {
          name: "Kedai Popular Plan",
          color: "bg-blue-100 text-blue-700",
          dotColor: "bg-blue-500",
        };
      case "besar":
        return {
          name: "Kedai Besar Plan",
          color: "bg-purple-100 text-purple-700",
          dotColor: "bg-purple-500",
        };
      case "trial":
        return {
          name: "Kedai Trial Plan",
          color: "bg-yellow-100 text-yellow-700",
          dotColor: "bg-yellow-500",
        };
      case "admin":
        return {
          name: "Kedai Admin Plan",
          color: "bg-red-100 text-red-700",
          dotColor: "bg-red-500",
        };
      default:
        return {
          name: "Kedai Free Plan",
          color: "bg-gray-100 text-gray-700",
          dotColor: "bg-gray-500",
        };
    }
  };

  // Redirect to login if user is not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, authLoading, navigate]);

  // Check user access when user data is available
  useEffect(() => {
    if (user && !authLoading) {
      checkUserAccess();
    }
  }, [user, authLoading]);

  // Collapse sidebar automatically on resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      setSidebarCollapsed(window.innerWidth < 1024); // auto-collapse on <lg
    };
    window.addEventListener('resize', checkMobile);
    checkMobile();
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Show loading while checking access
  if (showLoadingState) {
    return <FullPageLoader text="Loading dashboard..." />;
  }

  // Show locked dashboard for free/suspended users
  if (!hasFullAccess()) {
    const reason = isUserSuspended() ? "suspended" : "free";
    return <LockedDashboard reason={reason} user={user} />;
  }

  // Render OAuthCallback directly for its route, regardless of device
  if (location.pathname === '/dashboard/oauth/callback') {
    return <OAuthCallback />;
  }

  const planInfo = getPlanDisplayInfo();

  // Render mobile dashboard for mobile devices
  if (isMobile) {
    // Only use MobileDashboard for overview, contacts, and chat subroutes
    const mobilePaths = [
      '/dashboard',
      '/dashboard/',
      '/dashboard/overview',
      '/dashboard/contacts',
      '/dashboard/contacts/chat',
    ];
    if (mobilePaths.some((p) => location.pathname.startsWith(p))) {
      return <MobileDashboard user={user} isAdmin={isAdmin} planInfo={planInfo} />;
    }
    // For all other dashboard subroutes (including /dashboard/oauth/callback), fall through to normal routing
  }

  // Render desktop dashboard for larger screens
  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Left Navigation Sidebar - Fixed */}
      <aside className={`fixed left-0 top-20 h-[calc(100vh-80px)] bg-white shadow-2xl border-r border-gray-100 overflow-y-auto z-10 transition-all duration-300
        ${sidebarCollapsed ? 'w-16' : 'w-72'}`}
      >
        <div className="relative p-6 h-full flex flex-col">
          {/* Toggle button */}
          <button
            className="absolute top-2 right-2 p-2 rounded-md hover:bg-gray-100 focus:outline-none lg:block"
            onClick={() => setSidebarCollapsed((c) => !c)}
            aria-label="Toggle sidebar"
          >
            <Menu className="w-6 h-6 text-gray-500" />
          </button>
          {/* Header */}
          {!sidebarCollapsed && (
            <div className="mb-8 max-w-[230px]">
              <h1 className="text-2xl font-bold text-gray-900 mb-2 whitespace-nowrap overflow-hidden text-ellipsis block">
                Dashboard
              </h1>
              <p className="text-sm text-gray-500 whitespace-nowrap overflow-hidden text-ellipsis block">
                Manage your chatbot experience
              </p>
            </div>
          )}
          {/* User Plan Badge */}
          {!sidebarCollapsed && (
            <div className="mb-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 max-w-[230px]">
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-3 h-3 rounded-full ${planInfo.dotColor}`}></div>
                <span className="font-semibold text-gray-900 whitespace-nowrap overflow-hidden text-ellipsis block">{planInfo.name}</span>
              </div>
              <div className="text-xs text-gray-600">
                <p className="whitespace-nowrap overflow-hidden text-ellipsis block">
                  Welcome back, {user?.username || user?.profile?.display_name || "User"}!
                </p>
              </div>
            </div>
          )}
          {/* Navigation Links */}
          <nav className={`flex flex-col items-center justify-center ${sidebarCollapsed ? "gap-4 flex-1" : ""}`}>
            {!sidebarCollapsed && (
              <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3 py-2">
                Main Menu
              </div>
            )}
            {sidebarSections.map((section) => {
              const isActive = location.pathname === section.path;
              return (
                <Link
                  key={section.id}
                  to={section.path}
                  className={`flex items-center justify-center ${sidebarCollapsed ? 'w-10 h-10' : 'w-full gap-3 px-3 py-3'} rounded-xl transition-all duration-200
                    ${isActive ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-400 hover:bg-gray-50 hover:text-gray-900'}`}
                >
                  {/* Navigation icon */}
                  <div
                    className={`transition-colors ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5'} ${
                      isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'
                    }`}
                  >
                    <Icon name={section.icon} className={`${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} />
                  </div>
                  {/* Only show label/desc if not collapsed */}
                  {(!sidebarCollapsed) && (
                    <>
                      <div className="flex-1 min-w-0 max-w-[140px]">
                        <span className="font-medium text-sm whitespace-nowrap overflow-hidden text-ellipsis block">
                          {section.label}
                        </span>
                        <p
                          className={`text-xs mt-0.5 transition-colors whitespace-nowrap overflow-hidden text-ellipsis block ${
                            isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-600'
                          }`}
                        >
                          {section.description}
                        </p>
                      </div>
                      {isActive && (
                        <div className="w-1 h-6 bg-blue-600 rounded-full"></div>
                      )}
                    </>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>
      </aside>
      {/* Main Content Area - With left margin to account for fixed sidebar */}
      <main className={`${sidebarCollapsed ? 'ml-16' : 'ml-72'} transition-all duration-300`}>
        <div className="p-8">
          <Routes>
            {/* Dashboard Routes */}
            <Route path="/" element={<Overview />} />
            <Route path="/chatbot" element={<ChatbotConfig />} />
            <Route path="/knowledge" element={<KnowledgeBase />} />
            <Route path="/products" element={<Products />} />
            <Route path="/contacts" element={<Contacts />} />
            <Route path="/follow-ups" element={<FollowUps />} />
            <Route path="/whatsapp" element={<WhatsAppIntegration />} />
            <Route path="/social-media" element={<SocialMediaIntegrations />} />
            <Route path="/google" element={<GoogleIntegrations />} />
            <Route path="/oauth/callback" element={<OAuthCallback />} />
            <Route path="/analytics" element={<Analytics />} />
            {isAdmin() && (
              <Route path="/admin/*" element={<AdminDashboard />} />
            )}
          </Routes>
        </div>
      </main>
    </div>
  );
}