import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import aiService from "../../services/ai";
import {
  FileSpreadsheet,
  Calendar,
  Settings,
  CircleCheck,
  XCircle,
  X,
  AlertCircle,
  ExternalLink,
  Plus,
  Minus,
  Loader2,
  Link as LinkIcon
} from "lucide-react";

export default function GoogleIntegrations() {
  const { user } = useAuth();
  const { showSuccess, showError } = useAlertMigration();

  // Removed unused loading state
  const [saving, setSaving] = useState(false);
  
  // Google Sheets state
  const [sheetsIntegration, setSheetsIntegration] = useState(null);
  const [sheetsForm, setSheetsForm] = useState({
    spreadsheetUrl: "",
    worksheetName: "Data",
    spreadsheetColumns: [
      'OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'
    ],
  });
  const [showSheetsForm, setShowSheetsForm] = useState(false);
  const [customColumn, setCustomColumn] = useState("");

  // Google Calendar state (for future use)
  const [calendarIntegration, setCalendarIntegration] = useState(null);
  const [calendarForm, setCalendarForm] = useState({
    calendarId: "",
    calendarName: "",
  });
  const [showCalendarForm, setShowCalendarForm] = useState(false);

  useEffect(() => {
    if (user?.user?.id) {
      fetchIntegrations();
    }
  }, [user]);

  const fetchIntegrations = async () => {
    try {
      // setLoading(true); // Removed unused loading state
      
      // Fetch Google Sheets integration
      const sheetsResponse = await aiService.getGoogleIntegration(user.user.id, "sheets");
      if (sheetsResponse.success && sheetsResponse.data) {
        setSheetsIntegration(sheetsResponse.data);
        setSheetsForm({
          spreadsheetUrl: sheetsResponse.data.spreadsheet_url || "",
          worksheetName: sheetsResponse.data.worksheet_name || "Data",
          spreadsheetColumns: sheetsResponse.data.spreadsheet_columns || [
            'OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'
          ],
        });
      }

      // Fetch Google Calendar integration
      const calendarResponse = await aiService.getGoogleIntegration(user.user.id, "calendar");
      if (calendarResponse.success && calendarResponse.data) {
        setCalendarIntegration(calendarResponse.data);
        setCalendarForm({
          calendarId: calendarResponse.data.calendar_id || "",
          calendarName: calendarResponse.data.calendar_name || "",
        });
      }
    } catch (error) {
      console.error("Error fetching Google integrations:", error);
      showError("Failed to load Google integrations");
    } finally {
      // setLoading(false); // Removed unused loading state
    }
  };

  const handleSheetsSubmit = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      const response = await aiService.saveGoogleSheetsIntegration(user.user.id, sheetsForm);

      if (response.success) {
        showSuccess("Google Sheets integration saved successfully!");
        await fetchIntegrations();
        setShowSheetsForm(false);
      } else {
        showError(response.error || "Failed to save Google Sheets integration");
      }
    } catch (error) {
      console.error("Error saving Google Sheets integration:", error);
      console.log("Error response data:", error.response?.data);

      let errorMessage = "Failed to save Google Sheets integration";

      // Extract error details from the response
      if (error.response?.data) {
        const errorData = error.response.data;
        console.log("Error data:", errorData);

        errorMessage = errorData.error || errorData.details || errorMessage;

        // If there are available worksheets, show them
        if (errorData.availableWorksheets && errorData.availableWorksheets.length > 0) {
          errorMessage += `\n\nAvailable worksheets: ${errorData.availableWorksheets.join(', ')}`;

          if (errorData.suggestedWorksheet) {
            errorMessage += `\n\nTip: Try using "${errorData.suggestedWorksheet}" as the worksheet name.`;
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.log("Final error message:", errorMessage);
      showError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleCalendarSubmit = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      const response = await aiService.saveGoogleCalendarIntegration(user.user.id, calendarForm);

      if (response.success) {
        showSuccess("Google Calendar integration saved successfully!");
        await fetchIntegrations();
        setShowCalendarForm(false);
      } else {
        showError(response.error || "Failed to save Google Calendar integration");
      }
    } catch (error) {
      console.error("Error saving Google Calendar integration:", error);
      showError("Failed to save Google Calendar integration");
    } finally {
      setSaving(false);
    }
  };

  const handleToggleIntegration = async (serviceType, enabled) => {
    try {
      setSaving(true);
      const response = await aiService.toggleGoogleIntegration(user.user.id, serviceType, enabled);

      if (response.success) {
        if (serviceType === "sheets") {
          setSheetsIntegration(prev => ({ ...prev, is_enabled: enabled }));
        } else if (serviceType === "calendar") {
          setCalendarIntegration(prev => ({ ...prev, is_enabled: enabled }));
        }
        showSuccess(`Google ${serviceType.charAt(0).toUpperCase() + serviceType.slice(1)} ${enabled ? 'enabled' : 'disabled'} successfully`);
      } else {
        showError(response.error || `Failed to toggle Google ${serviceType}`);
      }
    } catch (error) {
      console.error(`Error toggling Google ${serviceType}:`, error);
      showError(`Failed to toggle Google ${serviceType}`);
    } finally {
      setSaving(false);
    }
  };

  const addColumn = () => {
    if (customColumn.trim() && !sheetsForm.spreadsheetColumns.includes(customColumn.trim())) {
      setSheetsForm(prev => ({
        ...prev,
        spreadsheetColumns: [...prev.spreadsheetColumns, customColumn.trim()]
      }));
      setCustomColumn("");
    }
  };

  const removeColumn = (index) => {
    setSheetsForm(prev => ({
      ...prev,
      spreadsheetColumns: prev.spreadsheetColumns.filter((_, i) => i !== index)
    }));
  };

  // --- MAIN LAYOUT ---
  return (
    <div className="h-[calc(90vh-6rem)] flex flex-col gap-6">
      {/* Header */}
      <div className="flex justify-between items-start flex-shrink-0">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Google Integrations</h2>
          <p className="text-gray-600">Connect your Google services for enhanced functionality</p>
        </div>
      </div>
      {/* Info Card */}
      <div className="card flex-shrink-0 bg-blue-50">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Google Services Integration</p>
            <p>Connect Google Sheets for order tracking and Google Calendar for appointment management. These integrations are separate from your social media platforms.</p>
          </div>
        </div>
      </div>
      {/* Service Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card flex-shrink-0 bg-white flex flex-col min-h-[220px]">
          {/* Google Sheets Card */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-3 rounded-lg bg-green-500">
                <FileSpreadsheet className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Google Sheets</h3>
                <p className="text-sm text-gray-600">Sync order data to spreadsheets</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {sheetsIntegration && sheetsIntegration.spreadsheet_id ? (
                <CircleCheck className="w-5 h-5 text-green-500" />
              ) : (
                <X className="w-5 h-5 text-red-500" />
              )}
              <button
                onClick={() => handleToggleIntegration("sheets", !sheetsIntegration?.is_enabled)}
                disabled={!sheetsIntegration?.spreadsheet_id || saving}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${sheetsIntegration?.is_enabled ? "bg-blue-600" : "bg-gray-200"} ${!sheetsIntegration?.spreadsheet_id ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${sheetsIntegration?.is_enabled ? "translate-x-6" : "translate-x-1"}`} />
              </button>
            </div>
          </div>
          <div className="space-y-2 flex-1 flex flex-col">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className={`font-medium ${sheetsIntegration?.spreadsheet_id ? (sheetsIntegration?.is_enabled ? "text-green-600" : "text-yellow-600") : "text-red-600"}`}>
                {sheetsIntegration?.spreadsheet_id ? (sheetsIntegration?.is_enabled ? "Active" : "Configured but Disabled") : "Not Configured"}
              </span>
            </div>
            {sheetsIntegration?.spreadsheet_url && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Spreadsheet:</span>
                <a
                  href={sheetsIntegration.spreadsheet_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                >
                  <span>Open</span>
                  <ExternalLink className="w-3 h-3" />
                </a>
              </div>
            )}
            <div className="mt-auto">
              <button
                onClick={() => setShowSheetsForm(true)}
                className={`w-full px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2 ${sheetsIntegration?.spreadsheet_id ? "bg-gray-100 text-gray-700 hover:bg-gray-200" : "bg-blue-600 text-white hover:bg-blue-700"}`}
              >
                <Settings className="w-4 h-4" />
                <span>{sheetsIntegration?.spreadsheet_id ? "Reconfigure" : "Configure"} Google Sheets</span>
              </button>
            </div>
          </div>
        </div>
        <div className="card flex-shrink-0 bg-white flex flex-col min-h-[220px]">
          {/* Google Calendar Card */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-3 rounded-lg bg-blue-500">
                <Calendar className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Google Calendar</h3>
                <p className="text-sm text-gray-600">Manage appointments and bookings</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {calendarIntegration && calendarIntegration.calendar_id ? (
                <CircleCheck className="w-5 h-5 text-green-500" />
              ) : (
                <X className="w-5 h-5 text-red-500" />
              )}
              <button
                onClick={() => handleToggleIntegration("calendar", !calendarIntegration?.is_enabled)}
                disabled={!calendarIntegration?.calendar_id || saving}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${calendarIntegration?.is_enabled ? "bg-blue-600" : "bg-gray-200"} ${!calendarIntegration?.calendar_id ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${calendarIntegration?.is_enabled ? "translate-x-6" : "translate-x-1"}`} />
              </button>
            </div>
          </div>
          <div className="space-y-2 flex-1 flex flex-col">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className={`font-medium ${calendarIntegration?.calendar_id ? (calendarIntegration?.is_enabled ? "text-green-600" : "text-yellow-600") : "text-red-600"}`}>
                {calendarIntegration?.calendar_id ? (calendarIntegration?.is_enabled ? "Active" : "Configured but Disabled") : "Not Configured"}
              </span>
            </div>
            <div className="mt-auto">
              <button
                onClick={() => setShowCalendarForm(true)}
                className={`w-full px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2 ${calendarIntegration?.calendar_id ? "bg-gray-100 text-gray-700 hover:bg-gray-200" : "bg-blue-600 text-white hover:bg-blue-700"}`}
              >
                <Settings className="w-4 h-4" />
                <span>{calendarIntegration?.calendar_id ? "Reconfigure" : "Configure"} Google Calendar</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* Google Sheets Modal */}
      {showSheetsForm && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity" onClick={() => setShowSheetsForm(false)} />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FileSpreadsheet className="w-5 h-5 text-green-600" />
                  Configure Google Sheets
                </h3>
                <button onClick={() => setShowSheetsForm(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="w-6 h-6" />
                </button>
              </div>
              <form onSubmit={handleSheetsSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Google Sheets URL or ID</label>
                  <input type="text" value={sheetsForm.spreadsheetUrl} onChange={e => setSheetsForm(prev => ({ ...prev, spreadsheetUrl: e.target.value }))} placeholder="https://docs.google.com/spreadsheets/d/your-sheet-id or just the sheet ID" className="input-field" required />
                  <p className="text-xs text-gray-500 mt-1">Paste the full Google Sheets URL or just the spreadsheet ID</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Worksheet Name</label>
                  <input type="text" value={sheetsForm.worksheetName} onChange={e => setSheetsForm(prev => ({ ...prev, worksheetName: e.target.value }))} placeholder="Data" className="input-field" required />
                  <p className="text-xs text-gray-500 mt-1">Name of the worksheet tab where data will be saved</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Spreadsheet Columns</label>
                  <div className="space-y-2">
                    {sheetsForm.spreadsheetColumns.map((column, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input type="text" value={column} onChange={e => { const newColumns = [...sheetsForm.spreadsheetColumns]; newColumns[index] = e.target.value; setSheetsForm(prev => ({ ...prev, spreadsheetColumns: newColumns })); }} className="flex-1 input-field" />
                        <button type="button" onClick={() => removeColumn(index)} className="p-2 text-red-600 hover:bg-red-50 rounded-lg"><Minus className="w-4 h-4" /></button>
                      </div>
                    ))}
                    <div className="flex items-center space-x-2">
                      <input type="text" value={customColumn} onChange={e => setCustomColumn(e.target.value)} placeholder="Add new column..." className="flex-1 input-field" onKeyPress={e => { if (e.key === 'Enter') { e.preventDefault(); addColumn(); } }} />
                      <button type="button" onClick={addColumn} className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"><Plus className="w-4 h-4" /></button>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Define the columns that will be used when saving order data to your spreadsheet</p>
                </div>
                <div className="flex gap-3 pt-4">
                  <button type="button" onClick={() => setShowSheetsForm(false)} className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors">Cancel</button>
                  <button type="submit" disabled={saving} className="flex-1 bg-green-600 text-white px-4 py-2.5 rounded-xl hover:bg-green-700 transition-colors disabled:opacity-60 flex items-center justify-center gap-2">{saving ? <Loader2 className="w-4 h-4 animate-spin" /> : "Save Configuration"}</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
      {/* Google Calendar Modal */}
      {showCalendarForm && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity" onClick={() => setShowCalendarForm(false)} />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-blue-600" />
                  Configure Google Calendar
                </h3>
                <button onClick={() => setShowCalendarForm(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="w-6 h-6" />
                </button>
              </div>
              <form onSubmit={handleCalendarSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Calendar ID</label>
                  <input type="text" value={calendarForm.calendarId} onChange={e => setCalendarForm(prev => ({ ...prev, calendarId: e.target.value }))} placeholder="<EMAIL>" className="input-field" required />
                  <p className="text-xs text-gray-500 mt-1">Your Google Calendar ID (usually your email address)</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Calendar Name (Optional)</label>
                  <input type="text" value={calendarForm.calendarName} onChange={e => setCalendarForm(prev => ({ ...prev, calendarName: e.target.value }))} placeholder="My Business Calendar" className="input-field" />
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800"><strong>Coming Soon:</strong> Google Calendar integration is being prepared for future appointment booking features.</p>
                </div>
                <div className="flex gap-3 pt-4">
                  <button type="button" onClick={() => setShowCalendarForm(false)} className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors">Cancel</button>
                  <button type="submit" disabled={saving} className="flex-1 bg-blue-600 text-white px-4 py-2.5 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-60 flex items-center justify-center gap-2">{saving ? <Loader2 className="w-4 h-4 animate-spin" /> : "Save Configuration"}</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
