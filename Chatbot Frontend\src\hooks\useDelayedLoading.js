import { useState, useEffect } from "react";

/**
 * Custom hook that delays showing loading state to prevent blinking on fast requests
 * @param {boolean} isLoading - The actual loading state
 * @param {number} delay - Delay in milliseconds before showing loading (default: 300ms)
 * @returns {boolean} - Whether to show loading state
 */
export const useDelayedLoading = (isLoading, delay = 300) => {
  const [showLoading, setShowLoading] = useState(false);

  useEffect(() => {
    let timer;

    if (isLoading) {
      // Only show loading after the delay
      timer = setTimeout(() => {
        setShowLoading(true);
      }, delay);
    } else {
      // Immediately hide loading when it's false
      setShowLoading(false);
      // Clear the timer if loading becomes false before delay
      if (timer) {
        clearTimeout(timer);
      }
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [isLoading, delay]);

  return showLoading;
};

export default useDelayedLoading;
