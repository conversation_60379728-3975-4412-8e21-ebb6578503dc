// WhatsApp utility functions extracted from routes/ai.js for better modularity

import axios from "axios";
import FormData from "form-data";
import logger from "./logger.js";

/**
 * Send a text message via WhatsApp Business API
 */
export async function sendWhatsAppTextMessage(to, message, phoneNumberId, accessToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;
    const data = {
      messaging_product: "whatsapp",
      recipient_type: "individual",
      to: to,
      type: "text",
      text: {
        preview_url: false,
        body: message,
      },
    };

    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error sending WhatsApp text message:", error);
    throw error;
  }
}

/**
 * Send an image message via WhatsApp Business API
 */
export async function sendWhatsAppImageMessage(to, imageSource, phoneNumberId, accessToken, caption = '') {
  try {
    const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;
    
    // Determine if imageSource is a URL or media ID
    let imageData;
    if (imageSource.startsWith('http')) {
      // URL-based image
      try {
        new URL(imageSource); // Validate URL
        logger.info(`Image URL validation passed: ${imageSource}`);
        imageData = {
          link: imageSource,
          caption: caption || ''
        };
      } catch (urlError) {
        logger.error(`Image URL validation failed:`, urlError);
        throw new Error(`Invalid image URL: ${imageSource}`);
      }
    } else {
      // Media ID-based image
      if (!imageSource || imageSource.length < 10) {
        throw new Error(`Invalid WhatsApp media ID: ${imageSource}`);
      }
      
      // Verify media ID format (basic validation)
      if (!/^[a-zA-Z0-9_-]+$/.test(imageSource)) {
        throw new Error(`Invalid media ID format: ${imageSource}`);
      }
      
      logger.info(`Media ID verification passed: ${imageSource}`);
      logger.info(`Using WhatsApp media ID: ${imageSource}`);
      
      imageData = {
        id: imageSource,
        caption: caption || ''
      };
    }

    const data = {
      messaging_product: "whatsapp",
      recipient_type: "individual",
      to: to,
      type: "image",
      image: imageData,
    };

    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error sending WhatsApp image message:", error);
    throw error;
  }
}

/**
 * Send a video message via WhatsApp Business API
 */
export async function sendWhatsAppVideoMessage(to, videoSource, phoneNumberId, accessToken, caption = '') {
  try {
    const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;
    
    // Determine if videoSource is a URL or media ID
    let videoData;
    if (videoSource.startsWith('http')) {
      // URL-based video
      try {
        new URL(videoSource); // Validate URL
        logger.info(`Video URL validation passed: ${videoSource}`);
        videoData = {
          link: videoSource,
          caption: caption || ''
        };
      } catch (urlError) {
        logger.error(`Video URL validation failed:`, urlError);
        throw new Error(`Invalid video URL: ${videoSource}`);
      }
    } else {
      // Media ID-based video
      logger.info(`Using WhatsApp media ID: ${videoSource}`);
      videoData = {
        id: videoSource,
        caption: caption || ''
      };
    }

    const data = {
      messaging_product: "whatsapp",
      recipient_type: "individual",
      to: to,
      type: "video",
      video: videoData,
    };

    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error sending WhatsApp video message:", error);
    throw error;
  }
}

/**
 * Send multiple WhatsApp messages (split by ||| or intelligently)
 */
export async function sendMultipleWhatsAppMessages(to, message, phoneNumberId) {
  try {
    // Check if message contains multiple parts separated by |||
    if (message.includes('|||')) {
      const messageParts = message.split('|||').map(part => part.trim()).filter(part => part.length > 0);

      logger.info(`Sending ${messageParts.length} separate WhatsApp messages | To: ${to}`);

      const results = [];

      // Send each message part with a small delay
      for (let i = 0; i < messageParts.length; i++) {
        const part = messageParts[i];

        try {
          const result = await sendWhatsAppMessage(to, part, phoneNumberId);
          results.push(result);

          // Add small delay between messages (except for the last one)
          if (i < messageParts.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1200)); // 1.2 second delay for better UX
          }
        } catch (error) {
          logger.error(`Failed to send message part ${i + 1}:`, error);
          // Continue with other parts even if one fails
          results.push({ error: error.message, part: part });
        }
      }

      logger.info(`Sent ${messageParts.length} messages successfully | To: ${to}`);
      return { multipleMessages: true, results: results, count: messageParts.length };
    } else {
      // Check if message should be intelligently split
      const shouldSplit = await shouldSplitMessage(message);

      if (shouldSplit) {
        const splitMessages = await intelligentMessageSplit(message);

        if (splitMessages.length > 1) {
          logger.info(`AI-powered message splitting: ${splitMessages.length} parts | To: ${to}`);

          const results = [];

          // Send each split message with a delay
          for (let i = 0; i < splitMessages.length; i++) {
            const part = splitMessages[i];

            try {
              const result = await sendWhatsAppMessage(to, part, phoneNumberId);
              results.push(result);

              // Add delay between messages (except for the last one)
              if (i < splitMessages.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1500)); // 1.5 second delay for AI splits
              }
            } catch (error) {
              logger.error(`Failed to send AI-split message part ${i + 1}:`, error);
              results.push({ error: error.message, part: part });
            }
          }

          return { multipleMessages: true, results: results, count: splitMessages.length, aiSplit: true };
        }
      }

      // Single message - use normal sending
      return await sendWhatsAppMessage(to, message, phoneNumberId);
    }
  } catch (error) {
    logger.error("Error sending multiple WhatsApp messages:", error);
    throw error;
  }
}

/**
 * Basic WhatsApp message sender (used internally)
 */
export async function sendWhatsAppMessage(to, message, phoneNumberId) {
  // This would be implemented to get the access token and call sendWhatsAppTextMessage
  // For now, this is a placeholder that would need to be connected to the database
  throw new Error("sendWhatsAppMessage requires database access - use from main routes");
}

/**
 * Download media from WhatsApp
 */
export async function downloadWhatsAppMedia(mediaId, accessToken) {
  try {
    logger.debug(`Downloading WhatsApp media | ID: ${mediaId}`);
    
    // First, get the media URL and info
    const mediaInfoResponse = await axios.get(
      `https://graph.facebook.com/v18.0/${mediaId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        timeout: 10000,
      },
    );

    const mediaUrl = mediaInfoResponse.data.url;
    const mimeType = mediaInfoResponse.data.mime_type;
    const fileSize = mediaInfoResponse.data.file_size;

    logger.debug(`Media info retrieved | Type: ${mimeType} | Size: ${fileSize} bytes`);

    // Download the actual media file
    const mediaResponse = await axios.get(mediaUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Accept': '*/*',
        'User-Agent': 'WhatsApp/2.24.1.84'
      },
      responseType: 'arraybuffer',
      timeout: 30000, // Longer timeout for media download
      maxContentLength: 16 * 1024 * 1024, // 16MB limit
    });

    const downloadedSize = mediaResponse.data.length;
    if (downloadedSize !== fileSize) {
      logger.warn(`Downloaded size (${downloadedSize} bytes) differs from reported size (${fileSize} bytes)`);
    }

    logger.debug(`Media downloaded successfully | Size: ${downloadedSize} bytes`);

    return {
      buffer: Buffer.from(mediaResponse.data),
      mimeType: mimeType,
      fileSize: downloadedSize,
    };
  } catch (error) {
    logger.error("Error downloading WhatsApp media:", error);
    throw error;
  }
}

/**
 * Upload media to WhatsApp servers
 */
export async function uploadMediaToWhatsApp(buffer, mimeType, phoneNumberId, accessToken) {
  try {
    logger.debug(`Uploading media to WhatsApp | PhoneID: ${phoneNumberId} | Type: ${mimeType}`);

    // Validate buffer
    if (!buffer || buffer.length === 0) {
      throw new Error('Empty media buffer received');
    }

    // Validate file size (WhatsApp limit is 16MB)
    if (buffer.length > 16 * 1024 * 1024) {
      throw new Error('File size exceeds WhatsApp 16MB limit');
    }

    // Validate minimum file size (1KB)
    if (buffer.length < 1024) {
      throw new Error('File size too small, minimum 1KB required');
    }

    // For WebP images, suggest converting to JPEG/PNG
    if (mimeType === 'image/webp') {
      logger.warn('Warning: WebP format is not recommended. Consider converting to JPEG or PNG for better compatibility.');
    }

    // Create form data for upload
    const formData = new FormData();
    formData.append('messaging_product', 'whatsapp');
    formData.append('file', buffer, { contentType: mimeType });

    // Add explicit headers for better compatibility
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/json',
      ...formData.getHeaders(),
    };

    const response = await axios.post(
      `https://graph.facebook.com/v18.0/${phoneNumberId}/media`,
      formData,
      {
        headers,
        maxContentLength: 16 * 1024 * 1024, // 16MB limit
        timeout: 30000, // 30 second timeout
      }
    );

    if (!response.data || !response.data.id) {
      throw new Error('Invalid response from WhatsApp API: Missing media ID');
    }

    logger.info(`Media uploaded to WhatsApp successfully | Media ID: ${response.data.id}`);
    return response.data.id;

  } catch (error) {
    logger.error("Error uploading media to WhatsApp:", error);
    throw error;
  }
}

// AI-powered function to determine if a message should be split
async function shouldSplitMessage(message) {
  try {
    // Don't split very short messages
    if (message.length < 100) {
      return false;
    }

    // Don't split if already contains manual splits
    if (message.includes('|||')) {
      return false;
    }

    // Check for natural splitting indicators
    const hasList = /(\n-|\n\*|\n\d+\.|\n•)/.test(message);
    const hasMultipleParagraphs = message.split('\n\n').length > 2;
    const hasMultipleTopics = /\b(also|additionally|furthermore|moreover|another|next|finally)\b/i.test(message);
    const isLong = message.length > 200;

    // Split if message has clear structure or is very long
    return hasList || hasMultipleParagraphs || (hasMultipleTopics && isLong) || message.length > 400;

  } catch (error) {
    logger.error("Error determining if message should be split:", error);
    return false;
  }
}

// AI-powered intelligent message splitting
async function intelligentMessageSplit(message) {
  try {
    // Import OpenAI here to avoid circular dependencies
    const { OpenAI } = await import('openai');
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const splitResponse = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `Split the message into logical parts for better WhatsApp delivery. Rules:

1. Each part should be complete and make sense on its own
2. Split at natural breakpoints (paragraphs, lists, topics)
3. Keep related information together
4. Maximum 3-4 parts to avoid overwhelming
5. Maintain the original tone and emojis
6. Return as JSON array of strings

Example:
Input: "Hi! We have 3 promotions today. First is 20% off all items. Second is buy 2 get 1 free. Third is free delivery over RM50. Which one interests you?"

Output: ["Hi! We have 3 promotions today 🎉", "First: 20% off all items ✨", "Second: Buy 2 get 1 free 🎁", "Third: Free delivery over RM50 🚚 Which one interests you?"]`
        },
        {
          role: "user",
          content: `Split this message: "${message}"`
        }
      ],
      max_tokens: 500,
      temperature: 0.1,
    });

    const splitResult = splitResponse.choices[0].message.content.trim();

    try {
      const parsedSplit = JSON.parse(splitResult);

      // Validate the result
      if (Array.isArray(parsedSplit) && parsedSplit.length > 1 && parsedSplit.length <= 4) {
        logger.info(`AI successfully split message into ${parsedSplit.length} parts`);
        return parsedSplit;
      }
    } catch (parseError) {
      logger.warn("AI split result was not valid JSON, using fallback");
    }

    // Fallback: simple paragraph splitting
    return fallbackMessageSplit(message);

  } catch (error) {
    logger.error("Error in AI message splitting:", error);
    return fallbackMessageSplit(message);
  }
}

// Fallback message splitting logic
function fallbackMessageSplit(message) {
  try {
    // Split by double newlines (paragraphs)
    let parts = message.split('\n\n').filter(part => part.trim().length > 0);

    // If no paragraphs, try splitting by sentences for very long messages
    if (parts.length === 1 && message.length > 300) {
      const sentences = message.split(/[.!?]+/).filter(s => s.trim().length > 0);
      if (sentences.length > 2) {
        const midPoint = Math.ceil(sentences.length / 2);
        parts = [
          sentences.slice(0, midPoint).join('. ') + '.',
          sentences.slice(midPoint).join('. ') + '.'
        ];
      }
    }

    // Limit to maximum 3 parts
    if (parts.length > 3) {
      parts = [
        parts[0],
        parts.slice(1, -1).join('\n\n'),
        parts[parts.length - 1]
      ];
    }

    return parts.length > 1 ? parts : [message];

  } catch (error) {
    logger.error("Error in fallback message splitting:", error);
    return [message];
  }
}

/**
 * Validate WhatsApp Business API credentials
 */
export async function validateWhatsAppCredentials(businessAccountId, phoneNumberId, accessToken) {
  try {
    // Test the credentials by making a simple API call
    const url = `https://graph.facebook.com/v18.0/${phoneNumberId}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: "id,display_phone_number,verified_name",
      },
      timeout: 30000,
    });

    return {
      isValid: true,
      phoneInfo: response.data,
    };
  } catch (error) {
    logger.error("Error validating WhatsApp credentials:", error);
    return {
      isValid: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}