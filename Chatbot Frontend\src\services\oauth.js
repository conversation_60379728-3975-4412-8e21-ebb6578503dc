import api from "./api";

/**
 * OAuth service for Meta platform integrations
 */

class OAuthService {
  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || "http://localhost:3000";
  }

  /**
   * Generate OAuth authorization URL
   */
  async generateAuthURL(authId, platform) {
    try {
      const redirectUri = `${window.location.origin}/dashboard/oauth/callback`;
      
      const response = await api.post(`/api/ai/oauth/${platform}/authorize`, {
        redirectUri,
      }, {
        headers: { "x-auth-id": authId },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to generate ${platform} OAuth URL`,
      );
    }
  }

  /**
   * Handle OAuth callback
   */
  async handleCallback(platform, code, state) {
    try {
      const redirectUri = `${window.location.origin}/dashboard/oauth/callback`;
      
      const response = await api.post(`/api/ai/oauth/${platform}/callback`, {
        code,
        state,
        redirectUri,
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to process ${platform} OAuth callback`,
      );
    }
  }

  /**
   * Complete platform setup after OAuth
   */
  async completePlatformSetup(authId, platform, setupData) {
    try {
      const response = await api.post(`/api/ai/oauth/${platform}/setup`, setupData, {
        headers: { "x-auth-id": authId },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to complete ${platform} setup`,
      );
    }
  }

  /**
   * Disconnect OAuth integration
   */
  async disconnectPlatform(authId, platform) {
    try {
      const response = await api.post(`/api/ai/oauth/${platform}/disconnect`, {}, {
        headers: { "x-auth-id": authId },
      });

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || `Failed to disconnect ${platform}`,
      );
    }
  }

  /**
   * Start OAuth flow by opening popup window
   */
  async startOAuthFlow(authId, platform) {
    // Store platform in localStorage for callback detection
    localStorage.setItem('oauth_platform', platform);

    // Generate OAuth URL
    const urlResult = await this.generateAuthURL(authId, platform);

    if (!urlResult.success) {
      throw new Error(urlResult.error);
    }

    // Open OAuth popup
    const popup = window.open(
      urlResult.data.authUrl,
      `${platform}_oauth`,
      'width=600,height=700,scrollbars=yes,resizable=yes'
    );

    if (!popup) {
      throw new Error('Popup blocked. Please allow popups for this site.');
    }

    // Wait for OAuth completion
    return new Promise((resolve, reject) => {
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          localStorage.removeItem('oauth_platform'); // Clean up
          reject(new Error('OAuth flow was cancelled'));
        }
      }, 1000);

      // Listen for OAuth completion message
      const messageHandler = (event) => {
        if (event.origin !== window.location.origin) {
          return;
        }

        if (event.data.type === 'OAUTH_SUCCESS') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          localStorage.removeItem('oauth_platform'); // Clean up
          popup.close();
          resolve(event.data.result);
        } else if (event.data.type === 'OAUTH_ERROR') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          localStorage.removeItem('oauth_platform'); // Clean up
          popup.close();
          reject(new Error(event.data.error));
        }
      };

      window.addEventListener('message', messageHandler);

      // Timeout after 5 minutes
      setTimeout(() => {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageHandler);
        if (!popup.closed) {
          popup.close();
        }
        reject(new Error('OAuth flow timed out'));
      }, 5 * 60 * 1000);
    });
  }

  /**
   * WhatsApp OAuth flow (disabled - WhatsApp uses system tokens)
   */
  async connectWhatsApp(authId) {
    throw new Error('WhatsApp uses system tokens, not OAuth. Please use the WhatsApp Integration page.');
  }

  /**
   * Messenger OAuth flow
   */
  async connectMessenger(authId) {
    return this.startOAuthFlow(authId, 'messenger');
  }

  /**
   * Instagram OAuth flow
   */
  async connectInstagram(authId) {
    return this.startOAuthFlow(authId, 'instagram');
  }

  /**
   * Disconnect WhatsApp (disabled - WhatsApp uses system tokens)
   */
  async disconnectWhatsApp(authId) {
    throw new Error('WhatsApp uses system tokens, not OAuth. Please use the WhatsApp Integration page.');
  }

  /**
   * Disconnect Messenger
   */
  async disconnectMessenger(authId) {
    return this.disconnectPlatform(authId, 'messenger');
  }

  /**
   * Disconnect Instagram
   */
  async disconnectInstagram(authId) {
    return this.disconnectPlatform(authId, 'instagram');
  }

  /**
   * Parse OAuth callback URL parameters
   */
  parseCallbackParams(url = window.location.href) {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    
    return {
      code: params.get('code'),
      state: params.get('state'),
      error: params.get('error'),
      error_description: params.get('error_description'),
    };
  }

  /**
   * Check if current URL is an OAuth callback
   */
  isOAuthCallback(url = window.location.href) {
    const urlObj = new URL(url);
    return urlObj.pathname.includes('/oauth/callback');
  }

  /**
   * Get platform from OAuth state or URL
   */
  getPlatformFromCallback(url = window.location.href) {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const callbackIndex = pathParts.indexOf('callback');
    
    if (callbackIndex > 0) {
      return pathParts[callbackIndex - 1];
    }
    
    return null;
  }
}

export default new OAuthService();
