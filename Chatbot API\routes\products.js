import express from "express";
import supabase from "../utils/supabase.js";
import OpenAI from "openai";
import dotenv from "dotenv";
import multer from "multer";
import path from "path";
import {
  getPaginationParams,
  buildPaginatedResponse,
} from "../utils/pagination.js";

dotenv.config();

const router = express.Router();

// Configure multer for product image uploads
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  // Only allow image files
  if (file.mimetype.startsWith('image/')) {
    // Check for supported image formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (supportedFormats.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported image format. Please use JPEG, PNG, GIF, or WebP.'), false);
    }
  } else {
    cb(new Error('Only image files are allowed.'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit for product images
    files: 1
  }
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Add product to catalog
router.post("/add-product", async (req, res) => {
  try {
    const {
      authId,
      name,
      description,
      price,
      variations,
      category,
      stockQuantity,
      isService,
      imageUrl,
      imageFilename,
      imageMimeType,
      imageFileSize,
    } = req.body;

    if (!authId || !name) {
      return res.status(400).json({
        error: "Missing required fields: authId, name",
      });
    }

    // For services, stock quantity should be null or 0
    const finalStockQuantity = isService ? 0 : (stockQuantity || 0);

    const { data, error } = await supabase
      .from("product_catalog")
      .insert({
        auth_id: authId,
        name: name,
        description: description,
        price: price,
        variations: variations || [],
        category: category,
        stock_quantity: finalStockQuantity,
        is_service: isService || false,
        is_active: true,
        image_url: imageUrl,
        image_filename: imageFilename,
        image_mime_type: imageMimeType,
        image_file_size: imageFileSize,
      })
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      message: `${isService ? 'Service' : 'Product'} added successfully`,
      data: data,
    });
  } catch (error) {
    console.error("Error adding product:", error);
    res.status(500).json({ error: "Failed to add product" });
  }
});

// Get products for a user
router.get("/list", async (req, res) => {
  try {
    const {
      authId,
      category,
      isActive = true,
      search,
      sortBy = "created_at",
      sortOrder = "desc",
    } = req.query;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId parameter" });
    }

    // Get pagination parameters
    const { limit, page, offset } = getPaginationParams(req.query);

    let query = supabase
      .from("product_catalog")
      .select("*", { count: "exact" })
      .eq("auth_id", authId);

    // Apply filters
    if (category) {
      query = query.eq("category", category);
    }

    if (isActive !== undefined) {
      query = query.eq("is_active", isActive === "true");
    }

    if (search) {
      query = query.or(
        `name.ilike.%${search}%, description.ilike.%${search}%, category.ilike.%${search}%`,
      );
    }

    // Apply sorting
    const validSortFields = [
      "created_at",
      "updated_at",
      "name",
      "price",
      "category",
      "stock_quantity",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const ascending = sortOrder === "asc";

    query = query.order(sortField, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    res.json(buildPaginatedResponse(data, count || 0, page, limit));
  } catch (error) {
    console.error("Error getting products:", error);
    res.status(500).json({ error: "Failed to get products" });
  }
});

// Update product
router.put("/update", async (req, res) => {
  try {
    const {
      authId,
      productId,
      name,
      description,
      price,
      variations,
      category,
      stockQuantity,
      isService,
      isActive,
      imageUrl,
      imageFilename,
      imageMimeType,
      imageFileSize,
    } = req.body;

    if (!authId || !productId) {
      return res.status(400).json({
        error: "Missing required fields: authId, productId",
      });
    }

    const updates = { updated_at: new Date().toISOString() };
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (price !== undefined) updates.price = price;
    if (variations !== undefined) updates.variations = variations;
    if (category !== undefined) updates.category = category;
    if (stockQuantity !== undefined) {
      // For services, don't allow stock quantity updates unless converting from service to product
      if (isService === false || isService === undefined) {
        updates.stock_quantity = stockQuantity;
      }
    }
    if (isService !== undefined) {
      updates.is_service = isService;
      // If converting to service, set stock to 0
      if (isService) {
        updates.stock_quantity = 0;
      }
    }
    if (isActive !== undefined) updates.is_active = isActive;
    if (imageUrl !== undefined) updates.image_url = imageUrl;
    if (imageFilename !== undefined) updates.image_filename = imageFilename;
    if (imageMimeType !== undefined) updates.image_mime_type = imageMimeType;
    if (imageFileSize !== undefined) updates.image_file_size = imageFileSize;

    const { data, error } = await supabase
      .from("product_catalog")
      .update(updates)
      .eq("id", productId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({ error: "Product not found" });
    }

    res.json({
      success: true,
      message: "Product updated successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error updating product:", error);
    res.status(500).json({ error: "Failed to update product" });
  }
});

// Delete product
router.delete("/delete", async (req, res) => {
  try {
    const { authId, productId } = req.body;

    if (!authId || !productId) {
      return res.status(400).json({
        error: "Missing required fields: authId, productId",
      });
    }

    const { data, error } = await supabase
      .from("product_catalog")
      .delete()
      .eq("id", productId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({ error: "Product not found" });
    }

    res.json({
      success: true,
      message: "Product deleted successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    res.status(500).json({ error: "Failed to delete product" });
  }
});

// Get formatted product list for chatbot
router.get("/chatbot-list", async (req, res) => {
  try {
    const { authId } = req.query;

    if (!authId) {
      return res.status(400).json({ error: "Missing authId parameter" });
    }

    const { data, error } = await supabase
      .from("product_catalog")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true)
      .order("category", { ascending: true });

    if (error) throw error;

    // Format products for chatbot display
    let formattedList = "📋 *Available Products:*\n\n";

    if (data.length === 0) {
      formattedList = "No products available at the moment.";
    } else {
      // Group by category
      const groupedProducts = data.reduce((acc, product) => {
        const category = product.category || "Other";
        if (!acc[category]) acc[category] = [];
        acc[category].push(product);
        return acc;
      }, {});

      Object.keys(groupedProducts).forEach((category, categoryIndex) => {
        formattedList += `*${category}:*\n`;

        groupedProducts[category].forEach((product, index) => {
          const productNumber = categoryIndex * 10 + index + 1;
          formattedList += `${productNumber}. ${product.name}`;

          if (product.price) {
            formattedList += ` - RM${product.price}`;
          }

          if (product.description) {
            formattedList += `\n   ${product.description}`;
          }

          if (product.variations && product.variations.length > 0) {
            formattedList += `\n   *Variations:* ${product.variations.join(", ")}`;
          }

          formattedList += `\n\n`;
        });
      });
    }

    res.json({
      success: true,
      formattedList: formattedList,
      products: data,
    });
  } catch (error) {
    console.error("Error getting chatbot product list:", error);
    res.status(500).json({ error: "Failed to get product list" });
  }
});

// Intelligent product search using AI
router.post("/search", async (req, res) => {
  try {
    const { authId, query } = req.body;

    if (!authId || !query) {
      return res.status(400).json({
        error: "Missing required fields: authId, query",
      });
    }

    // Get all products for the user
    const { data: products, error } = await supabase
      .from("product_catalog")
      .select("*")
      .eq("auth_id", authId)
      .eq("is_active", true);

    if (error) throw error;

    if (products.length === 0) {
      return res.json({
        success: true,
        matches: [],
        message: "No products found in catalog",
      });
    }

    // Use AI to find matching products
    const productDescriptions = products
      .map(
        (p) =>
          `${p.name}: ${p.description || ""} ${p.category || ""} ${p.variations ? p.variations.join(" ") : ""}`,
      )
      .join("\n");

    const aiResponse = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are a product search assistant. Given a user query and a list of products, identify which products best match the query. Return only the product names that match, separated by commas. If no products match, return "No matches found".

Products available:
${productDescriptions}`,
        },
        {
          role: "user",
          content: `Find products that match: ${query}`,
        },
      ],
      max_tokens: 200,
      temperature: 0.3,
    });

    const aiMatches = aiResponse.choices[0].message.content.trim();

    if (aiMatches === "No matches found") {
      return res.json({
        success: true,
        matches: [],
        message: "No products match your search",
      });
    }

    // Find actual product objects that match AI response
    const matchedNames = aiMatches.split(",").map((name) => name.trim());
    const matchedProducts = products.filter((product) =>
      matchedNames.some(
        (name) =>
          product.name.toLowerCase().includes(name.toLowerCase()) ||
          name.toLowerCase().includes(product.name.toLowerCase()),
      ),
    );

    res.json({
      success: true,
      matches: matchedProducts,
      aiResponse: aiMatches,
    });
  } catch (error) {
    console.error("Error searching products:", error);
    res.status(500).json({ error: "Failed to search products" });
  }
});

// Upload product image
router.post("/upload-image", upload.single('image'), async (req, res) => {
  try {
    const { authId } = req.body;

    if (!authId) {
      return res.status(400).json({
        error: "Missing required field: authId",
      });
    }

    if (!req.file) {
      return res.status(400).json({
        error: "No image file provided",
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1E9);
    const ext = path.extname(req.file.originalname);
    const filename = `product-${timestamp}-${random}${ext}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase
      .storage
      .from('whatsapp-media')
      .upload(`${authId}/products/${filename}`, req.file.buffer, {
        contentType: req.file.mimetype,
        cacheControl: '31536000',
        upsert: false
      });

    if (uploadError) {
      console.error("Error uploading image to Supabase Storage:", uploadError);
      return res.status(500).json({ error: "Failed to upload image" });
    }

    // Get public URL
    const { data: urlData } = supabase
      .storage
      .from('whatsapp-media')
      .getPublicUrl(`${authId}/products/${filename}`);

    res.json({
      success: true,
      message: "Image uploaded successfully",
      data: {
        imageUrl: urlData.publicUrl,
        imageFilename: req.file.originalname,
        imageMimeType: req.file.mimetype,
        imageFileSize: req.file.size,
        storagePath: uploadData.path
      },
    });
  } catch (error) {
    console.error("Error uploading product image:", error);
    res.status(500).json({ error: "Failed to upload image" });
  }
});

// Delete product image
router.delete("/delete-image", async (req, res) => {
  try {
    const { authId, productId, storagePath } = req.body;

    if (!authId || !productId) {
      return res.status(400).json({
        error: "Missing required fields: authId, productId",
      });
    }

    // Remove image from storage if storagePath provided
    if (storagePath) {
      const { error: deleteError } = await supabase
        .storage
        .from('whatsapp-media')
        .remove([storagePath]);

      if (deleteError) {
        console.error("Error deleting image from storage:", deleteError);
        // Continue with database update even if storage deletion fails
      }
    }

    // Update product to remove image references
    const { data, error } = await supabase
      .from("product_catalog")
      .update({
        image_url: null,
        image_filename: null,
        image_mime_type: null,
        image_file_size: null,
        updated_at: new Date().toISOString()
      })
      .eq("id", productId)
      .eq("auth_id", authId)
      .select()
      .single();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({ error: "Product not found" });
    }

    res.json({
      success: true,
      message: "Product image deleted successfully",
      data: data,
    });
  } catch (error) {
    console.error("Error deleting product image:", error);
    res.status(500).json({ error: "Failed to delete image" });
  }
});

export default router;
