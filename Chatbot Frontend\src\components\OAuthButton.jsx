import { useState } from "react";
import { <PERSON>L<PERSON>, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, Unplug } from "lucide-react";

export default function OAuthButton({ 
  platform, 
  onConnect, 
  onDisconnect, 
  isConnected = false, 
  isLoading = false, 
  disabled = false,
  className = "",
  children 
}) {
  const [connecting, setConnecting] = useState(false);

  const handleClick = async () => {
    if (isConnected) {
      if (onDisconnect) {
        await onDisconnect();
      }
    } else {
      if (onConnect) {
        setConnecting(true);
        try {
          await onConnect();
        } finally {
          setConnecting(false);
        }
      }
    }
  };

  const isProcessing = isLoading || connecting;

  const getButtonStyles = () => {
    if (isConnected) {
      switch (platform) {
        case 'whatsapp':
          return "bg-green-100 hover:bg-green-200 text-green-700 border border-green-400";
        case 'messenger':
          return "bg-blue-700 hover:bg-blue-800 text-white border border-blue-800";
        case 'instagram':
          return "bg-gradient-to-r from-pink-100 to-purple-100 hover:from-pink-200 hover:to-purple-200 text-pink-700 border border-pink-400";
        default:
          return "bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-400";
      }
    }
    
    switch (platform) {
      case 'whatsapp':
        return "bg-green-500 hover:bg-green-600 text-white";
      case 'messenger':
        return "bg-blue-600 hover:bg-blue-700 text-white";
      case 'instagram':
        return "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white";
      default:
        return "bg-gray-600 hover:bg-gray-700 text-white";
    }
  };

  const getButtonText = () => {
    if (isProcessing) {
      return isConnected ? "Disconnecting..." : "Connecting...";
    }
    
    if (isConnected) {
      return "Disconnect";
    }
    
    return `Connect with ${platform.charAt(0).toUpperCase() + platform.slice(1)}`;
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled || isProcessing}
      className={`
        flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium
        transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed
        ${getButtonStyles()} ${className}
      `}
    >
      {isProcessing ? (
        <Loader2 className="w-4 h-4 animate-spin" />
      ) : isConnected ? (
        <Unplug className="w-4 h-4" />
      ) : (
        <ExternalLink className="w-4 h-4" />
      )}
      
      <span>{children || getButtonText()}</span>
    </button>
  );
}

// Platform-specific OAuth buttons
export function WhatsAppOAuthButton(props) {
  return <OAuthButton {...props} platform="whatsapp" />;
}

export function MessengerOAuthButton(props) {
  return <OAuthButton {...props} platform="messenger" />;
}

export function InstagramOAuthButton(props) {
  return <OAuthButton {...props} platform="instagram" />;
}
