import { Link } from "react-router-dom";
import Icon from "../components/ui/icon";

export default function ResetPasswordComplete() {
  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8 fade-in">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Icon name="CheckCircle" className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Password reset complete!
          </h1>
          <p className="text-gray-600">
            Your password has been successfully updated
          </p>
        </div>

        {/* Success Card */}
        <div className="card space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <Icon
                name="CheckCircle"
                className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5"
              />
              <div>
                <h3 className="text-sm font-semibold text-green-900 mb-1">
                  Password updated successfully
                </h3>
                <p className="text-sm text-green-700">
                  You can now sign in to your account using your new password.
                  Make sure to keep it secure.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-gray-700">
              Security tips:
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                <span>Use a unique password for your account</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                <span>Keep your password secure and don't share it</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                <span>Update your password regularly</span>
              </li>
            </ul>
          </div>

          {/* Sign In Button */}
          <div className="text-center pt-6 border-t border-gray-200">
            <Link to="/login" className="btn-primary w-full">
              Sign In to Your Account
            </Link>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Need help? Contact our{" "}
            <a
              href="#"
              className="text-blue-600 hover:text-blue-700 font-semibold"
            >
              support team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
