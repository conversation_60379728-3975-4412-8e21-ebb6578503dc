import { useState, useEffect } from "react";
import { Routes, Route, useLocation, useNavigate } from "react-router-dom";
import { LayoutDashboard, Users, ArrowLeft, Settings } from "lucide-react";
import Icon from "../../components/ui/icon";

// Import mobile-specific components
import MobileOverview from "./MobileOverview";
import MobileContacts from "./MobileContacts";
import MobileChatView from "./MobileChatView";
import { MobileAlertProvider } from "../../contexts/MobileAlertContext";
import MobileAlertContainer from "../../components/MobileAlertContainer";

/**
 * Mobile Dashboard Component
 * Provides a mobile-optimized interface with bottom navigation
 * and full-screen views for Overview and Contacts
 */
export default function MobileDashboard({ user, planInfo }) {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");



  // Settings state
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    autoRefresh: true
  });

  // Update active tab based on current route
  useEffect(() => {
    const path = location.pathname;


    if (path === "/dashboard" || path === "/dashboard/" || path === "/dashboard/overview") {
      setActiveTab("overview");
    } else if (path.startsWith("/dashboard/contacts")) {
      setActiveTab("contacts");
    }
  }, [location.pathname]);

  // Navigation items for mobile bottom nav
  const navItems = [
    {
      id: "overview",
      label: "Overview",
      icon: "LayoutDashboard",
      path: "/dashboard",
    },
    {
      id: "contacts",
      label: "Contacts",
      icon: "Users",
      path: "/dashboard/contacts",
    },
  ];

  const handleNavigation = (item) => {
    setActiveTab(item.id);
    navigate(item.path);
  };

  // Settings handlers
  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // Save to localStorage
    localStorage.setItem('mobileSettings', JSON.stringify({
      ...settings,
      [key]: value
    }));
  };

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('mobileSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (e) {
        console.error('Failed to parse saved settings:', e);
      }
    }
  }, []);

  // Check if we're in a chat view (has contact ID in URL)
  const isInChatView = location.pathname.includes("/dashboard/contacts/chat/");



  return (
    <MobileAlertProvider>
      <div className="fixed inset-0 top-20 bg-gray-50 flex flex-col overflow-hidden">
      {/* Mobile Header - Only show for non-chat views */}
      {!isInChatView && (
        <header className="bg-white shadow-sm border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          {/* Default header */}
          <>
            <div>
              <h1 className="text-lg font-bold text-gray-900">
                {activeTab === "overview" ? "Overview" : "Contacts"}
              </h1>
              <p className="text-xs text-gray-500">
                {user?.username || "User"} • {planInfo?.name || "Free Plan"}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Settings className="w-5 h-5 text-gray-600" />
              </button>
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white text-sm font-semibold">
                {(user?.username || "U").charAt(0).toUpperCase()}
              </div>
            </div>
          </>
        </header>
      )}

      {/* Main Content Area */}
      <main className="flex-1 overflow-hidden relative">
        <div className="h-full mobile-fade-in">
          <Routes>
            <Route path="/" element={<MobileOverview user={user} settings={settings} />} />
            <Route path="/contacts" element={<MobileContacts user={user} settings={settings} />} />
            <Route path="/contacts/chat/:contactId" element={<MobileChatView user={user} settings={settings} />} />
          </Routes>
        </div>
      </main>

      {/* Bottom Navigation - Hidden in chat view */}
      {!isInChatView && (
        <nav className="bg-white border-t border-gray-200 px-4 py-3 shadow-lg" style={{ minHeight: '70px' }}>
          <div className="flex justify-around">
            {navItems.map((item) => {
              const isActive = activeTab === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item)}
                  className={`flex flex-col items-center gap-1 py-2 px-4 rounded-lg transition-all duration-200 ${
                    isActive
                      ? "text-blue-600 bg-blue-50"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <Icon name={item.icon} className="w-5 h-5" />
                  <span className="text-xs font-medium">{item.label}</span>
                  {isActive && (
                    <div className="w-1 h-1 bg-blue-600 rounded-full mt-1"></div>
                  )}
                </button>
              );
            })}
          </div>
        </nav>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white w-full max-w-md rounded-xl p-6 mobile-fade-in">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Settings</h3>
              <button
                onClick={() => setShowSettings(false)}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <Icon name="X" className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Auto Refresh */}
              <div className="flex items-center justify-between">
                <div className="flex-1 pr-4">
                  <h4 className="font-medium text-gray-900">Auto Refresh</h4>
                  <p className="text-sm text-gray-500">Automatically refresh chat messages</p>
                </div>
                <div className="relative">
                  <input
                    type="checkbox"
                    id="auto-refresh-toggle"
                    checked={settings.autoRefresh}
                    onChange={(e) => updateSetting('autoRefresh', e.target.checked)}
                    className="sr-only"
                  />
                  <label
                    htmlFor="auto-refresh-toggle"
                    className={`relative inline-flex h-6 w-11 cursor-pointer items-center rounded-full transition-colors duration-200 ease-in-out focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 ${
                      settings.autoRefresh ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200 ease-in-out ${
                        settings.autoRefresh ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </label>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setShowSettings(false)}
                className="w-full px-4 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Alert Container */}
      <MobileAlertContainer />
    </div>
    </MobileAlertProvider>
  );
}
