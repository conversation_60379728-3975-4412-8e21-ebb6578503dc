// Simple logging utility for production optimization
// Enables/disables verbose logging based on environment

const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// Minimal logging - only errors in production, errors and warnings in development
const CURRENT_LOG_LEVEL = process.env.NODE_ENV === 'production' ? LOG_LEVELS.ERROR : LOG_LEVELS.ERROR;

const logger = {
  error: (message, ...args) => {
    if (CURRENT_LOG_LEVEL >= LOG_LEVELS.ERROR) {
      console.error(`❌ [ERROR] ${message}`, ...args);
    }
  },
  
  warn: (message, ...args) => {
    if (CURRENT_LOG_LEVEL >= LOG_LEVELS.WARN) {
      console.warn(`⚠️ [WARN] ${message}`, ...args);
    }
  },
  
  info: (message, ...args) => {
    if (CURRENT_LOG_LEVEL >= LOG_LEVELS.INFO) {
      console.log(`ℹ️ [INFO] ${message}`, ...args);
    }
  },
  
  debug: (message, ...args) => {
    if (CURRENT_LOG_LEVEL >= LOG_LEVELS.DEBUG) {
      console.log(`🔍 [DEBUG] ${message}`, ...args);
    }
  },
  
  // WhatsApp operational logs (only in development)
  whatsapp: (message, ...args) => {
    if (CURRENT_LOG_LEVEL >= LOG_LEVELS.INFO) {
      console.log(`📱 [WhatsApp] ${message}`, ...args);
    }
  }
};

export default logger; 