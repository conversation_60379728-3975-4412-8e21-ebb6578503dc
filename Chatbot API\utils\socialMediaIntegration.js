import { supabase } from "./supabase.js";
import logger from "./logger.js";
import { validateWhatsAppCredentials } from "./whatsapp.js";
import { validateMessengerCredentials } from "./messenger.js";
import { validateInstagramCredentials } from "./instagram.js";

/**
 * Get social media integration configuration for a user
 */
export async function getSocialMediaIntegrations(authId) {
  try {
    const { data, error } = await supabase
      .from("social_media_integrations")
      .select("*")
      .eq("auth_id", authId);

    if (error) {
      logger.error("Error fetching social media integrations:", error);
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error("Error in getSocialMediaIntegrations:", error);
    throw error;
  }
}

/**
 * Get specific platform integration
 */
export async function getPlatformIntegration(authId, platform) {
  try {
    const { data, error } = await supabase
      .from("social_media_integrations")
      .select("*")
      .eq("auth_id", authId)
      .eq("platform", platform)
      .single();

    if (error && error.code !== "PGRST116") {
      logger.error(`Error fetching ${platform} integration:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error in getPlatformIntegration for ${platform}:`, error);
    throw error;
  }
}

/**
 * Save or update social media integration
 */
export async function saveSocialMediaIntegration(authId, platform, config) {
  try {
    const integrationData = {
      auth_id: authId,
      platform: platform,
      is_enabled: config.isEnabled !== undefined ? config.isEnabled : true,
      updated_at: new Date().toISOString(),
    };

    // Add platform-specific fields
    switch (platform) {
      case "whatsapp":
        integrationData.whatsapp_business_account_id = config.whatsappBusinessAccountId;
        integrationData.whatsapp_phone_number_id = config.whatsappPhoneNumberId;
        integrationData.system_access_token = config.systemAccessToken;
        break;
      case "messenger":
        integrationData.messenger_page_id = config.messengerPageId;
        integrationData.messenger_access_token = config.messengerAccessToken;
        integrationData.messenger_app_id = config.messengerAppId;
        break;
      case "instagram":
        integrationData.instagram_account_id = config.instagramAccountId;
        integrationData.instagram_access_token = config.instagramAccessToken;
        break;
    }

    const { data, error } = await supabase
      .from("social_media_integrations")
      .upsert(integrationData, {
        onConflict: 'auth_id,platform'
      })
      .select()
      .single();

    if (error) {
      logger.error(`Error saving ${platform} integration:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error in saveSocialMediaIntegration for ${platform}:`, error);
    throw error;
  }
}

/**
 * Toggle platform integration on/off
 */
export async function togglePlatformIntegration(authId, platform, isEnabled) {
  try {
    const { data, error } = await supabase
      .from("social_media_integrations")
      .update({
        is_enabled: isEnabled,
        updated_at: new Date().toISOString(),
      })
      .eq("auth_id", authId)
      .eq("platform", platform)
      .select()
      .single();

    if (error) {
      logger.error(`Error toggling ${platform} integration:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error in togglePlatformIntegration for ${platform}:`, error);
    throw error;
  }
}

/**
 * Validate platform credentials
 */
export async function validatePlatformCredentials(platform, credentials) {
  try {
    switch (platform) {
      case "whatsapp":
        return await validateWhatsAppCredentials(
          credentials.whatsappBusinessAccountId,
          credentials.whatsappPhoneNumberId,
          credentials.systemAccessToken
        );
      case "messenger":
        return await validateMessengerCredentials(
          credentials.messengerAccessToken,
          credentials.messengerPageId
        );
      case "instagram":
        return await validateInstagramCredentials(
          credentials.instagramAccessToken,
          credentials.instagramAccountId
        );
      default:
        return { isValid: false, error: "Unsupported platform" };
    }
  } catch (error) {
    logger.error(`Error validating ${platform} credentials:`, error);
    return { isValid: false, error: error.message };
  }
}

/**
 * Get enabled platforms for a user
 */
export async function getEnabledPlatforms(authId) {
  try {
    const { data, error } = await supabase
      .from("social_media_integrations")
      .select("platform")
      .eq("auth_id", authId)
      .eq("is_enabled", true);

    if (error) {
      logger.error("Error fetching enabled platforms:", error);
      throw error;
    }

    return data.map(item => item.platform);
  } catch (error) {
    logger.error("Error in getEnabledPlatforms:", error);
    throw error;
  }
}

/**
 * Find customer by platform and identifier
 */
export async function findCustomerByPlatformId(platform, platformId) {
  try {
    let query;
    
    switch (platform) {
      case "whatsapp":
        query = supabase
          .from("social_media_integrations")
          .select("*")
          .eq("platform", "whatsapp")
          .eq("whatsapp_phone_number_id", platformId)
          .eq("is_enabled", true);
        break;
      case "messenger":
        query = supabase
          .from("social_media_integrations")
          .select("*")
          .eq("platform", "messenger")
          .eq("messenger_page_id", platformId)
          .eq("is_enabled", true);
        break;
      case "instagram":
        query = supabase
          .from("social_media_integrations")
          .select("*")
          .eq("platform", "instagram")
          .eq("instagram_account_id", platformId)
          .eq("is_enabled", true);
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    const { data, error } = await query.single();

    if (error && error.code !== "PGRST116") {
      logger.error(`Error finding customer by ${platform} ID:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error in findCustomerByPlatformId for ${platform}:`, error);
    throw error;
  }
}

/**
 * Migrate existing WhatsApp customers to new social media integrations table
 */
export async function migrateWhatsAppCustomers() {
  try {
    // Get all existing WhatsApp customers
    const { data: whatsappCustomers, error: fetchError } = await supabase
      .from("whatsapp_customers")
      .select("*")
      .eq("is_active", true);

    if (fetchError) {
      logger.error("Error fetching WhatsApp customers for migration:", fetchError);
      throw fetchError;
    }

    if (!whatsappCustomers || whatsappCustomers.length === 0) {
      logger.info("No WhatsApp customers to migrate");
      return { migrated: 0 };
    }

    let migratedCount = 0;

    for (const customer of whatsappCustomers) {
      try {
        // Check if already migrated
        const existing = await getPlatformIntegration(customer.auth_id, "whatsapp");
        if (existing) {
          logger.info(`WhatsApp integration already exists for user ${customer.auth_id}`);
          continue;
        }

        // Migrate to new table
        await saveSocialMediaIntegration(customer.auth_id, "whatsapp", {
          whatsappBusinessAccountId: customer.whatsapp_business_account_id,
          whatsappPhoneNumberId: customer.whatsapp_phone_number_id,
          systemAccessToken: customer.system_access_token,
          isEnabled: customer.is_active,
        });

        migratedCount++;
        logger.info(`Migrated WhatsApp customer ${customer.auth_id}`);
      } catch (error) {
        logger.error(`Error migrating WhatsApp customer ${customer.auth_id}:`, error);
      }
    }

    logger.info(`Migration completed. Migrated ${migratedCount} WhatsApp customers`);
    return { migrated: migratedCount };
  } catch (error) {
    logger.error("Error in migrateWhatsAppCustomers:", error);
    throw error;
  }
}
