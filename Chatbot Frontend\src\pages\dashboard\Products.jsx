import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { productsService } from "../../services/products";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { usePagination } from "../../hooks/usePagination";
import { Pagination } from "../../components/ui/pagination";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import Icon from "../../components/ui/icon";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select2";

export default function Products() {
  const { user } = useAuth();
  const { showSuccess, showError } = useAlertMigration();
  const [products, setProducts] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showActiveOnly, setShowActiveOnly] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Pagination hook
  const pagination = usePagination({
    initialLimit: 8,
    initialSortBy: "created_at",
    initialSortOrder: "desc",
  });

  const showLoadingState = useDelayedLoading(pagination.loading, 200);

  // Form states
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    category: "none",
    stockQuantity: "",
    variations: [""],
    isActive: true,
    isService: false,
  });

  // Image upload states
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [imageUploading, setImageUploading] = useState(false);

  // Categories for filtering (you can make this dynamic by fetching from your products)
  const categories = [
    "Food",
    "Beverages",
    "Electronics",
    "Clothing",
    "Books",
    "Services",
    "Other",
  ];

  // Fetch products with pagination
  const fetchProducts = async () => {
    if (!user?.user?.id) {
      pagination.setLoading(false);
      return;
    }

    try {
      pagination.setLoading(true);
      pagination.setError("");

      const params = {
        ...pagination.getQueryParams(),
        authId: user.user.id,
        isActive: showActiveOnly,
        category: selectedCategory !== "all" ? selectedCategory : undefined,
      };

      const response = await productsService.getProductsList(params);

      if (response.success) {
        setProducts(response.data);
        pagination.updatePagination(response);
      } else {
        pagination.setError(response.error);
      }
    } catch (error) {
      pagination.setError("Failed to fetch products");
      console.error("Error fetching products:", error);
    } finally {
      pagination.setLoading(false);
    }
  };

  // Debounced search effect
  useEffect(() => {
    if (!user) return;

    const timeoutId = setTimeout(() => {
      pagination.updateSearch(searchTerm);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, user]);

  // Effect to fetch data when pagination parameters change
  useEffect(() => {
    if (user) {
      fetchProducts();
    }
  }, [
    user,
    pagination.page,
    pagination.limit,
    pagination.sortBy,
    pagination.sortOrder,
    pagination.search,
    showActiveOnly,
    selectedCategory,
  ]);

  const handleImageSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        showError('Please select a valid image file');
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        showError('Image size must be less than 5MB');
        return;
      }

      setSelectedImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    // Reset file input
    const fileInput = document.getElementById('product-image');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);

    // Validation
    if (!user?.user?.id) {
      showError("User not authenticated");
      setFormLoading(false);
      return;
    }

    if (!formData.name.trim()) {
      showError("Product name is required");
      setFormLoading(false);
      return;
    }

    try {
      let imageData = {};

      // Upload image if selected
      if (selectedImage) {
        setImageUploading(true);
        try {
          const uploadResponse = await productsService.uploadProductImage(
            selectedImage,
            user.user.id
          );

          if (uploadResponse.success) {
            imageData = {
              imageUrl: uploadResponse.data.imageUrl,
              imageFilename: uploadResponse.data.imageFilename,
              imageMimeType: uploadResponse.data.imageMimeType,
              imageFileSize: uploadResponse.data.imageFileSize,
            };
          }
        } catch (uploadError) {
          console.error("Error uploading image:", uploadError);
          showError("Failed to upload image. Product will be saved without image.");
        } finally {
          setImageUploading(false);
        }
      }

      const payload = {
        authId: user.user.id,
        name: formData.name.trim(),
        description: formData.description,
        price: parseFloat(formData.price) || null,
        category: formData.category === "none" ? "" : formData.category,
        stockQuantity: formData.isService ? 0 : (parseInt(formData.stockQuantity) || 0),
        variations: formData.variations.filter((v) => v.trim() !== ""),
        isService: formData.isService,
        isActive: formData.isActive,
        ...imageData,
      };

      if (editingProduct) {
        payload.productId = editingProduct.id;
      }

      let response;
      if (editingProduct) {
        response = await productsService.updateProduct(payload);
      } else {
        response = await productsService.addProduct(payload);
      }

      if (response.success) {
        resetForm();
        fetchProducts();
        showSuccess(
          `${formData.isService ? 'Service' : 'Product'} ${editingProduct ? "updated" : "added"} successfully!`,
        );
      } else {
        showError("Error: " + response.error);
      }
    } catch (error) {
      console.error("Error saving product:", error);
      showError("Failed to save product");
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteClick = (product) => {
    setProductToDelete(product);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;
    setDeleteLoading(true);

    try {
      const response = await productsService.deleteProduct({
        authId: user.user.id,
        productId: productToDelete.id,
      });

      if (response.success) {
        fetchProducts();
        showSuccess("Product deleted successfully!");
      } else {
        showError("Error: " + response.error);
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      showError("Failed to delete product");
    } finally {
      setDeleteLoading(false);
      setShowDeleteDialog(false);
      setProductToDelete(null);
    }
  };

  const handleEdit = (product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      description: product.description || "",
      price: product.price?.toString() || "",
      category: product.category || "none",
      stockQuantity: product.stock_quantity?.toString() || "",
      variations:
        product.variations && product.variations.length > 0
          ? product.variations
          : [""],
      isActive: product.is_active,
      isService: product.is_service || false,
    });

    // Set existing image if available
    if (product.image_url) {
      setImagePreview(product.image_url);
      setSelectedImage(null); // Don't set selected image for existing images
    } else {
      setImagePreview(null);
      setSelectedImage(null);
    }

    setShowAddForm(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      price: "",
      category: "none",
      stockQuantity: "",
      variations: [""],
      isActive: true,
      isService: false,
    });
    setEditingProduct(null);
    setShowAddForm(false);
    setSelectedImage(null);
    setImagePreview(null);
    setImageUploading(false);

    // Reset file input
    const fileInput = document.getElementById('product-image');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const addVariation = () => {
    setFormData((prev) => ({
      ...prev,
      variations: [...prev.variations, ""],
    }));
  };

  const removeVariation = (index) => {
    setFormData((prev) => ({
      ...prev,
      variations: prev.variations.filter((_, i) => i !== index),
    }));
  };

  const updateVariation = (index, value) => {
    setFormData((prev) => ({
      ...prev,
      variations: prev.variations.map((v, i) => (i === index ? value : v)),
    }));
  };

  if (showLoadingState) {
    return (
      <div className="h-[calc(90vh-6rem)] flex flex-col">
        <PageSkeleton cardType="products" cardCount={9} />
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-160px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 -m-8 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-6 min-h-[calc(100vh-200px)]">
          {/* Header */}
          <div className="flex justify-between items-start flex-shrink-0">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Product Management
              </h2>
              <p className="text-gray-600">
                Manage your product catalog for WhatsApp commerce.
              </p>
            </div>

            <button
              onClick={() => setShowAddForm(true)}
              className="btn-primary flex items-center gap-2 px-4 py-2"
            >
              <Icon name="Plus" className="h-4 w-4" />
              Add Product
            </button>
          </div>

      {/* Success alerts now handled by global AlertContainer */}

      {/* Filters Card */}
      <div className="card flex-shrink-0">
        <div className="flex items-center gap-2 mb-3">
          <Icon name="Filter" className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <label
              htmlFor="search"
              className="block text-sm font-medium text-gray-700"
            >
              Search Products
            </label>
            <input
              id="search"
              type="text"
              placeholder="Search by name, description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field"
            />
          </div>
          <div className="space-y-2">
            <label
              htmlFor="categoryFilter"
              className="block text-sm font-medium text-gray-700"
            >
              Category Filter
            </label>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="input-field">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label
              htmlFor="sortBy"
              className="block text-sm font-medium text-gray-700"
            >
              Sort By
            </label>
            <Select
              value={pagination.sortBy}
              onValueChange={pagination.updateSort}
            >
              <SelectTrigger className="input-field">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Newest First</SelectItem>
                <SelectItem value="updated_at">Recently Updated</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
                <SelectItem value="price">Price</SelectItem>
                <SelectItem value="stock_quantity">Stock Level</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Options
            </label>
            <div className="flex items-center gap-3 border border-gray-200 border-2 rounded-lg p-3">
              <div className="relative">
                <input
                  type="checkbox"
                  id="activeOnly"
                  checked={showActiveOnly}
                  onChange={(e) => setShowActiveOnly(e.target.checked)}
                  className="sr-only"
                />
                <label
                  htmlFor="activeOnly"
                  className={`flex items-center justify-center w-6 h-6 rounded border-2 transition-all duration-200 cursor-pointer ${
                    showActiveOnly
                      ? "bg-blue-600 border-blue-600"
                      : "bg-white border-gray-300 hover:border-blue-400"
                  }`}
                >
                  {showActiveOnly && (
                    <Icon name="Check" className="w-3 h-3 text-white" />
                  )}
                </label>
              </div>
              <label
                htmlFor="activeOnly"
                className="text-md text-gray-700 cursor-pointer truncate"
              >
                Show active products only
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1">
        {/* Products Grid */}
        {products.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {products.map((product) => (
              <div
                key={product.id}
                className="bg-white border border-gray-100 rounded-lg p-3 flex flex-col transition-all duration-200 hover:shadow-md hover:border-blue-200 shadow-sm"
              >
                {/* Product Image */}
                <div className="mb-3 rounded-md overflow-hidden bg-gray-50">
                  {product.image_url ? (
                    <img
                      src={product.image_url}
                      alt={product.name}
                      className="w-full h-24 object-cover"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.parentElement.innerHTML = '<div class="w-full h-24 bg-gray-100 flex items-center justify-center"><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></div>';
                      }}
                    />
                  ) : (
                    <div className="w-full h-24 bg-gray-100 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                  )}
                </div>

                {/* Header with Name and Status */}
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 truncate pr-2 leading-tight">
                    {product.name}
                  </h3>
                  <div
                    className={`w-2 h-2 rounded-full flex-shrink-0 ${
                      product.is_active ? "bg-green-500" : "bg-gray-300"
                    }`}
                    title={product.is_active ? "Active" : "Inactive"}
                  />
                </div>

                {/* Category and Type */}
                <div className="mb-3 flex flex-wrap gap-1.5">
                  {product.category ? (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-sm font-medium bg-gray-50 text-gray-700 border border-gray-200">
                      {product.category}
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-sm font-medium bg-gray-50 text-gray-500 border border-gray-200">
                      No Category
                    </span>
                  )}
                  {product.is_service && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200">
                      Service
                    </span>
                  )}
                </div>

                {/* Description */}
                <div className="flex-1 mb-3">
                  <p className="text-gray-600 text-sm leading-relaxed line-clamp-2 min-h-[2.5rem]">
                    {product.description || "No description available"}
                  </p>
                </div>

                {/* Variations */}
                {product.variations && product.variations.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {product.variations.slice(0, 2).map((variation, index) => (
                      <span
                        key={index}
                        className="px-1.5 py-0.5 bg-gray-50 text-gray-600 text-xs rounded border border-gray-200"
                      >
                        {variation}
                      </span>
                    ))}
                    {product.variations.length > 2 && (
                      <span className="px-1.5 py-0.5 bg-gray-50 text-gray-500 text-xs rounded border border-gray-200">
                        +{product.variations.length - 2}
                      </span>
                    )}
                  </div>
                )}

                {/* Price and Stock */}
                <div className="mb-3 pb-3 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-base font-bold text-gray-900">
                        {product.price
                          ? `RM${parseFloat(product.price).toFixed(2)}`
                          : "N/A"}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      {product.is_service ? (
                        <>
                          <Icon name="Zap" className="h-4 w-4 text-blue-400" />
                          <span className="text-sm text-blue-600 font-medium">
                            Available
                          </span>
                        </>
                      ) : (
                        <>
                          <Icon name="Archive" className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600 font-medium">
                            {product.stock_quantity || 0}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-1.5">
                  <button
                    onClick={() => handleEdit(product)}
                    className="flex-1 px-3 py-2 text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 rounded border border-gray-200 transition-colors flex items-center justify-center gap-1.5"
                  >
                    <Icon name="Edit" className="h-4 w-4" />
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteClick(product)}
                    className="flex-1 px-3 py-2 text-sm bg-red-50 hover:bg-red-100 text-red-700 rounded border border-red-200 transition-colors flex items-center justify-center gap-1.5"
                  >
                    <Icon name="Trash2" className="h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white border border-gray-200 rounded-lg p-12 text-center">
            <div className="w-16 h-16 flex items-center justify-center mx-auto">
              <Icon name="Package" className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No products found
            </h3>
            <p className="text-gray-600 mb-6 max-w-sm mx-auto">
              {pagination.search || selectedCategory || !showActiveOnly
                ? "Try adjusting your filters to see more results."
                : "Get started by adding your first product to the catalog."}
            </p>
            {!pagination.search && !selectedCategory && showActiveOnly && (
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-blue-600 text-white px-6 py-2.5 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Add Your First Product
              </button>
            )}
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="mt-4 flex-shrink-0 w-full">
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            itemsPerPage={pagination.limit}
            onPageChange={pagination.goToPage}
            onItemsPerPageChange={pagination.changeLimit}
            showItemsPerPage={false}
          />
        </div>
      )}

      {/* Add/Edit Product Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!formLoading) {
                resetForm();
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Icon name="Package" className="h-5 w-5" />
                  {editingProduct ? "Edit Product" : "Add New Product"}
                </h3>
                {!formLoading && (
                  <button
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Icon name="X" className="h-5 w-5" />
                  </button>
                )}
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Product Name *
                    </label>
                    <input
                      id="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      placeholder="Enter product name"
                      className="input-field disabled:opacity-60"
                      disabled={formLoading}
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="category"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Category
                    </label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) =>
                        setFormData((prev) => ({
                          ...prev,
                          category: value,
                        }))
                      }
                      disabled={formLoading}
                    >
                      <SelectTrigger className="input-field disabled:opacity-60">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Select Category</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Description
                  </label>
                  <textarea
                    id="description"
                    rows={3}
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder="Describe your product..."
                    className="input-field resize-none disabled:opacity-60"
                    disabled={formLoading}
                  />
                </div>

                {/* Service Type Checkbox */}
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <input
                      type="checkbox"
                      id="isService"
                      checked={formData.isService}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          isService: e.target.checked,
                        }))
                      }
                      className="sr-only"
                      disabled={formLoading}
                    />
                    <label
                      htmlFor="isService"
                      className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-all duration-200 cursor-pointer ${
                        formData.isService
                          ? "bg-blue-600 border-blue-600"
                          : "bg-white border-gray-300 hover:border-blue-400"
                      } ${formLoading ? "opacity-60 cursor-not-allowed" : ""}`}
                    >
                      {formData.isService && (
                        <Icon name="Check" className="w-3 h-3 text-white" />
                      )}
                    </label>
                  </div>
                  <label
                    htmlFor="isService"
                    className="text-sm font-medium text-gray-700 cursor-pointer"
                  >
                    This is a service (No stock tracking)
                  </label>
                </div>

                {/* Image Upload */}
                <div>
                  <label
                    htmlFor="product-image"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Product Image
                  </label>
                  <div className="space-y-3">
                    {/* Image Preview */}
                    {imagePreview && (
                      <div className="relative inline-block">
                        <img
                          src={imagePreview}
                          alt="Product preview"
                          className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={handleRemoveImage}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                          disabled={formLoading}
                        >
                          <Icon name="X" className="w-3 h-3" />
                        </button>
                      </div>
                    )}

                    {/* File Input */}
                    <input
                      id="product-image"
                      type="file"
                      accept="image/*"
                      onChange={handleImageSelect}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-60"
                      disabled={formLoading || imageUploading}
                    />
                    <p className="text-xs text-gray-500">
                      Supported formats: JPEG, PNG, GIF, WebP. Max size: 5MB
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="price"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Price (RM)
                    </label>
                    <input
                      id="price"
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          price: e.target.value,
                        }))
                      }
                      placeholder="0.00"
                      className="input-field disabled:opacity-60"
                      disabled={formLoading}
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="stock"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Stock Quantity
                      {formData.isService && (
                        <span className="text-xs text-blue-600 ml-1">(Not applicable for services)</span>
                      )}
                    </label>
                    <input
                      id="stock"
                      type="number"
                      value={formData.isService ? "0" : formData.stockQuantity}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          stockQuantity: e.target.value,
                        }))
                      }
                      placeholder="0"
                      className="input-field disabled:opacity-60"
                      disabled={formLoading || formData.isService}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Variations
                  </label>
                  <div className="space-y-2">
                    {formData.variations.map((variation, index) => (
                      <div key={index} className="flex gap-2">
                        <input
                          type="text"
                          placeholder="e.g., Small, Medium, Large"
                          value={variation}
                          onChange={(e) =>
                            updateVariation(index, e.target.value)
                          }
                          className="flex-1 input-field disabled:opacity-60"
                          disabled={formLoading}
                        />
                        {formData.variations.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeVariation(index)}
                            className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-60"
                            disabled={formLoading}
                          >
                            <Icon name="Trash2" className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={addVariation}
                      className="w-full text-blue-600 hover:text-blue-800 text-sm font-medium py-2 border border-dashed border-blue-300 rounded-lg hover:bg-blue-50 transition-colors disabled:opacity-60"
                      disabled={formLoading}
                    >
                      <Icon name="Plus" className="h-4 w-4 inline mr-2" />
                      Add Variation
                    </button>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="relative">
                    <input
                      type="checkbox"
                      id="isActive"
                      checked={formData.isActive}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          isActive: e.target.checked,
                        }))
                      }
                      className="sr-only"
                      disabled={formLoading}
                    />
                    <label
                      htmlFor="isActive"
                      className={`flex items-center justify-center w-6 h-6 rounded border-2 transition-all duration-200 cursor-pointer ${
                        formData.isActive
                          ? "bg-blue-600 border-blue-600"
                          : "bg-white border-gray-300 hover:border-blue-400"
                      } ${formLoading ? "opacity-60 cursor-not-allowed" : ""}`}
                    >
                      {formData.isActive && (
                        <Icon name="Check" className="w-3 h-3 text-white" />
                      )}
                    </label>
                  </div>
                  <label
                    htmlFor="isActive"
                    className="text-sm text-gray-700 cursor-pointer"
                  >
                    Product is active and available for sale
                  </label>
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                    disabled={formLoading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 text-white px-4 py-2.5 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    disabled={formLoading || imageUploading}
                  >
                    {formLoading || imageUploading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        {imageUploading ? "Uploading image..." : (editingProduct ? "Updating..." : "Adding...")}
                      </>
                    ) : (
                      <>{editingProduct ? `Update ${formData.isService ? 'Service' : 'Product'}` : `Add ${formData.isService ? 'Service' : 'Product'}`}</>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteDialog && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!deleteLoading) {
                setShowDeleteDialog(false);
                setProductToDelete(null);
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-xl transform transition-all">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Confirm Delete
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete{" "}
                <strong>"{productToDelete?.name}"</strong>? This action cannot
                be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => handleDeleteConfirm()}
                  className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  disabled={deleteLoading}
                >
                  {deleteLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Icon name="Trash2" className="h-4 w-4" />
                      Delete Product
                    </>
                  )}
                </button>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setProductToDelete(null);
                  }}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={deleteLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
        </div>
      </div>
    </div>
  );
}
