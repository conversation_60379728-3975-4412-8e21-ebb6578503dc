import React, { createContext, useContext, useState, useCallback } from 'react';

const MobileAlertContext = createContext();

export const useMobileAlert = () => {
  const context = useContext(MobileAlertContext);
  if (!context) {
    throw new Error('useMobileAlert must be used within a MobileAlertProvider');
  }
  return context;
};

export const MobileAlertProvider = ({ children }) => {
  const [alerts, setAlerts] = useState([]);

  const addAlert = useCallback((alert) => {
    const id = Date.now() + Math.random();
    const newAlert = {
      id,
      type: alert.type || 'info', // 'success', 'error', 'warning', 'info'
      title: alert.title,
      message: alert.message,
      duration: alert.duration || 4000,
      persistent: alert.persistent || false,
      position: alert.position || 'top', // 'top', 'bottom', 'center'
      ...alert,
    };

    setAlerts((prev) => {
      // Limit to 3 alerts max for mobile
      const newAlerts = [...prev, newAlert];
      return newAlerts.slice(-3);
    });

    // Auto-remove after duration if not persistent
    if (!newAlert.persistent && newAlert.duration > 0) {
      setTimeout(() => {
        removeAlert(id);
      }, newAlert.duration);
    }

    return id;
  }, []);

  const removeAlert = useCallback((id) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id));
  }, []);

  const clearAllAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  // Convenience methods with mobile-optimized defaults
  const success = useCallback(
    (message, options = {}) => {
      return addAlert({ 
        type: 'success', 
        message, 
        duration: 3000,
        ...options 
      });
    },
    [addAlert],
  );

  const error = useCallback(
    (message, options = {}) => {
      return addAlert({ 
        type: 'error', 
        message, 
        duration: 5000,
        ...options 
      });
    },
    [addAlert],
  );

  const warning = useCallback(
    (message, options = {}) => {
      return addAlert({ 
        type: 'warning', 
        message, 
        duration: 4000,
        ...options 
      });
    },
    [addAlert],
  );

  const info = useCallback(
    (message, options = {}) => {
      return addAlert({ 
        type: 'info', 
        message, 
        duration: 3500,
        ...options 
      });
    },
    [addAlert],
  );

  // Quick toast methods for common mobile scenarios
  const toast = useCallback(
    (message, type = 'info') => {
      return addAlert({
        type,
        message,
        duration: 2500,
        position: 'top'
      });
    },
    [addAlert],
  );

  const quickSuccess = useCallback(
    (message) => toast(message, 'success'),
    [toast],
  );

  const quickError = useCallback(
    (message) => toast(message, 'error'),
    [toast],
  );

  const quickInfo = useCallback(
    (message) => toast(message, 'info'),
    [toast],
  );

  // Persistent alert for important messages
  const persistent = useCallback(
    (message, type = 'warning', title = null) => {
      return addAlert({
        type,
        title,
        message,
        persistent: true,
        position: 'center'
      });
    },
    [addAlert],
  );

  const value = {
    alerts,
    addAlert,
    removeAlert,
    clearAllAlerts,
    success,
    error,
    warning,
    info,
    toast,
    quickSuccess,
    quickError,
    quickInfo,
    persistent,
  };

  return (
    <MobileAlertContext.Provider value={value}>
      {children}
    </MobileAlertContext.Provider>
  );
};

/**
 * Hook for mobile alert migration - similar to useAlertMigration but for mobile
 */
export const useMobileAlertMigration = () => {
  const { success, error, warning, info, quickSuccess, quickError } = useMobileAlert();

  const showError = (message, title = null) => {
    return error(message, { title });
  };

  const showSuccess = (message, title = null) => {
    return success(message, { title });
  };

  const showWarning = (message, title = null) => {
    return warning(message, { title });
  };

  const showInfo = (message, title = null) => {
    return info(message, { title });
  };

  // Quick methods for simple feedback
  const showQuickSuccess = (message) => {
    return quickSuccess(message);
  };

  const showQuickError = (message) => {
    return quickError(message);
  };

  return {
    showError,
    showSuccess,
    showWarning,
    showInfo,
    showQuickSuccess,
    showQuickError,
  };
};

export default MobileAlertContext;
