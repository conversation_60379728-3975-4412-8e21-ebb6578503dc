import { useNavigate } from "react-router-dom";
import {
  LayoutDashboard,
  Bot,
  BookText,
  Package,
  Users,
  MessageCircle,
  MessageSquare,
  Link as LinkIcon,
  BarChart2,
  <PERSON><PERSON>he<PERSON>,
  Clock,
} from "lucide-react";
import Icon from "./ui/icon";

export default function LockedDashboard({ reason, user }) {
  const navigate = useNavigate();
  const isSuspended = reason === "suspended";
  const isAdmin = () => {
    const plan = user?.profile?.plan || user?.plan;
    return plan === "admin" || user?.profile?.role === "admin";
  };

  // Sidebar sections copied from Dashboard.jsx
  const sidebarSections = [
    {
      id: "overview",
      label: "Overview",
      path: "/dashboard",
      icon: "LayoutDashboard",
      description: "Dashboard overview",
    },
    {
      id: "chatbot",
      label: "Chatbot Config",
      path: "/dashboard/chatbot",
      icon: "Bot",
      description: "AI configuration",
    },
    {
      id: "knowledge",
      label: "Knowledge Base",
      path: "/dashboard/knowledge",
      icon: "BookText",
      description: "Manage content",
    },
    {
      id: "products",
      label: "Products",
      path: "/dashboard/products",
      icon: "Package",
      description: "Product catalog",
    },
    {
      id: "contacts",
      label: "Contacts",
      path: "/dashboard/contacts",
      icon: "Users",
      description: "Customer management",
    },
    {
      id: "follow-ups",
      label: "Follow-ups",
      path: "/dashboard/follow-ups",
      icon: "Clock",
      description: "AI & follow-up settings",
    },
    {
      id: "whatsapp",
      label: "WhatsApp",
      path: "/dashboard/whatsapp",
      icon: "MessageCircle",
      description: "WhatsApp integration",
    },
    {
      id: "social-media",
      label: "Social Media",
      path: "/dashboard/social-media",
      icon: "MessageSquare",
      description: "Messenger & Instagram",
    },
    {
      id: "google",
      label: "Google Services",
      path: "/dashboard/google",
      icon: "LinkIcon",
      description: "Sheets & Calendar",
    },
    {
      id: "analytics",
      label: "Analytics",
      path: "/dashboard/analytics",
      icon: "BarChart2",
      description: "Reports & insights",
    },
  ];
  if (isAdmin()) {
    sidebarSections.push({
      id: "admin",
      label: "Admin Panel",
      path: "/dashboard/admin",
      icon: "ShieldCheck",
      description: "System management",
    });
  }

  // Plan badge logic copied from Dashboard.jsx
  const getPlanDisplayInfo = () => {
    if (isSuspended) {
      return {
        name: "Account Suspended",
        color: "bg-red-100 text-red-700",
        dotColor: "bg-red-500",
      };
    }
    const plan = user?.profile?.plan || user?.plan || "free";
    switch (plan) {
      case "free":
        return {
          name: "Kedai Free Plan",
          color: "bg-gray-100 text-gray-700",
          dotColor: "bg-gray-500",
        };
      case "kecil":
        return {
          name: "Kedai Kecil Plan",
          color: "bg-green-100 text-green-700",
          dotColor: "bg-green-500",
        };
      case "popular":
        return {
          name: "Kedai Popular Plan",
          color: "bg-blue-100 text-blue-700",
          dotColor: "bg-blue-500",
        };
      case "besar":
        return {
          name: "Kedai Besar Plan",
          color: "bg-purple-100 text-purple-700",
          dotColor: "bg-purple-500",
        };
      case "trial":
        return {
          name: "Kedai Trial Plan",
          color: "bg-yellow-100 text-yellow-700",
          dotColor: "bg-yellow-500",
        };
      case "admin":
        return {
          name: "Kedai Admin Plan",
          color: "bg-red-100 text-red-700",
          dotColor: "bg-red-500",
        };
      default:
        return {
          name: "Kedai Free Plan",
          color: "bg-gray-100 text-gray-700",
          dotColor: "bg-gray-500",
        };
    }
  };
  const planInfo = getPlanDisplayInfo();

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Sidebar - fixed like Dashboard */}
      <aside className="fixed left-0 top-20 w-72 h-[calc(100vh-80px)] bg-white shadow-2xl border-r border-gray-100 overflow-y-auto z-10">
        <div className="p-6">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Dashboard</h1>
            <p className="text-sm text-gray-500">
              {isSuspended ? "Account requires attention" : "Limited access mode"}
            </p>
          </div>
          {/* Plan Status Badge */}
          <div className="mb-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
            <div className="flex items-center gap-3 mb-2">
              <div className={`w-3 h-3 rounded-full ${planInfo.dotColor}`}></div>
              <span className="font-semibold text-gray-900">{planInfo.name}</span>
            </div>
            <div className="text-xs text-gray-600">
              <p>
                {isSuspended
                  ? "Contact support for assistance"
                  : `Welcome back, ${user?.username || user?.profile?.display_name || "User"}!`}
              </p>
              {isSuspended && (
                <p className="mt-1 text-red-600">Access temporarily restricted</p>
              )}
            </div>
          </div>
          {/* Navigation Links - all sections, only Overview enabled */}
          <nav className="space-y-1">
            <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3 py-2">Main Menu</div>
            {sidebarSections.map((section) => {
              const isOverview = section.id === "overview";
              return isOverview ? (
                <div
                  key={section.id}
                  className="group w-full flex items-center gap-3 px-3 py-3 rounded-lg bg-blue-50 text-blue-700 border border-blue-200 shadow-sm"
                >
                  <div className="w-5 h-5 text-blue-600">
                    <Icon name={section.icon} className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="font-medium text-sm">{section.label}</span>
                    <p className="text-xs mt-0.5 text-blue-600">{section.description}</p>
                  </div>
                  <div className="w-1 h-6 bg-blue-600 rounded-full"></div>
                </div>
              ) : (
                <div
                  key={section.id}
                  className="group w-full flex items-center gap-3 px-3 py-3 rounded-lg text-gray-400 cursor-not-allowed relative opacity-60"
                >
                  <div className="w-5 h-5 text-gray-300">
                    <Icon name={section.icon} className="w-5 h-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="font-medium text-sm">{section.label}</span>
                    <p className="text-xs mt-0.5">{section.description}</p>
                  </div>
                  <div className="w-4 h-4 text-gray-300">
                    <Icon name="Lock" className="w-5 h-5" />
                  </div>
                </div>
              );
            })}
          </nav>
        </div>
      </aside>
      {/* Main Content Area - With left margin to account for fixed sidebar */}
      <main className="ml-72">
        <div className="p-8">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Page Header */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome back, {user?.username || user?.profile?.display_name || "User"}!
              </h2>
              <p className="text-gray-600">
                {isSuspended
                  ? "Your account is currently suspended. Contact our support team for assistance in resolving this issue."
                  : "Upgrade your plan to access all dashboard features and unlock your chatbot's full potential with advanced AI configuration and unlimited knowledge base entries."}
              </p>
            </div>
            {/* Alert Banner */}
            <div
              className={`card ${
                isSuspended ? "bg-red-50 border-red-200" : "bg-amber-50 border-amber-200"
              }`}
            >
              <div className="flex items-start gap-4">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                    isSuspended ? "bg-red-100" : "bg-amber-100"
                  }`}
                >
                  <Icon
                    name={isSuspended ? "AlertTriangle" : "CircleX"}
                    className={`w-6 h-6 ${isSuspended ? "text-red-600" : "text-amber-600"}`}
                  />
                </div>
                <div className="flex-1">
                  <h3
                    className={`text-xl font-semibold mb-3 ${
                      isSuspended ? "text-red-900" : "text-amber-900"
                    }`}
                  >
                    {isSuspended ? "Account Suspended" : "Limited Access - Free Plan"}
                  </h3>
                  <p
                    className={`text-sm leading-relaxed ${
                      isSuspended ? "text-red-700" : "text-amber-700"
                    }`}
                  >
                    {isSuspended
                      ? "Your account has been suspended. Please contact our support team to resolve this issue and restore access to all dashboard features."
                      : "You are currently on the free plan with limited features. Upgrade to unlock the full potential of your chatbot dashboard and access all premium functionality."}
                  </p>
                </div>
              </div>
            </div>
            {/* Plan Status Cards */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Current Plan Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="card">
                  <div className="text-center">
                    <div
                      className={`w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center ${
                        isSuspended ? "bg-red-100" : "bg-blue-100"
                      }`}
                    >
                      {isSuspended ? (
                        <Icon name="X" className="w-6 h-6 text-red-600" />
                      ) : (
                        <Icon name="User" className="w-6 h-6 text-blue-600" />
                      )}
                    </div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Current Plan</p>
                    <p
                      className={`text-2xl font-bold mb-2 ${
                        isSuspended ? "text-red-600" : "text-blue-600"
                      }`}
                    >
                      {isSuspended ? "Suspended" : planInfo.name.replace("Kedai ", "")}
                    </p>
                    <p className="text-xs text-gray-500">
                      {isSuspended ? "Account suspended" : "Limited features"}
                    </p>
                  </div>
                </div>
                <div className="card opacity-60">
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                      <Icon name="X" className="w-6 h-6 text-gray-400" />
                    </div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Messages</p>
                    <p className="text-2xl font-bold text-gray-400 mb-2">Locked</p>
                    <p className="text-xs text-gray-500">Upgrade to view</p>
                  </div>
                </div>
                <div className="card opacity-60">
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                      <Icon name="X" className="w-6 h-6 text-gray-400" />
                    </div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Analytics</p>
                    <p className="text-2xl font-bold text-gray-400 mb-2">Locked</p>
                    <p className="text-xs text-gray-500">Upgrade to view</p>
                  </div>
                </div>
              </div>
            </div>
            {/* Feature Comparison - Only for Free Users */}
            {!isSuspended && (
              <div className="card">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Unlock Premium Features</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                  {[
                    {
                      feature: "Advanced Chatbot Configuration",
                      icon: <Bot className="w-6 h-6 text-blue-600" />,
                      description: "Customize AI behavior and responses",
                    },
                    {
                      feature: "Unlimited Knowledge Base",
                      icon: <BookText className="w-6 h-6 text-blue-600" />,
                      description: "Store unlimited content and data",
                    },
                    {
                      feature: "Product Management",
                      icon: <Package className="w-6 h-6 text-blue-600" />,
                      description: "Manage your product catalog",
                    },
                    {
                      feature: "Contact Management",
                      icon: <Users className="w-6 h-6 text-blue-600" />,
                      description: "Track and organize customers",
                    },
                    {
                      feature: "WhatsApp Integration",
                      icon: <MessageCircle className="w-6 h-6 text-blue-500" />,
                      description: "Connect with WhatsApp Business",
                    },
                    {
                      feature: "Advanced Analytics",
                      icon: <BarChart2 className="w-6 h-6 text-blue-600" />,
                      description: "Detailed reports and insights",
                    },
                    {
                      feature: "Social Media Integration",
                      icon: <MessageSquare className="w-6 h-6 text-blue-500" />,
                      description: "Messenger & Instagram integration",
                    },
                    {
                      feature: "Google Sheets & Calendar",
                      icon: <LinkIcon className="w-6 h-6 text-blue-600" />,
                      description: "Sync with Google Sheets and Calendar",
                    },
                    // Only show Admin Panel if user is admin
                    ...(isAdmin()
                      ? [
                          {
                            feature: "Admin Panel",
                            icon: <ShieldCheck className="w-6 h-6 text-red-600" />,
                            description: "System management and logs",
                          },
                        ]
                      : []),
                  ].map((item, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100"
                    >
                      <div className="w-10 h-10 rounded-lg bg-white flex items-center justify-center flex-shrink-0 shadow-sm">
                        {item.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{item.feature}</h4>
                        <p className="text-sm text-gray-600">{item.description}</p>
                      </div>
                      <span className="ml-auto text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium flex-shrink-0">Premium</span>
                    </div>
                  ))}
                </div>
                <div className="text-center">
                  <button
                    onClick={() => navigate("/pricing")}
                    className="btn-primary"
                  >
                    Unlock All Features - View Pricing
                  </button>
                </div>
              </div>
            )}
            {/* Contact Support for Suspended Users */}
            {isSuspended && (
              <div className="card bg-red-50 border-red-200">
                <h3 className="text-xl font-semibold text-red-900 mb-4">Account Suspended</h3>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Your account has been temporarily suspended. This may be due to:
                </p>
                <ul className="list-disc pl-6 text-gray-600 mb-8 space-y-2">
                  <li>Violation of terms of service</li>
                  <li>Payment or billing issues</li>
                  <li>Suspicious or unusual activity</li>
                  <li>Administrative security review</li>
                </ul>
                <div className="bg-red-50 border border-red-200 p-6 rounded-lg">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Icon name="X" className="w-5 h-5 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-red-900 mb-2">Need Help?</h4>
                      <p className="text-red-700 text-sm leading-relaxed">
                        Contact our support team via WhatsApp to resolve this issue and restore your account access. We're here to help!
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
