-- Enable the pgvector extension for vector operations
CREATE EXTENSION IF NOT EXISTS vector;

-- Create profiles table for storing user profile information
CREATE TABLE IF NOT EXISTS profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT UNIQUE NOT NULL,
    display_name TEXT,
    phone TEXT,
    email TEXT,
    plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'trial', 'kecil', 'popular', 'besar', 'admin')),
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
    subscription_start_date TIMESTAMP WITH TIME ZONE,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    is_suspended BOOLEAN DEFAULT false,
    suspended_at TIMESTAMP WITH TIME ZONE,
    suspended_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create knowledge_base table for storing vector embeddings
CREATE TABLE IF NOT EXISTS knowledge_base (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI text-embedding-3-small uses 1536 dimensions
    has_image BOOLEAN DEFAULT false,
    image_filename TEXT,
    image_url TEXT,
    image_mime_type TEXT,
    image_file_size INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chat_history table for storing conversation history
CREATE TABLE IF NOT EXISTS chat_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    media_filename VARCHAR(255),
    media_url TEXT,
    media_type VARCHAR(50),
    mime_type VARCHAR(100),
    file_size INTEGER
);

-- Create chatbot_settings table for storing chatbot behavior configuration
CREATE TABLE IF NOT EXISTS chatbot_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT UNIQUE NOT NULL,
    system_prompt TEXT DEFAULT 'You are a friendly customer service assistant. Be helpful, warm, and natural in your responses.
PERSONALITY: Casual and friendly, like talking to a helpful friend. Use Malaysian expressions naturally.
LANGUAGE: Respond in the same language the customer uses (English, Malay, Chinese, or mixed).
KNOWLEDGE: Use only the information provided in your knowledge base. If you don''t know something, politely say you''re not sure.
ORDERS: You cannot process orders or payments. Direct customers to contact the business directly for purchases.
STYLE: Give complete, helpful information. Be natural and conversational, not robotic.',
    order_system_prompt TEXT DEFAULT 'You are a friendly sales assistant who helps customers with orders and questions.
PERSONALITY: Warm, helpful, and efficient. Use natural Malaysian expressions.
LANGUAGE: Respond in the customer''s preferred language (English, Malay, Chinese, or mixed).
ORDERS: Help customers by showing products, collecting order details step by step, and confirming everything before finalizing.
QUESTIONS: Answer using your knowledge base. Be complete and helpful in your responses.
COMPLETION: When an order is ready, summarize everything clearly and mark it as "ORDER_COMPLETE:" followed by the order summary.',
    model TEXT DEFAULT 'gpt-4o-mini' CHECK (model IN ('gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo')),
    temperature DECIMAL(2,1) DEFAULT 0.5 CHECK (temperature >= 0.0 AND temperature <= 2.0),
    max_tokens INTEGER DEFAULT 200 CHECK (max_tokens > 0 AND max_tokens <= 4000),
    chat_history_limit INTEGER DEFAULT 10 CHECK (chat_history_limit > 0 AND chat_history_limit <= 50),
    similarity_threshold DECIMAL(3,2) DEFAULT 0.30 CHECK (similarity_threshold >= 0.0 AND similarity_threshold <= 1.0),
    match_count INTEGER DEFAULT 5 CHECK (match_count > 0 AND match_count <= 20),
    order_processing_enabled BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contacts table for managing WhatsApp contacts
CREATE TABLE IF NOT EXISTS contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    name TEXT,
    first_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_messages INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    tags TEXT[], -- Array of tags for contact categorization
    follow_up_enabled BOOLEAN DEFAULT true, -- Toggle for automated follow-ups
    follow_up_count INTEGER DEFAULT 0, -- Number of follow-ups sent
    last_follow_up_at TIMESTAMP WITH TIME ZONE,
    next_follow_up_at TIMESTAMP WITH TIME ZONE,
    follow_up_stopped_reason TEXT, -- 'customer_replied', 'max_attempts', 'manual_stop', '24h_expired'
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(auth_id, phone_number)
);



-- Create follow_up_schedules table for managing automated follow-ups
CREATE TABLE IF NOT EXISTS follow_up_schedules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    auth_id TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    follow_up_type TEXT NOT NULL CHECK (follow_up_type IN ('booking_reminder', 'order_follow_up', 'engagement_check')),
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'executed', 'cancelled', 'failed')),
    message_template TEXT, -- Template for the follow-up message
    message_sent TEXT, -- Actual message sent (after template processing)
    whatsapp_message_id TEXT, -- WhatsApp API message ID
    failure_reason TEXT, -- Reason if follow-up failed
    attempt_number INTEGER DEFAULT 1, -- Which attempt this is (1st, 2nd, etc.)
    max_attempts INTEGER DEFAULT 1, -- Maximum attempts allowed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create follow_up_rules table for configuring follow-up behavior
CREATE TABLE IF NOT EXISTS follow_up_rules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    rule_name TEXT NOT NULL,
    intent_category TEXT NOT NULL, -- Which AI intent category this rule applies to
    is_active BOOLEAN DEFAULT true,
    delay_hours INTEGER NOT NULL DEFAULT 24, -- Hours to wait before follow-up
    max_follow_ups INTEGER DEFAULT 1, -- Maximum number of follow-ups
    message_template TEXT NOT NULL, -- Template for follow-up messages
    conditions JSONB, -- Additional conditions (e.g., engagement_score > 0.5)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(auth_id, intent_category)
);

-- Create message_statistics table for tracking detailed message stats
CREATE TABLE IF NOT EXISTS message_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    message_type TEXT NOT NULL CHECK (message_type IN ('incoming', 'outgoing')),
    model_used TEXT,
    prompt_tokens INTEGER DEFAULT 0,
    completion_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    response_time_ms INTEGER,
    knowledge_sections_found INTEGER DEFAULT 0,
    similarity_score DECIMAL(4,3),
    message_length INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create daily_statistics table for aggregated daily stats
CREATE TABLE IF NOT EXISTS daily_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    date DATE NOT NULL,
    total_messages INTEGER DEFAULT 0,
    incoming_messages INTEGER DEFAULT 0,
    outgoing_messages INTEGER DEFAULT 0,
    unique_contacts INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    prompt_tokens INTEGER DEFAULT 0,
    completion_tokens INTEGER DEFAULT 0,
    avg_response_time_ms INTEGER DEFAULT 0,
    knowledge_queries INTEGER DEFAULT 0,
    knowledge_hits INTEGER DEFAULT 0, -- Queries that found relevant knowledge
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(auth_id, date)
);

-- Create monthly_statistics table for aggregated monthly stats
CREATE TABLE IF NOT EXISTS monthly_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    total_messages INTEGER DEFAULT 0,
    incoming_messages INTEGER DEFAULT 0,
    outgoing_messages INTEGER DEFAULT 0,
    unique_contacts INTEGER DEFAULT 0,
    new_contacts INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    prompt_tokens INTEGER DEFAULT 0,
    completion_tokens INTEGER DEFAULT 0,
    avg_response_time_ms INTEGER DEFAULT 0,
    knowledge_queries INTEGER DEFAULT 0,
    knowledge_hits INTEGER DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    completed_orders INTEGER DEFAULT 0,
    cancelled_orders INTEGER DEFAULT 0,
    pending_orders INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(auth_id, year, month)
);

-- Create social_media_integrations table for storing all social media platform configurations
CREATE TABLE IF NOT EXISTS social_media_integrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('whatsapp', 'messenger', 'instagram')),
    is_enabled BOOLEAN DEFAULT true,

    -- OAuth fields
    oauth_connected BOOLEAN DEFAULT false,
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP WITH TIME ZONE,
    token_type TEXT DEFAULT 'Bearer',
    granted_scopes TEXT[], -- Array of granted permissions

    -- WhatsApp specific fields
    whatsapp_business_account_id TEXT,
    whatsapp_phone_number_id TEXT,
    system_access_token TEXT, -- For backward compatibility

    -- Messenger specific fields
    messenger_page_id TEXT,
    messenger_page_name TEXT,
    messenger_app_id TEXT,

    -- Instagram specific fields
    instagram_account_id TEXT,
    instagram_username TEXT,

    -- Meta user info
    meta_user_id TEXT,
    meta_user_name TEXT,

    -- Common fields
    webhook_verified BOOLEAN DEFAULT false,
    last_webhook_challenge TEXT,
    connection_status TEXT DEFAULT 'disconnected' CHECK (connection_status IN ('connected', 'disconnected', 'error', 'expired')),
    last_error_message TEXT,
    last_sync_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(auth_id, platform)
);

-- Create oauth_states table for storing OAuth state parameters (security)
CREATE TABLE IF NOT EXISTS oauth_states (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    state_token TEXT UNIQUE NOT NULL,
    auth_id TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('whatsapp', 'messenger', 'instagram')),
    redirect_uri TEXT,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '10 minutes'),
    used BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create google_integrations table for storing Google service configurations
CREATE TABLE IF NOT EXISTS google_integrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    service_type TEXT NOT NULL CHECK (service_type IN ('sheets', 'calendar')),
    is_enabled BOOLEAN DEFAULT true,

    -- Google Sheets specific fields
    spreadsheet_id TEXT,
    spreadsheet_url TEXT,
    worksheet_name TEXT DEFAULT 'Data',
    spreadsheet_columns TEXT[] DEFAULT ARRAY['OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'],

    -- Google Calendar specific fields (for future use)
    calendar_id TEXT,
    calendar_name TEXT,

    -- Common Google fields
    service_account_email TEXT,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT DEFAULT 'active' CHECK (sync_status IN ('active', 'error', 'disabled')),
    error_message TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(auth_id, service_type)
);

-- Keep whatsapp_customers table for backward compatibility but mark as deprecated
-- This will be migrated to social_media_integrations table
CREATE TABLE IF NOT EXISTS whatsapp_customers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT UNIQUE NOT NULL,
    whatsapp_business_account_id TEXT NOT NULL,
    whatsapp_phone_number_id TEXT NOT NULL,
    system_access_token TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create product_catalog table for storing products (kept for knowledge base reference)
CREATE TABLE IF NOT EXISTS product_catalog (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    variations JSONB DEFAULT '[]', -- Array of variations like colors, sizes, etc.
    category TEXT,
    stock_quantity INTEGER DEFAULT 0,
    is_service BOOLEAN DEFAULT false, -- True for services that don't have stock
    is_active BOOLEAN DEFAULT true,
    image_url TEXT, -- URL to the product image
    image_filename TEXT, -- Original filename of the uploaded image
    image_mime_type TEXT, -- MIME type of the image (image/jpeg, image/png, etc.)
    image_file_size INTEGER, -- File size in bytes
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    duration_days INTEGER NOT NULL DEFAULT 30,
    monthly_message_limit INTEGER NOT NULL DEFAULT 1000,
    total_contact_limit INTEGER NOT NULL DEFAULT 1000,
    knowledge_base_limit INTEGER NOT NULL DEFAULT 10,
    product_catalog_limit INTEGER NOT NULL DEFAULT 50,
    features JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default plans based on existing user profile plans
INSERT INTO subscription_plans (name, description, price, duration_days, monthly_message_limit, total_contact_limit, knowledge_base_limit, product_catalog_limit, features) VALUES
('free', 'Basic free plan with limited features', 0.00, 30, 0, 0, 0, 0, '{"whatsapp_integration": false, "analytics": false, "priority_support": false}'),
('trial', 'Trial plan with full features for testing', 0.00, 7, 500, 200, 20, 50, '{"whatsapp_integration": true, "analytics": true, "priority_support": false, "trial": true}'),
('kecil', 'Small business plan', 29.00, 30, 5000, 1000, 50, 100, '{"whatsapp_integration": true, "analytics": true, "priority_support": false}'),
('popular', 'Most popular business plan', 59.00, 30, 10000, 5000, 200, 500, '{"whatsapp_integration": true, "analytics": true, "priority_support": true}'),
('besar', 'Large business plan', 119.00, 30, 50000, 10000, 1000, 2000, '{"whatsapp_integration": true, "analytics": true, "priority_support": true}'),
('admin', 'Admin plan with unlimited features', 0.00, 365, -1, -1, -1, -1, '{"whatsapp_integration": true, "analytics": true, "priority_support": true, "admin_access": true}')
ON CONFLICT DO NOTHING;

-- Create user subscriptions table for tracking subscription history and admin actions
CREATE TABLE IF NOT EXISTS user_subscription_history (
    id SERIAL PRIMARY KEY,
    auth_id TEXT NOT NULL,
    plan_name TEXT NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    action_type TEXT DEFAULT 'subscription', -- 'subscription', 'trial', 'admin_extend', 'expired'
    admin_id TEXT, -- If admin action
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create monthly usage tracking table
CREATE TABLE IF NOT EXISTS monthly_usage (
    id SERIAL PRIMARY KEY,
    auth_id VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    messages_sent INTEGER DEFAULT 0,
    messages_received INTEGER DEFAULT 0,
    total_contacts INTEGER DEFAULT 0,
    new_contacts INTEGER DEFAULT 0,
    knowledge_base_queries INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(auth_id, year, month)
);

-- Create quota violations log table
CREATE TABLE IF NOT EXISTS quota_violations (
    id SERIAL PRIMARY KEY,
    auth_id VARCHAR(100) NOT NULL,
    violation_type VARCHAR(100) NOT NULL, -- 'message_limit', 'contact_limit', 'subscription_expired'
    attempted_action VARCHAR(255),
    current_usage INTEGER,
    limit_value INTEGER,
    plan_name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create admin actions log table
CREATE TABLE IF NOT EXISTS admin_actions (
    id SERIAL PRIMARY KEY,
    admin_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(100) NOT NULL, -- 'plan_create', 'plan_update', 'subscription_update', 'quota_adjustment'
    target_auth_id VARCHAR(255), -- For user-specific actions
    action_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table for storing customer orders with flexible columns
CREATE TABLE IF NOT EXISTS orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    order_status TEXT DEFAULT 'pending' CHECK (order_status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'placed')),
    customer_info JSONB DEFAULT '{}', -- Flexible storage for customer information based on spreadsheet columns
    order_summary TEXT, -- Complete order summary from OpenAI
    spreadsheet_row_number INTEGER, -- Track which row in Google Sheets this order corresponds to
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create uploaded_media table for tracking uploaded files
CREATE TABLE IF NOT EXISTS uploaded_media (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    original_name TEXT,
    media_type TEXT,
    mime_type TEXT,
    file_size INTEGER,
    file_url TEXT,
    storage_path TEXT,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50) DEFAULT 'user_upload',
    phone_number VARCHAR(20),
    whatsapp_media_id VARCHAR(255)
);

-- Create human_takeover_sessions table for tracking when humans are actively messaging customers
CREATE TABLE IF NOT EXISTS human_takeover_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_id TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    timeout_minutes INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin_impersonation_sessions table for tracking admin user impersonation
CREATE TABLE IF NOT EXISTS admin_impersonation_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id TEXT NOT NULL,
    target_auth_id TEXT NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_auth_id ON profiles(auth_id);
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_end ON profiles(subscription_end_date);
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_start ON profiles(subscription_start_date);
CREATE INDEX IF NOT EXISTS idx_profiles_plan ON profiles(plan);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_auth_id ON knowledge_base(auth_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_embedding ON knowledge_base USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_has_image ON knowledge_base(auth_id, has_image) WHERE has_image = true;
CREATE INDEX IF NOT EXISTS idx_chat_history_auth_phone ON chat_history(auth_id, phone_number);
CREATE INDEX IF NOT EXISTS idx_chat_history_created_at ON chat_history(created_at);
CREATE INDEX IF NOT EXISTS idx_chatbot_settings_auth_id ON chatbot_settings(auth_id);
CREATE INDEX IF NOT EXISTS idx_contacts_auth_id ON contacts(auth_id);
CREATE INDEX IF NOT EXISTS idx_contacts_phone ON contacts(phone_number);
CREATE INDEX IF NOT EXISTS idx_contacts_auth_phone ON contacts(auth_id, phone_number);
CREATE INDEX IF NOT EXISTS idx_message_statistics_auth_id ON message_statistics(auth_id);
CREATE INDEX IF NOT EXISTS idx_message_statistics_phone ON message_statistics(phone_number);
CREATE INDEX IF NOT EXISTS idx_message_statistics_created_at ON message_statistics(created_at);
CREATE INDEX IF NOT EXISTS idx_message_statistics_auth_created ON message_statistics(auth_id, created_at);
CREATE INDEX IF NOT EXISTS idx_daily_statistics_auth_id ON daily_statistics(auth_id);
CREATE INDEX IF NOT EXISTS idx_daily_statistics_date ON daily_statistics(date);
CREATE INDEX IF NOT EXISTS idx_daily_statistics_auth_date ON daily_statistics(auth_id, date);
CREATE INDEX IF NOT EXISTS idx_monthly_statistics_auth_id ON monthly_statistics(auth_id);
CREATE INDEX IF NOT EXISTS idx_monthly_statistics_year_month ON monthly_statistics(year, month);
CREATE INDEX IF NOT EXISTS idx_monthly_statistics_auth_year_month ON monthly_statistics(auth_id, year, month);
-- Indexes for social_media_integrations table
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_auth_id ON social_media_integrations(auth_id);
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_platform ON social_media_integrations(platform);
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_auth_platform ON social_media_integrations(auth_id, platform);
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_enabled ON social_media_integrations(is_enabled);
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_oauth ON social_media_integrations(oauth_connected);
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_status ON social_media_integrations(connection_status);
CREATE INDEX IF NOT EXISTS idx_social_media_integrations_meta_user ON social_media_integrations(meta_user_id);

-- Indexes for oauth_states table
CREATE INDEX IF NOT EXISTS idx_oauth_states_state_token ON oauth_states(state_token);
CREATE INDEX IF NOT EXISTS idx_oauth_states_auth_id ON oauth_states(auth_id);
CREATE INDEX IF NOT EXISTS idx_oauth_states_expires_at ON oauth_states(expires_at);
CREATE INDEX IF NOT EXISTS idx_oauth_states_used ON oauth_states(used);

-- Indexes for google_integrations table
CREATE INDEX IF NOT EXISTS idx_google_integrations_auth_id ON google_integrations(auth_id);
CREATE INDEX IF NOT EXISTS idx_google_integrations_service_type ON google_integrations(service_type);
CREATE INDEX IF NOT EXISTS idx_google_integrations_auth_service ON google_integrations(auth_id, service_type);
CREATE INDEX IF NOT EXISTS idx_google_integrations_enabled ON google_integrations(is_enabled);

-- Keep existing whatsapp_customers indexes for backward compatibility
CREATE INDEX IF NOT EXISTS idx_whatsapp_customers_auth_id ON whatsapp_customers(auth_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_customers_phone_number_id ON whatsapp_customers(whatsapp_phone_number_id);
CREATE INDEX IF NOT EXISTS idx_product_catalog_auth_id ON product_catalog(auth_id);
CREATE INDEX IF NOT EXISTS idx_product_catalog_category ON product_catalog(category);
CREATE INDEX IF NOT EXISTS idx_product_catalog_active ON product_catalog(is_active);
CREATE INDEX IF NOT EXISTS idx_product_catalog_is_service ON product_catalog(is_service);
CREATE INDEX IF NOT EXISTS idx_monthly_usage_auth_id ON monthly_usage(auth_id);
CREATE INDEX IF NOT EXISTS idx_monthly_usage_date ON monthly_usage(year, month);
CREATE INDEX IF NOT EXISTS idx_quota_violations_auth_id ON quota_violations(auth_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_id ON admin_actions(admin_id);
CREATE INDEX IF NOT EXISTS idx_orders_auth_id ON orders(auth_id);
CREATE INDEX IF NOT EXISTS idx_orders_phone_number ON orders(phone_number);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_auth_phone ON orders(auth_id, phone_number);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_history_media ON chat_history(auth_id, phone_number, media_filename) WHERE media_filename IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_uploaded_media_auth_id ON uploaded_media(auth_id);
CREATE INDEX IF NOT EXISTS idx_uploaded_media_filename ON uploaded_media(filename);
CREATE INDEX IF NOT EXISTS idx_uploaded_media_whatsapp ON uploaded_media(auth_id, phone_number, source) WHERE source = 'whatsapp_incoming';
CREATE INDEX IF NOT EXISTS idx_uploaded_media_whatsapp_id ON uploaded_media(whatsapp_media_id) WHERE whatsapp_media_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_human_takeover_sessions_auth_id ON human_takeover_sessions(auth_id);
CREATE INDEX IF NOT EXISTS idx_human_takeover_sessions_phone ON human_takeover_sessions(phone_number);
CREATE INDEX IF NOT EXISTS idx_human_takeover_sessions_active ON human_takeover_sessions(auth_id, phone_number, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_human_takeover_sessions_activity ON human_takeover_sessions(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_admin_impersonation_sessions_admin_id ON admin_impersonation_sessions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_impersonation_sessions_target_auth_id ON admin_impersonation_sessions(target_auth_id);
CREATE INDEX IF NOT EXISTS idx_admin_impersonation_sessions_active ON admin_impersonation_sessions(admin_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_admin_impersonation_sessions_started_at ON admin_impersonation_sessions(started_at);

-- Add comments to document subscription date columns
COMMENT ON COLUMN profiles.subscription_start_date IS 'Start date of current subscription (for all plan types)';
COMMENT ON COLUMN profiles.subscription_end_date IS 'End date of current subscription (NULL for unlimited plans like admin/free)';

-- Create function for similarity search in knowledge base
CREATE OR REPLACE FUNCTION match_knowledge_sections(
    query_embedding vector(1536),
    match_threshold float,
    match_count int,
    p_auth_id text
)
RETURNS TABLE (
    id UUID,
    auth_id TEXT,
    title TEXT,
    content TEXT,
    similarity float,
    has_image BOOLEAN,
    image_filename TEXT,
    image_url TEXT,
    image_mime_type TEXT,
    image_file_size INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        kb.id,
        kb.auth_id,
        kb.title,
        kb.content,
        1 - (kb.embedding <=> query_embedding) as similarity,
        kb.has_image,
        kb.image_filename,
        kb.image_url,
        kb.image_mime_type,
        kb.image_file_size
    FROM knowledge_base kb
    WHERE kb.auth_id = p_auth_id
        AND 1 - (kb.embedding <=> query_embedding) > match_threshold
    ORDER BY kb.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create function to get recent chat history
CREATE OR REPLACE FUNCTION get_recent_chat_history(
    p_auth_id text,
    p_phone_number text,
    p_limit int DEFAULT 5
)
RETURNS TABLE (
    id UUID,
    role TEXT,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        ch.id,
        ch.role,
        ch.content,
        ch.created_at
    FROM chat_history ch
    WHERE ch.auth_id = p_auth_id 
        AND ch.phone_number = p_phone_number
    ORDER BY ch.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Create function to clean old chat history
CREATE OR REPLACE FUNCTION clean_old_chat_history()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    DELETE FROM chat_history 
    WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$;

-- Create function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_base_updated_at 
    BEFORE UPDATE ON knowledge_base 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chatbot_settings_updated_at 
    BEFORE UPDATE ON chatbot_settings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contacts_updated_at 
    BEFORE UPDATE ON contacts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_statistics_updated_at 
    BEFORE UPDATE ON daily_statistics 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monthly_statistics_updated_at 
    BEFORE UPDATE ON monthly_statistics 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Triggers for new tables
CREATE TRIGGER update_social_media_integrations_updated_at
    BEFORE UPDATE ON social_media_integrations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired OAuth states
CREATE OR REPLACE FUNCTION cleanup_expired_oauth_states()
RETURNS void AS $$
BEGIN
    DELETE FROM oauth_states
    WHERE expires_at < NOW() OR used = true;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up expired OAuth states (optional)
-- This would typically be handled by a cron job or background task

CREATE TRIGGER update_google_integrations_updated_at
    BEFORE UPDATE ON google_integrations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Keep existing whatsapp_customers trigger for backward compatibility
CREATE TRIGGER update_whatsapp_customers_updated_at
    BEFORE UPDATE ON whatsapp_customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_catalog_updated_at 
    BEFORE UPDATE ON product_catalog 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_plans_updated_at BEFORE UPDATE ON subscription_plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monthly_usage_updated_at BEFORE UPDATE ON monthly_usage FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON orders 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_human_takeover_sessions_updated_at 
    BEFORE UPDATE ON human_takeover_sessions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_impersonation_sessions_updated_at 
    BEFORE UPDATE ON admin_impersonation_sessions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to update daily statistics
CREATE OR REPLACE FUNCTION update_daily_statistics(
    p_auth_id text,
    p_date date DEFAULT CURRENT_DATE
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO daily_statistics (
        auth_id, date, total_messages, incoming_messages, outgoing_messages,
        unique_contacts, total_tokens, prompt_tokens, completion_tokens,
        avg_response_time_ms, knowledge_queries, knowledge_hits
    )
    SELECT 
        p_auth_id,
        p_date,
        COUNT(*) as total_messages,
        COUNT(*) FILTER (WHERE message_type = 'incoming') as incoming_messages,
        COUNT(*) FILTER (WHERE message_type = 'outgoing') as outgoing_messages,
        COUNT(DISTINCT phone_number) as unique_contacts,
        COALESCE(SUM(total_tokens), 0) as total_tokens,
        COALESCE(SUM(prompt_tokens), 0) as prompt_tokens,
        COALESCE(SUM(completion_tokens), 0) as completion_tokens,
        COALESCE(AVG(response_time_ms), 0)::INTEGER as avg_response_time_ms,
        COUNT(*) FILTER (WHERE knowledge_sections_found > 0) as knowledge_queries,
        COUNT(*) FILTER (WHERE knowledge_sections_found > 0 AND similarity_score > 0.2) as knowledge_hits
    FROM message_statistics 
    WHERE auth_id = p_auth_id 
        AND DATE(created_at) = p_date
    ON CONFLICT (auth_id, date) 
    DO UPDATE SET
        total_messages = EXCLUDED.total_messages,
        incoming_messages = EXCLUDED.incoming_messages,
        outgoing_messages = EXCLUDED.outgoing_messages,
        unique_contacts = EXCLUDED.unique_contacts,
        total_tokens = EXCLUDED.total_tokens,
        prompt_tokens = EXCLUDED.prompt_tokens,
        completion_tokens = EXCLUDED.completion_tokens,
        avg_response_time_ms = EXCLUDED.avg_response_time_ms,
        knowledge_queries = EXCLUDED.knowledge_queries,
        knowledge_hits = EXCLUDED.knowledge_hits,
        updated_at = NOW();
END;
$$;

-- Create function to update monthly statistics
CREATE OR REPLACE FUNCTION update_monthly_statistics(
    p_auth_id text,
    p_year integer DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
    p_month integer DEFAULT EXTRACT(MONTH FROM CURRENT_DATE)
)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    msg_stats RECORD;
    contact_stats RECORD;
    order_stats RECORD;
BEGIN
    -- Get message statistics
    SELECT 
        COUNT(*) as total_messages,
        COUNT(*) FILTER (WHERE message_type = 'incoming') as incoming_messages,
        COUNT(*) FILTER (WHERE message_type = 'outgoing') as outgoing_messages,
        COUNT(DISTINCT phone_number) as unique_contacts,
        COALESCE(SUM(total_tokens), 0) as total_tokens,
        COALESCE(SUM(prompt_tokens), 0) as prompt_tokens,
        COALESCE(SUM(completion_tokens), 0) as completion_tokens,
        COALESCE(AVG(response_time_ms), 0)::INTEGER as avg_response_time_ms,
        COUNT(*) FILTER (WHERE knowledge_sections_found > 0) as knowledge_queries,
        COUNT(*) FILTER (WHERE knowledge_sections_found > 0 AND similarity_score > 0.2) as knowledge_hits
    INTO msg_stats
    FROM message_statistics 
    WHERE auth_id = p_auth_id 
        AND EXTRACT(YEAR FROM created_at) = p_year
        AND EXTRACT(MONTH FROM created_at) = p_month;

    -- Get contact statistics
    SELECT COUNT(*) as new_contacts
    INTO contact_stats
    FROM contacts 
    WHERE auth_id = p_auth_id 
        AND DATE_TRUNC('month', created_at) = DATE_TRUNC('month', MAKE_DATE(p_year, p_month, 1));

    -- Get order statistics
    SELECT 
        COUNT(*) as total_orders,
        COUNT(*) FILTER (WHERE order_status IN ('delivered', 'shipped')) as completed_orders,
        COUNT(*) FILTER (WHERE order_status = 'cancelled') as cancelled_orders,
        COUNT(*) FILTER (WHERE order_status IN ('pending', 'confirmed', 'processing')) as pending_orders
    INTO order_stats
    FROM orders 
    WHERE auth_id = p_auth_id 
        AND EXTRACT(YEAR FROM created_at) = p_year
        AND EXTRACT(MONTH FROM created_at) = p_month;

    INSERT INTO monthly_statistics (
        auth_id, year, month, total_messages, incoming_messages, outgoing_messages,
        unique_contacts, new_contacts, total_tokens, prompt_tokens, completion_tokens,
        avg_response_time_ms, knowledge_queries, knowledge_hits, total_orders, completed_orders, cancelled_orders, pending_orders
    )
    VALUES (
        p_auth_id,
        p_year,
        p_month,
        COALESCE(msg_stats.total_messages, 0),
        COALESCE(msg_stats.incoming_messages, 0),
        COALESCE(msg_stats.outgoing_messages, 0),
        COALESCE(msg_stats.unique_contacts, 0),
        COALESCE(contact_stats.new_contacts, 0),
        COALESCE(msg_stats.total_tokens, 0),
        COALESCE(msg_stats.prompt_tokens, 0),
        COALESCE(msg_stats.completion_tokens, 0),
        COALESCE(msg_stats.avg_response_time_ms, 0),
        COALESCE(msg_stats.knowledge_queries, 0),
        COALESCE(msg_stats.knowledge_hits, 0),
        COALESCE(order_stats.total_orders, 0),
        COALESCE(order_stats.completed_orders, 0),
        COALESCE(order_stats.cancelled_orders, 0),
        COALESCE(order_stats.pending_orders, 0)
    )
    ON CONFLICT (auth_id, year, month) 
    DO UPDATE SET
        total_messages = EXCLUDED.total_messages,
        incoming_messages = EXCLUDED.incoming_messages,
        outgoing_messages = EXCLUDED.outgoing_messages,
        unique_contacts = EXCLUDED.unique_contacts,
        new_contacts = EXCLUDED.new_contacts,
        total_tokens = EXCLUDED.total_tokens,
        prompt_tokens = EXCLUDED.prompt_tokens,
        completion_tokens = EXCLUDED.completion_tokens,
        avg_response_time_ms = EXCLUDED.avg_response_time_ms,
        knowledge_queries = EXCLUDED.knowledge_queries,
        knowledge_hits = EXCLUDED.knowledge_hits,
        total_orders = EXCLUDED.total_orders,
        completed_orders = EXCLUDED.completed_orders,
        cancelled_orders = EXCLUDED.cancelled_orders,
        pending_orders = EXCLUDED.pending_orders,
        updated_at = NOW();
END;
$$;

-- Create function to upsert contact information
CREATE OR REPLACE FUNCTION upsert_contact(
    p_auth_id text,
    p_phone_number text,
    p_name text DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    contact_id UUID;
    is_new_contact BOOLEAN := false;
BEGIN
    -- Try to insert new contact
    INSERT INTO contacts (auth_id, phone_number, name, last_message_at, total_messages, follow_up_enabled)
    VALUES (p_auth_id, p_phone_number, p_name, NOW(), 1, true)
    ON CONFLICT (auth_id, phone_number)
    DO UPDATE SET
        name = COALESCE(EXCLUDED.name, contacts.name),
        last_message_at = NOW(),
        total_messages = contacts.total_messages + 1,
        updated_at = NOW()
    RETURNING id, (total_messages = 1) INTO contact_id, is_new_contact;

    -- If this is a new contact, log it
    IF is_new_contact THEN
        RAISE NOTICE 'New contact created with follow-ups enabled: % (ID: %)', p_phone_number, contact_id;
    END IF;

    RETURN contact_id;
END;
$$;

-- Function to get user's current subscription with plan details from profiles (simplified)
CREATE OR REPLACE FUNCTION get_current_user_subscription(p_auth_id TEXT)
RETURNS TABLE (
    auth_id TEXT,
    plan_name TEXT,
    subscription_start_date TIMESTAMP WITH TIME ZONE,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    subscription_status TEXT,
    days_until_expiry INTEGER,
    is_suspended BOOLEAN
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.auth_id,
        p.plan,
        p.subscription_start_date,
        p.subscription_end_date,
        CASE 
            WHEN p.is_suspended = true THEN 'suspended'
            WHEN p.plan = 'admin' THEN 'active'
            WHEN p.plan = 'free' THEN 'active'
            WHEN p.subscription_end_date IS NULL THEN 'active'
            WHEN p.subscription_end_date > CURRENT_TIMESTAMP THEN 'active'
            ELSE 'expired'
        END as subscription_status,
        CASE 
            WHEN p.subscription_end_date IS NULL THEN NULL
            ELSE EXTRACT(days FROM (p.subscription_end_date - CURRENT_TIMESTAMP))::INTEGER
        END as days_until_expiry,
        p.is_suspended
    FROM profiles p
    WHERE p.auth_id = p_auth_id;
    
    -- If no profile found, return null
    IF NOT FOUND THEN
        RETURN;
    END IF;
END;
$$;

-- Function to check user quotas (incorporating subscription plans)
CREATE OR REPLACE FUNCTION check_user_quotas(p_auth_id TEXT)
RETURNS TABLE (
    plan_name TEXT,
    subscription_status TEXT,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    message_limit INTEGER,
    contact_limit INTEGER,
    current_message_count INTEGER,
    current_contact_count INTEGER,
    can_send_message BOOLEAN,
    can_add_contact BOOLEAN,
    subscription_active BOOLEAN,
    days_until_expiry INTEGER
)
LANGUAGE plpgsql
AS $$
DECLARE
    user_plan TEXT;
    plan_limits RECORD;
    current_counts RECORD;
    subscription_info RECORD;
BEGIN
    -- Get subscription info from the simplified function
    SELECT * INTO subscription_info FROM get_current_user_subscription(p_auth_id);
    
    IF subscription_info IS NULL THEN
        -- Return default values if user not found
        RETURN QUERY SELECT 
            'none'::TEXT, 'not_found'::TEXT, NULL::TIMESTAMP WITH TIME ZONE,
            0, 0, 0, 0, false, false, false, 0;
        RETURN;
    END IF;

    user_plan := subscription_info.plan_name;
    
    -- Get plan limits from subscription_plans table
    SELECT monthly_message_limit, total_contact_limit 
    INTO plan_limits
    FROM subscription_plans 
    WHERE name = user_plan AND is_active = true;
    
    IF plan_limits IS NULL THEN
        -- Fallback for unknown plans
        plan_limits.monthly_message_limit := 0;
        plan_limits.total_contact_limit := 0;
    END IF;
    
    -- Get current usage counts
    SELECT 
        COALESCE(SUM(messages_sent + messages_received), 0) as current_messages,
        COALESCE(MAX(total_contacts), 0) as current_contacts
    INTO current_counts
    FROM monthly_usage 
    WHERE auth_id = p_auth_id 
        AND year = EXTRACT(YEAR FROM CURRENT_DATE)
        AND month = EXTRACT(MONTH FROM CURRENT_DATE);
    
    -- If no usage data, set to 0
    IF current_counts IS NULL THEN
        current_counts.current_messages := 0;
        current_counts.current_contacts := 0;
    END IF;
    
    -- Return the quota information
    RETURN QUERY SELECT 
        user_plan,
        subscription_info.subscription_status,
        subscription_info.subscription_end_date,
        plan_limits.monthly_message_limit,
        plan_limits.total_contact_limit,
        current_counts.current_messages::INTEGER,
        current_counts.current_contacts::INTEGER,
        -- Can send message if plan allows unlimited (-1) or under limit and subscription active
        CASE 
            WHEN subscription_info.subscription_status != 'active' THEN false
            WHEN plan_limits.monthly_message_limit = -1 THEN true
            ELSE current_counts.current_messages < plan_limits.monthly_message_limit
        END as can_send_message,
        -- Can add contact if plan allows unlimited (-1) or under limit and subscription active  
        CASE 
            WHEN subscription_info.subscription_status != 'active' THEN false
            WHEN plan_limits.total_contact_limit = -1 THEN true
            ELSE current_counts.current_contacts < plan_limits.total_contact_limit
        END as can_add_contact,
        subscription_info.subscription_status = 'active' as subscription_active,
        subscription_info.days_until_expiry;
END;
$$;

-- Function to increment message usage in monthly_usage table
CREATE OR REPLACE FUNCTION increment_message_usage(p_auth_id TEXT, p_message_type TEXT DEFAULT 'outgoing')
RETURNS VOID AS $$
DECLARE
    current_year INTEGER;
    current_month INTEGER;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE);
    current_month := EXTRACT(MONTH FROM CURRENT_DATE);
    
    INSERT INTO monthly_usage (auth_id, year, month, messages_sent, messages_received)
    VALUES (
        p_auth_id, 
        current_year, 
        current_month,
        CASE WHEN p_message_type = 'outgoing' THEN 1 ELSE 0 END,
        CASE WHEN p_message_type = 'incoming' THEN 1 ELSE 0 END
    )
    ON CONFLICT (auth_id, year, month)
    DO UPDATE SET
        messages_sent = monthly_usage.messages_sent + (CASE WHEN p_message_type = 'outgoing' THEN 1 ELSE 0 END),
        messages_received = monthly_usage.messages_received + (CASE WHEN p_message_type = 'incoming' THEN 1 ELSE 0 END),
        updated_at = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Function to expire trial subscriptions
CREATE OR REPLACE FUNCTION expire_trial_subscriptions()
RETURNS VOID AS $$
BEGIN
    UPDATE profiles 
    SET plan = 'free',
        subscription_end_date = NULL,
        updated_at = CURRENT_TIMESTAMP
    WHERE plan = 'trial' 
        AND subscription_end_date < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Add source column to uploaded_media table to distinguish between incoming WhatsApp media and user uploads
ALTER TABLE uploaded_media 
ADD COLUMN IF NOT EXISTS source VARCHAR(50) DEFAULT 'user_upload',
ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS whatsapp_media_id VARCHAR(255);

-- Add index for WhatsApp media lookups
CREATE INDEX IF NOT EXISTS idx_uploaded_media_whatsapp ON uploaded_media(auth_id, phone_number, source) WHERE source = 'whatsapp_incoming';
CREATE INDEX IF NOT EXISTS idx_uploaded_media_whatsapp_id ON uploaded_media(whatsapp_media_id) WHERE whatsapp_media_id IS NOT NULL;

-- Add enhanced AI analysis fields to contacts table
ALTER TABLE contacts
ADD COLUMN IF NOT EXISTS auto_tags TEXT[], -- AI-generated tags
ADD COLUMN IF NOT EXISTS ai_intent_category TEXT, -- AI-determined intent category
ADD COLUMN IF NOT EXISTS ai_engagement_score DECIMAL(4,3), -- AI-calculated engagement score (0.000 to 1.000)
ADD COLUMN IF NOT EXISTS ai_urgency_level TEXT, -- AI-determined urgency level
ADD COLUMN IF NOT EXISTS ai_journey_stage TEXT, -- AI-determined customer journey stage
ADD COLUMN IF NOT EXISTS ai_sentiment_score DECIMAL(4,3), -- AI-calculated sentiment score (-1.000 to 1.000)
ADD COLUMN IF NOT EXISTS ai_optimal_follow_up_hours INTEGER, -- AI-recommended follow-up timing
ADD COLUMN IF NOT EXISTS ai_confidence_score DECIMAL(4,3); -- AI confidence in analysis (0.000 to 1.000)

-- Template performance tracking table
CREATE TABLE IF NOT EXISTS template_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    template_id TEXT NOT NULL,
    variant TEXT NOT NULL,
    intent_category TEXT NOT NULL,
    outcome TEXT NOT NULL, -- 'sent', 'responded', 'converted'
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Template settings table
CREATE TABLE IF NOT EXISTS template_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ab_testing_enabled BOOLEAN DEFAULT true,
    preferred_variant TEXT,
    custom_templates JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(auth_id)
);

-- Follow-up analytics table
CREATE TABLE IF NOT EXISTS follow_up_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    follow_up_id UUID REFERENCES follow_up_schedules(id) ON DELETE CASCADE,
    outcome TEXT NOT NULL, -- 'sent', 'responded', 'converted', 'failed'
    response_time_hours DECIMAL(8,2),
    conversion_value DECIMAL(10,2),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_template_performance_auth_id ON template_performance(auth_id);
CREATE INDEX IF NOT EXISTS idx_template_performance_variant ON template_performance(auth_id, variant);
CREATE INDEX IF NOT EXISTS idx_template_performance_intent ON template_performance(auth_id, intent_category);
CREATE INDEX IF NOT EXISTS idx_template_performance_outcome ON template_performance(auth_id, outcome);
CREATE INDEX IF NOT EXISTS idx_template_performance_created_at ON template_performance(created_at);

CREATE INDEX IF NOT EXISTS idx_follow_up_analytics_auth_id ON follow_up_analytics(auth_id);
CREATE INDEX IF NOT EXISTS idx_follow_up_analytics_outcome ON follow_up_analytics(auth_id, outcome);
CREATE INDEX IF NOT EXISTS idx_follow_up_analytics_created_at ON follow_up_analytics(created_at);

-- Function to track template performance
CREATE OR REPLACE FUNCTION track_template_performance(
    p_template_id TEXT,
    p_auth_id UUID,
    p_outcome TEXT
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO template_performance (template_id, auth_id, outcome)
    VALUES (p_template_id, p_auth_id, p_outcome);
END;
$$;

-- Function to track follow-up outcome
CREATE OR REPLACE FUNCTION track_follow_up_outcome(
    p_follow_up_id UUID,
    p_auth_id UUID,
    p_outcome TEXT,
    p_metadata JSONB DEFAULT '{}',
    p_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO follow_up_analytics (follow_up_id, auth_id, outcome, metadata, created_at)
    VALUES (p_follow_up_id, p_auth_id, p_outcome, p_metadata, p_timestamp);
END;
$$;

-- Function to get follow-up overview metrics
CREATE OR REPLACE FUNCTION get_follow_up_overview_metrics(
    p_auth_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE,
    p_end_date TIMESTAMP WITH TIME ZONE
)
RETURNS TABLE (
    total_scheduled BIGINT,
    total_sent BIGINT,
    total_responded BIGINT,
    total_converted BIGINT,
    response_rate DECIMAL(5,4),
    conversion_rate DECIMAL(5,4),
    avg_response_time DECIMAL(8,2)
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) FILTER (WHERE fs.status IN ('pending', 'executed', 'failed')) as total_scheduled,
        COUNT(*) FILTER (WHERE fs.status = 'executed') as total_sent,
        COUNT(*) FILTER (WHERE fa.outcome = 'responded') as total_responded,
        COUNT(*) FILTER (WHERE fa.outcome = 'converted') as total_converted,
        CASE
            WHEN COUNT(*) FILTER (WHERE fs.status = 'executed') > 0
            THEN COUNT(*) FILTER (WHERE fa.outcome = 'responded')::DECIMAL / COUNT(*) FILTER (WHERE fs.status = 'executed')
            ELSE 0
        END as response_rate,
        CASE
            WHEN COUNT(*) FILTER (WHERE fa.outcome = 'responded') > 0
            THEN COUNT(*) FILTER (WHERE fa.outcome = 'converted')::DECIMAL / COUNT(*) FILTER (WHERE fa.outcome = 'responded')
            ELSE 0
        END as conversion_rate,
        AVG(fa.response_time_hours) FILTER (WHERE fa.response_time_hours IS NOT NULL) as avg_response_time
    FROM follow_up_schedules fs
    LEFT JOIN follow_up_analytics fa ON fs.id = fa.follow_up_id
    WHERE fs.auth_id = p_auth_id
    AND fs.created_at BETWEEN p_start_date AND p_end_date;
END;
$$;

-- Function to start a human takeover session
CREATE OR REPLACE FUNCTION start_human_takeover_session(
    p_auth_id TEXT,
    p_phone_number TEXT,
    p_timeout_minutes INTEGER DEFAULT 30
)
RETURNS UUID AS $$
DECLARE
    session_id UUID;
BEGIN
    -- End any existing active sessions for this auth_id and phone_number
    UPDATE human_takeover_sessions 
    SET is_active = false,
        ended_at = NOW(),
        updated_at = NOW()
    WHERE auth_id = p_auth_id 
      AND phone_number = p_phone_number 
      AND is_active = true;
    
    -- Create new session
    INSERT INTO human_takeover_sessions (auth_id, phone_number, timeout_minutes)
    VALUES (p_auth_id, p_phone_number, p_timeout_minutes)
    RETURNING id INTO session_id;
    
    RETURN session_id;
END;
$$ LANGUAGE plpgsql;

-- Function to end a human takeover session
CREATE OR REPLACE FUNCTION end_human_takeover_session(
    p_auth_id TEXT,
    p_phone_number TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    session_found BOOLEAN := false;
BEGIN
    -- End the active session
    UPDATE human_takeover_sessions 
    SET is_active = false,
        ended_at = NOW(),
        updated_at = NOW()
    WHERE auth_id = p_auth_id 
      AND phone_number = p_phone_number 
      AND is_active = true;
    
    session_found := FOUND;
    
    RETURN session_found;
END;
$$ LANGUAGE plpgsql;

-- Function to update human takeover session activity
CREATE OR REPLACE FUNCTION update_human_takeover_activity(
    p_auth_id TEXT,
    p_phone_number TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    session_found BOOLEAN := false;
BEGIN
    -- Update the last activity timestamp
    UPDATE human_takeover_sessions 
    SET last_activity_at = NOW(),
        updated_at = NOW()
    WHERE auth_id = p_auth_id 
      AND phone_number = p_phone_number 
      AND is_active = true;
    
    session_found := FOUND;
    
    RETURN session_found;
END;
$$ LANGUAGE plpgsql;

-- Function to check if there's an active human takeover session
CREATE OR REPLACE FUNCTION is_human_takeover_active(
    p_auth_id TEXT,
    p_phone_number TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    session_active BOOLEAN := false;
    session_timeout_minutes INTEGER;
    session_last_activity TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get the active session info
    SELECT timeout_minutes, last_activity_at
    INTO session_timeout_minutes, session_last_activity
    FROM human_takeover_sessions
    WHERE auth_id = p_auth_id 
      AND phone_number = p_phone_number 
      AND is_active = true;
    
    -- If no active session found, return false
    IF session_timeout_minutes IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check if session has timed out
    IF session_last_activity + INTERVAL '1 minute' * session_timeout_minutes < NOW() THEN
        -- Session has timed out, end it
        UPDATE human_takeover_sessions 
        SET is_active = false,
            ended_at = NOW(),
            updated_at = NOW()
        WHERE auth_id = p_auth_id 
          AND phone_number = p_phone_number 
          AND is_active = true;
        
        RETURN false;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- ===== INDEXES FOR AUTO-TAGGING AND FOLLOW-UP SYSTEM =====



-- Indexes for follow_up_schedules table
CREATE INDEX IF NOT EXISTS idx_follow_up_schedules_contact_id ON follow_up_schedules(contact_id);
CREATE INDEX IF NOT EXISTS idx_follow_up_schedules_auth_id ON follow_up_schedules(auth_id);
CREATE INDEX IF NOT EXISTS idx_follow_up_schedules_scheduled_at ON follow_up_schedules(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_follow_up_schedules_status ON follow_up_schedules(status);
CREATE INDEX IF NOT EXISTS idx_follow_up_schedules_pending ON follow_up_schedules(scheduled_at) WHERE status = 'pending';

-- Indexes for follow_up_rules table
CREATE INDEX IF NOT EXISTS idx_follow_up_rules_auth_id ON follow_up_rules(auth_id);
CREATE INDEX IF NOT EXISTS idx_follow_up_rules_intent_category ON follow_up_rules(intent_category);
CREATE INDEX IF NOT EXISTS idx_follow_up_rules_active ON follow_up_rules(is_active) WHERE is_active = true;

-- Additional indexes for contacts table
CREATE INDEX IF NOT EXISTS idx_contacts_follow_up_enabled ON contacts(follow_up_enabled) WHERE follow_up_enabled = true;
CREATE INDEX IF NOT EXISTS idx_contacts_next_follow_up ON contacts(next_follow_up_at) WHERE next_follow_up_at IS NOT NULL;

-- Indexes for AI analysis fields
CREATE INDEX IF NOT EXISTS idx_contacts_auto_tags ON contacts USING GIN(auto_tags) WHERE auto_tags IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contacts_ai_intent_category ON contacts(ai_intent_category) WHERE ai_intent_category IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contacts_ai_engagement_score ON contacts(ai_engagement_score) WHERE ai_engagement_score IS NOT NULL;

-- ===== FUNCTIONS FOR AUTO-TAGGING AND FOLLOW-UP SYSTEM =====



-- Function to schedule follow-up for a contact
CREATE OR REPLACE FUNCTION schedule_follow_up(
    p_contact_id UUID,
    p_auth_id TEXT,
    p_phone_number TEXT,
    p_follow_up_type TEXT,
    p_delay_hours INTEGER DEFAULT 24,
    p_message_template TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    follow_up_id UUID;
    scheduled_time TIMESTAMP WITH TIME ZONE;
    rule_template TEXT;
    max_attempts INTEGER;
BEGIN
    -- Calculate scheduled time
    scheduled_time := NOW() + (p_delay_hours || ' hours')::INTERVAL;

    -- Get message template from rules if not provided
    IF p_message_template IS NULL THEN
        -- Use default template since AI intent category is no longer available
        p_message_template := 'Hi! Just following up on our conversation. Is there anything else I can help you with? 😊';
        max_attempts := 1;
    ELSE
        max_attempts := 1;
    END IF;

    -- Create follow-up schedule
    INSERT INTO follow_up_schedules (
        contact_id,
        auth_id,
        phone_number,
        follow_up_type,
        scheduled_at,
        message_template,
        max_attempts
    ) VALUES (
        p_contact_id,
        p_auth_id,
        p_phone_number,
        p_follow_up_type,
        scheduled_time,
        p_message_template,
        max_attempts
    ) RETURNING id INTO follow_up_id;

    -- Update contact with next follow-up time
    UPDATE contacts SET
        next_follow_up_at = scheduled_time,
        updated_at = NOW()
    WHERE id = p_contact_id;

    RETURN follow_up_id;
END;
$$;

-- Function to get pending follow-ups that are ready to be sent
CREATE OR REPLACE FUNCTION get_pending_follow_ups(
    p_limit INTEGER DEFAULT 50
)
RETURNS TABLE (
    id UUID,
    contact_id UUID,
    auth_id TEXT,
    phone_number TEXT,
    follow_up_type TEXT,
    message_template TEXT,
    attempt_number INTEGER,
    contact_name TEXT,
    contact_follow_up_enabled BOOLEAN
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        fs.id,
        fs.contact_id,
        fs.auth_id,
        fs.phone_number,
        fs.follow_up_type,
        fs.message_template,
        fs.attempt_number,
        c.name as contact_name,
        c.follow_up_enabled as contact_follow_up_enabled
    FROM follow_up_schedules fs
    JOIN contacts c ON fs.contact_id = c.id
    WHERE fs.status = 'pending'
    AND fs.scheduled_at <= NOW()
    AND c.follow_up_enabled = true
    AND c.follow_up_stopped_reason IS NULL
    ORDER BY fs.scheduled_at ASC
    LIMIT p_limit;
END;
$$;

-- Function to mark follow-up as executed
CREATE OR REPLACE FUNCTION mark_follow_up_executed(
    p_follow_up_id UUID,
    p_whatsapp_message_id TEXT,
    p_message_sent TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    contact_uuid UUID;
    follow_up_count_val INTEGER;
BEGIN
    -- Update follow-up schedule
    UPDATE follow_up_schedules SET
        status = 'executed',
        executed_at = NOW(),
        whatsapp_message_id = p_whatsapp_message_id,
        message_sent = p_message_sent,
        updated_at = NOW()
    WHERE id = p_follow_up_id
    RETURNING contact_id INTO contact_uuid;

    -- Update contact follow-up tracking
    UPDATE contacts SET
        follow_up_count = follow_up_count + 1,
        last_follow_up_at = NOW(),
        next_follow_up_at = NULL,
        updated_at = NOW()
    WHERE id = contact_uuid;

    RETURN true;
END;
$$;

-- Function to stop follow-ups for a contact
CREATE OR REPLACE FUNCTION stop_follow_ups(
    p_contact_id UUID,
    p_reason TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    -- Cancel pending follow-ups
    UPDATE follow_up_schedules SET
        status = 'cancelled',
        updated_at = NOW()
    WHERE contact_id = p_contact_id
    AND status = 'pending';

    -- Update contact
    UPDATE contacts SET
        follow_up_enabled = false,
        follow_up_stopped_reason = p_reason,
        next_follow_up_at = NULL,
        updated_at = NOW()
    WHERE id = p_contact_id;

    RETURN true;
END;
$$;

-- Function to enable follow-ups for a contact
CREATE OR REPLACE FUNCTION enable_follow_ups(
    p_contact_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE contacts SET
        follow_up_enabled = true,
        follow_up_stopped_reason = NULL,
        updated_at = NOW()
    WHERE id = p_contact_id;

    RETURN true;
END;
$$;

-- Function to mark follow-up as cancelled
CREATE OR REPLACE FUNCTION mark_follow_up_cancelled(
    p_follow_up_id UUID,
    p_reason TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update follow-up schedule
    UPDATE follow_up_schedules SET
        status = 'cancelled',
        failure_reason = p_reason,
        updated_at = NOW()
    WHERE id = p_follow_up_id;

    RETURN true;
END;
$$;

-- Function to create default follow-up rules for a new business
CREATE OR REPLACE FUNCTION create_default_follow_up_rules(
    p_auth_id TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    -- Create default rules for each intent category (with WhatsApp 24h window compliance)
    INSERT INTO follow_up_rules (auth_id, rule_name, intent_category, delay_hours, max_follow_ups, message_template, is_active)
    VALUES
    (
        p_auth_id,
        'Interested Customer Follow-up',
        'interested',
        4, -- 4 hours - well within 24h window
        1,
        'Hi {name}! 👋 Just following up on our conversation. Do you have any other questions about our products/services? I''m here to help! 😊',
        true
    ),
    (
        p_auth_id,
        'Booking Reminder',
        'booking',
        2, -- 2 hours - urgent for bookings
        2,
        'Hi {name}! 📅 Following up on your booking inquiry. Would you like to proceed with scheduling? Let me know what works best for you!',
        false
    ),
    (
        p_auth_id,
        'Gentle Re-engagement',
        'not_interested',
        48,
        1,
        'Hi {name}! 👋 Hope you''re doing well. Just wanted to check if anything has changed or if you''d like to hear about any new offers we have! 😊',
        false
    )
    ON CONFLICT (auth_id, intent_category) DO NOTHING;

    RETURN true;
END;
$$;

