import React from "react";
import { MessageCircle } from "lucide-react";

const WHATSAPP_LINK = "https://wa.me/60182713466?text=Hello%20Chilbee%20Team!";

export default function ContactUs() {
  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-blue-100/50 min-h-screen flex flex-col justify-between">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 lg:py-32 flex-1 flex items-center">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="relative container mx-auto px-6 flex flex-col items-center justify-center w-full">
          <div className="max-w-lg w-full text-center fade-in">
            <div className="flex flex-col items-center gap-4 mb-8">
              <div className="bg-green-100 p-4 rounded-full mb-2">
                <MessageCircle className="w-10 h-10 text-green-600" />
              </div>
              <h1 className="text-4xl font-extrabold text-gray-900 mb-2">Contact Us</h1>
              <p className="text-lg text-gray-600 mb-2">Have questions or need help? Reach out to us directly on WhatsApp or email. We support Malaysian businesses in BM, English, and Chinese.</p>
            </div>
            <a
              href={WHATSAPP_LINK}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-green-500 hover:bg-green-600 text-white font-bold px-8 py-4 rounded-xl text-lg shadow-lg transition-all duration-200 mb-4"
            >
              Contact Us via WhatsApp
            </a>
            <div className="mt-6 text-gray-500 text-sm">
              Or email us at <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
            </div>
            <div className="mt-8 text-xs text-gray-400">
              <p>Business Hours: 9am - 6pm (Mon-Fri, Malaysia Time)</p>
              <p>Bahasa Malaysia, English, 中文 supported</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 