import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useAlertMigration } from "../hooks/useAlertMigration";
import { InlineLoader } from "../components/ui/loader";
import Icon from "../components/ui/icon";

export default function ChangePassword() {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const { changePassword, user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { showError } = useAlertMigration();

  // Redirect if not authenticated
  if (!isAuthenticated) {
    navigate("/login");
    return null;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Clear previous errors - not needed with global alerts
    setSuccess(false);

    // Validation
    if (newPassword !== confirmPassword) {
      showError("New passwords do not match");
      return;
    }

    if (newPassword.length < 6) {
      showError("New password must be at least 6 characters long");
      return;
    }

    if (currentPassword === newPassword) {
      showError("New password must be different from current password");
      return;
    }

    setLoading(true);
    try {
      await changePassword(currentPassword, newPassword);
      setSuccess(true);
      // Clear form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (err) {
      showError(err.message || "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md mx-auto">
          {/* Success Icon */}
          <div className="text-center mb-8 fade-in">
            <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
              <Icon name="CheckCircle" className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Password changed successfully!
            </h1>
            <p className="text-gray-600">
              Your password has been updated securely
            </p>
          </div>

          {/* Success Card */}
          <div className="card space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <Icon
                  name="CheckCircle"
                  className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5"
                />
                <div>
                  <h3 className="text-sm font-semibold text-green-900 mb-1">
                    Password updated
                  </h3>
                  <p className="text-sm text-green-700">
                    Your new password is now active. Make sure to remember it
                    for future logins.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => setSuccess(false)}
                className="btn-secondary w-full"
              >
                Change password again
              </button>

              <button
                onClick={() => navigate("/dashboard")}
                className="btn-primary w-full"
              >
                Continue to dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8 fade-in">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Icon name="Lock" className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Change your password
          </h2>
          <p className="text-gray-600">
            Update your password to keep your account secure
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="card space-y-6">
          {/* Current Password */}
          <div>
            <label
              htmlFor="currentPassword"
              className="block text-sm font-semibold text-gray-700 mb-2"
            >
              Current password
            </label>
            <div className="relative">
              <input
                id="currentPassword"
                type="password"
                className="input-field"
                style={{ paddingLeft: "3rem" }}
                placeholder="Enter your current password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                required
              />
              <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                <Icon name="Lock" className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>

          {/* New Password */}
          <div>
            <label
              htmlFor="newPassword"
              className="block text-sm font-semibold text-gray-700 mb-2"
            >
              New password
            </label>
            <div className="relative">
              <input
                id="newPassword"
                type="password"
                className="input-field"
                style={{ paddingLeft: "3rem" }}
                placeholder="Enter your new password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
              />
              <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                <Icon name="Lock" className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>

          {/* Confirm New Password */}
          <div>
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-semibold text-gray-700 mb-2"
            >
              Confirm new password
            </label>
            <div className="relative">
              <input
                id="confirmPassword"
                type="password"
                className="input-field"
                style={{ paddingLeft: "3rem" }}
                placeholder="Confirm your new password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
              <div className="absolute inset-y-0 left-0 flex items-center justify-center w-12 pointer-events-none">
                <Icon name="Lock" className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>

          {/* Password Requirements */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <Icon
                name="Lock"
                className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5"
              />
              <div>
                <h3 className="text-sm font-semibold text-blue-900 mb-1">
                  Password requirements
                </h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• At least 6 characters long</li>
                  <li>• Different from your current password</li>
                  <li>• Use a strong, unique password</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Error messages now handled by global AlertContainer */}

          {/* Submit Button */}
          <button
            type="submit"
            className="btn-primary w-full text-lg py-4"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-3">
                <InlineLoader size="sm" />
                <span>Updating password...</span>
              </div>
            ) : (
              <span>Update password</span>
            )}
          </button>
        </form>

        {/* User Info */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            Changing password for:{" "}
            <span className="font-semibold">
              {user?.username || user?.user?.email}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
}
