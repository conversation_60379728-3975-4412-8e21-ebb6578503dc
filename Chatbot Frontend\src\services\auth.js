import { api, tokenManager } from "./api.js";

// Authentication service
export const authService = {
  // Sign up (register)
  async signup(email, password, phone, name) {
    try {
      const response = await api.post("/api/auth/signup", {
        email,
        password,
        phone: phone,
        name: name,
      });

      // Store the session token
      if (response.data.session?.access_token) {
        tokenManager.setToken(response.data.session.access_token);
      }
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Registration failed");
    }
  },

  // Sign in (login)
  async signin(email, password) {
    try {
      const response = await api.post("/api/auth/signin", {
        email,
        password,
      });

      // Store the session token
      if (response.data.session?.access_token) {
        tokenManager.setToken(response.data.session.access_token);
      }
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Login failed");
    }
  },

  // Sign out
  async signout() {
    try {
      const token = tokenManager.getToken();
      if (token) {
        await api.post("/api/auth/signout", {
          access_token: token,
        });
      }
    } catch {
      // Continue with logout even if API call fails
    } finally {
      // Always remove token from localStorage
      tokenManager.removeToken();
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.get("/api/auth/user", {
        headers: {
          access_token: token,
        },
      });

      // Return both user and profile data
      // The profile contains display_name which can be used as username
      return {
        user: response.data.user,
        profile: response.data.profile,
        username:
          response.data.profile?.display_name ||
          response.data.user?.email ||
          "User",
      };
    } catch (error) {
      // If token is invalid, remove it
      if (error.response?.status === 401) {
        tokenManager.removeToken();
      }
      throw new Error(error.response?.data?.error || "Failed to get user info");
    }
  },

  // Reset password
  async resetPassword(email) {
    try {
      const response = await api.post("/api/auth/reset-password", {
        email,
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Password reset failed");
    }
  },

  // Complete password reset (using token from email)
  async forgotPassword(newPassword, accessToken, refreshToken) {
    try {
      const response = await api.post("/api/auth/forgot-password", {
        newPassword,
        accessToken,
        refreshToken,
      });

      // Store the new session token if provided
      if (response.data.session?.access_token) {
        tokenManager.setToken(response.data.session.access_token);
      }

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Password reset failed");
    }
  },

  // Change password
  async changePassword(currentPassword, newPassword) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.post(
        "/api/auth/change-password",
        {
          currentPassword,
          newPassword,
          // Note: refresh_token is optional according to the API
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || "Password change failed");
    }
  },

  // Change display name
  async changeDisplayName(newDisplayName) {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        throw new Error("No access token found");
      }

      const response = await api.post(
        "/api/auth/change-display-name",
        {
          newDisplayName,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Display name change failed",
      );
    }
  },

  // Check if user is authenticated
  isAuthenticated() {
    return !!tokenManager.getToken();
  },
};

// Export as default for compatibility
export default authService;
