import React, { useState } from 'react';
import Icon from './ui/icon';
import { Checkbox } from './ui/checkbox';
import { getAllTemplates } from '../data/businessTemplates';

export default function QuickSetupWizard({ onComplete, onClose }) {
  const [currentStep, setCurrentStep] = useState(1);
  const [wizardData, setWizardData] = useState({
    businessName: '',
    businessType: '',
    primaryGoal: '',
    customerLanguage: 'multilingual',
    tone: 'friendly',
    features: []
  });

  const templates = getAllTemplates();
  
  const steps = [
    { number: 1, title: 'Business Info', description: 'Tell us about your business' },
    { number: 2, title: 'Goals', description: 'What should your chatbot do?' },
    { number: 3, title: 'Personality', description: 'How should it communicate?' },
    { number: 4, title: 'Features', description: 'What features do you need?' },
    { number: 5, title: 'Review', description: 'Review and launch' }
  ];

  const goals = [
    { id: 'customer_service', title: 'Customer Service', description: 'Answer questions, provide support', icon: '💬' },
    { id: 'sales', title: 'Sales & Orders', description: 'Help customers buy products', icon: '🛒' },
    { id: 'information', title: 'Information Hub', description: 'Share business info, hours, location', icon: 'ℹ️' },
    { id: 'appointments', title: 'Appointments', description: 'Schedule bookings and services', icon: '📅' }
  ];

  const tones = [
    { id: 'friendly', title: 'Friendly & Casual', description: 'Warm, like talking to a friend', example: '"Hi there! How can I help ya? 😊"' },
    { id: 'professional', title: 'Professional', description: 'Polite and business-appropriate', example: '"Good day! How may I assist you today?"' },
    { id: 'enthusiastic', title: 'Enthusiastic', description: 'Energetic and excited', example: '"Hey! So excited to help you today! 🎉"' }
  ];

  const features = [
    { id: 'multilingual', title: 'Multi-language Support', description: 'English, Malay, Chinese' },
    { id: 'images', title: 'Product Images', description: 'Show pictures of products' },
    { id: 'prices', title: 'Pricing Info', description: 'Display prices automatically' },
    { id: 'orders', title: 'Order Processing', description: 'Take and manage orders' },
    { id: 'memory', title: 'Customer Memory', description: 'Remember customer preferences' }
  ];

  const handleNext = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFeatureToggle = (featureId) => {
    const newFeatures = wizardData.features.includes(featureId)
      ? wizardData.features.filter(f => f !== featureId)
      : [...wizardData.features, featureId];
    
    setWizardData({ ...wizardData, features: newFeatures });
  };

  const generateConfiguration = () => {
    // Generate simplified prompt based on wizard selections
    const businessTypeDescriptions = {
      retail: 'retail store assistant',
      restaurant: 'restaurant assistant',
      service: 'service assistant',
      ecommerce: 'online store assistant',
      healthcare: 'healthcare assistant',
      education: 'education assistant'
    };

    const toneDescriptions = {
      friendly: 'friendly and casual, like talking to a helpful friend',
      professional: 'professional and polite, maintaining business standards',
      enthusiastic: 'enthusiastic and energetic, excited about helping customers'
    };

    const languageInstructions = {
      multilingual: 'Respond in the same language the customer uses (English, Malay, Chinese, or mixed)',
      english: 'Respond only in English'
    };

    const businessName = wizardData.businessName ? `${wizardData.businessName} ` : '';
    const businessType = businessTypeDescriptions[wizardData.businessType] || 'customer service assistant';
    const tone = toneDescriptions[wizardData.tone] || 'friendly and helpful';
    const language = languageInstructions[wizardData.customerLanguage] || 'Respond in the customer\'s preferred language';

    const orderHandling = wizardData.features.includes('orders')
      ? 'Help customers with orders by collecting details step by step and confirming everything before finalizing.'
      : 'You cannot process orders or payments. Direct customers to contact the business directly for purchases.';

    const systemPrompt = `You are a ${businessName}${businessType}. Be ${tone} in your responses.
PERSONALITY: ${tone.charAt(0).toUpperCase() + tone.slice(1)}. Use natural expressions and be genuinely helpful.
LANGUAGE: ${language}.
KNOWLEDGE: Use only the information provided in your knowledge base. If you don't know something, politely say you're not sure.
ORDERS: ${orderHandling}
STYLE: Give complete, helpful information. Be natural and conversational, not robotic.`;

    const orderPrompt = `You are a ${businessName}sales assistant who helps customers with orders and questions.
PERSONALITY: Warm, helpful, and efficient. Use natural expressions.
LANGUAGE: ${language}.
ORDERS: Help customers by showing products, collecting order details step by step, and confirming everything before finalizing.
QUESTIONS: Answer using your knowledge base. Be complete and helpful in your responses.
COMPLETION: When an order is ready, summarize everything clearly and mark it as "ORDER_COMPLETE:" followed by the order summary.`;

    // Get optimized settings based on business type
    const template = templates.find(t => t.key === wizardData.businessType) || templates[0];

    return {
      system_prompt: systemPrompt,
      order_system_prompt: orderPrompt,
      ...template.settings,
      order_processing_enabled: wizardData.features.includes('orders')
    };
  };

  const handleComplete = () => {
    const configuration = generateConfiguration();
    onComplete(configuration, wizardData);
    onClose(); // Close the modal after completion
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                What's your business name? (Optional)
              </label>
              <input
                type="text"
                value={wizardData.businessName}
                onChange={(e) => setWizardData({ ...wizardData, businessName: e.target.value })}
                placeholder="e.g., ABC Coffee Shop, XYZ Electronics"
                className="input-field"
              />
              <p className="text-xs text-gray-500 mt-2">This helps personalize your chatbot's responses</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                What type of business do you have?
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {templates.map((template) => (
                  <button
                    key={template.key}
                    onClick={() => setWizardData({ ...wizardData, businessType: template.key })}
                    className={`p-4 rounded-lg border text-left transition-all ${
                      wizardData.businessType === template.key
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{template.icon}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">{template.name}</h4>
                        <p className="text-sm text-gray-600">{template.description}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                What's your main goal for the chatbot?
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {goals.map((goal) => (
                  <button
                    key={goal.id}
                    onClick={() => setWizardData({ ...wizardData, primaryGoal: goal.id })}
                    className={`p-4 rounded-lg border text-left transition-all ${
                      wizardData.primaryGoal === goal.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <span className="text-2xl">{goal.icon}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">{goal.title}</h4>
                        <p className="text-sm text-gray-600">{goal.description}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                How should your chatbot communicate?
              </h3>
              <div className="space-y-4">
                {tones.map((tone) => (
                  <button
                    key={tone.id}
                    onClick={() => setWizardData({ ...wizardData, tone: tone.id })}
                    className={`w-full p-4 rounded-lg border text-left transition-all ${
                      wizardData.tone === tone.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <h4 className="font-medium text-gray-900">{tone.title}</h4>
                    <p className="text-sm text-gray-600 mb-2">{tone.description}</p>
                    <p className="text-sm text-blue-600 italic">{tone.example}</p>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                What languages should it support?
              </h3>
              <div className="space-y-3">
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer">
                  <input
                    type="radio"
                    name="language"
                    value="multilingual"
                    checked={wizardData.customerLanguage === 'multilingual'}
                    onChange={(e) => setWizardData({ ...wizardData, customerLanguage: e.target.value })}
                  />
                  <div>
                    <p className="font-medium">Multi-language (Recommended)</p>
                    <p className="text-sm text-gray-600">English, Bahasa Malaysia, Chinese - matches customer language</p>
                  </div>
                </label>
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer">
                  <input
                    type="radio"
                    name="language"
                    value="english"
                    checked={wizardData.customerLanguage === 'english'}
                    onChange={(e) => setWizardData({ ...wizardData, customerLanguage: e.target.value })}
                  />
                  <div>
                    <p className="font-medium">English Only</p>
                    <p className="text-sm text-gray-600">Respond only in English</p>
                  </div>
                </label>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Which features do you want to enable?
              </h3>
              <div className="space-y-3">
                {features.map((feature) => (
                  <label
                    key={feature.id}
                    className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                  >
                    <Checkbox
                      checked={wizardData.features.includes(feature.id)}
                      onCheckedChange={() => handleFeatureToggle(feature.id)}
                      className="mt-3"
                    />
                    <div>
                      <p className="font-medium text-gray-900">{feature.title}</p>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 5: {
        const selectedTemplate = templates.find(t => t.key === wizardData.businessType);
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Icon name="CheckCircle" className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">Ready to Launch!</h3>
              <p className="text-gray-600">Your chatbot is configured and ready to help customers</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold text-gray-900 mb-4">Configuration Summary</h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Business:</span>
                  <span className="font-medium">{wizardData.businessName || 'Not specified'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium">{selectedTemplate?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tone:</span>
                  <span className="font-medium">{tones.find(t => t.id === wizardData.tone)?.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Language:</span>
                  <span className="font-medium">{wizardData.customerLanguage === 'multilingual' ? 'Multi-language' : 'English Only'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Features:</span>
                  <span className="font-medium">{wizardData.features.length} enabled</span>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Icon name="Info" className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900">Next Steps</h5>
                  <p className="text-sm text-blue-700 mt-1">
                    After setup, you can add your business information to the knowledge base and start testing your chatbot.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      }

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Quick Setup Wizard</h2>
            <p className="text-sm text-gray-600">Get your chatbot ready in 5 easy steps</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Icon name="X" className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-4 left-4 right-4 h-0.5 bg-gray-200">
              <div
                className="h-full bg-blue-600 transition-all duration-300"
                style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
              />
            </div>

            {/* Step Circles */}
            <div className="relative flex justify-between">
              {steps.map((step) => (
                <div key={step.number} className="flex flex-col items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium border-2 ${
                    currentStep >= step.number
                      ? 'border-blue-600 bg-blue-600 text-white'
                      : 'border-gray-200 bg-white text-gray-600'
                  }`}>
                    {currentStep > step.number ? (
                      <Icon name="Check" className="w-4 h-4" />
                    ) : (
                      step.number
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="mt-4">
            <h3 className="font-medium text-gray-900">{steps[currentStep - 1].title}</h3>
            <p className="text-sm text-gray-600">{steps[currentStep - 1].description}</p>
          </div>
        </div>

        {/* Step Content */}
        <div className="p-4 sm:p-6 max-h-[45vh] sm:max-h-[50vh] overflow-y-auto">
          {renderStep()}
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 p-4 sm:p-6 border-t border-gray-200">
          <button
            onClick={handleBack}
            disabled={currentStep === 1}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            <Icon name="ArrowLeft" className="w-4 h-4 mr-2" />
            Back
          </button>

          {currentStep < 5 ? (
            <button
              onClick={handleNext}
              disabled={
                (currentStep === 1 && !wizardData.businessType) ||
                (currentStep === 2 && !wizardData.primaryGoal) ||
                (currentStep === 3 && !wizardData.tone)
              }
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Next
              <Icon name="ArrowRight" className="w-4 h-4 ml-2" />
            </button>
          ) : (
            <button
              onClick={handleComplete}
              className="btn-primary flex items-center justify-center"
            >
              <Icon name="Rocket" className="w-4 h-4 mr-2" />
              Launch Chatbot
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
