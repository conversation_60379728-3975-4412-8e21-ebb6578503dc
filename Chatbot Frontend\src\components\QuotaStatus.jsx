import { useState, useEffect } from "react";
import { aiService } from "../services/ai";
import { useAlertMigration } from "../hooks/useAlertMigration";
import Icon from "./ui/icon";

export default function QuotaStatus({ authId, className = "" }) {
  const [quota, setQuota] = useState(null);
  const [loading, setLoading] = useState(true);
  const { showError } = useAlertMigration();

  useEffect(() => {
    if (authId) {
      fetchQuotaStatus();
    }
  }, [authId]);

  const fetchQuotaStatus = async () => {
    try {
      setLoading(true);
      const response = await aiService.getQuotaStatus(authId);

      if (response.success) {
        setQuota(response.quota);
      } else {
        showError("Failed to load quota information");
      }
    } catch (error) {
      console.error("Error fetching quota:", error);
      showError("Failed to load quota information");
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num === -1) return "∞";
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num?.toString() || "0";
  };

  const getProgressBarColor = (percentage) => {
    if (percentage >= 90) return "bg-red-500";
    if (percentage >= 80) return "bg-yellow-500";
    return "bg-blue-500";
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return "text-red-600";
    if (percentage >= 80) return "text-yellow-600";
    return "text-blue-600";
  };

  if (loading) {
    return (
      <div className={`card ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[1, 2].map((i) => (
              <div key={i}>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-2 bg-gray-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!quota) {
    return (
      <div className={`card bg-red-50 border-red-200 ${className}`}>
        <div className="text-center py-4">
          <p className="text-sm text-red-600">
            Unable to load quota information
          </p>
          <button
            onClick={fetchQuotaStatus}
            className="mt-2 text-xs text-red-500 hover:text-red-700 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`card ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Usage & Quotas
            </h3>
            <p className="text-sm text-gray-600 capitalize">
              {quota.plan} Plan • {quota.subscriptionStatus}
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-gray-500">Expires in</p>
            <p className="text-sm font-medium text-gray-900">
              {quota.period.daysUntilExpiry ?? "0"} days
            </p>
          </div>
        </div>

        {/* Subscription Warning */}
        {quota.warnings.subscriptionExpiring && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <Icon name="Warning" className="w-5 h-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-800">
                  Your subscription expires in {quota.period.daysUntilExpiry}{" "}
                  days
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Message Quota */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Messages</span>
            <span className="text-sm text-gray-600">
              {formatNumber(quota.usage.totalMessages)} /{" "}
              {formatNumber(quota.limits.monthlyMessages)}
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full transition-all duration-500 ${getProgressBarColor(quota.usagePercent.messages)}`}
              style={{
                width: `${Math.min(quota.usagePercent.messages, 100)}%`,
              }}
            />
          </div>

          <div className="flex justify-between items-center">
            <span
              className={`text-xs font-medium ${getUsageColor(quota.usagePercent.messages)}`}
            >
              {quota.usagePercent.messages}% used
            </span>
            <span className="text-xs text-gray-500">
              {formatNumber(quota.remaining.messages)} remaining
            </span>
          </div>

          {/* Message Usage Warning */}
          {(quota.warnings.messageUsage80 || quota.warnings.messageUsage90) && (
            <div
              className={`text-xs px-2 py-1 rounded ${
                quota.warnings.messageUsage90
                  ? "bg-red-50 text-red-700 border border-red-200"
                  : "bg-yellow-50 text-yellow-700 border border-yellow-200"
              }`}
            >
              ⚠️ High usage: {quota.usagePercent.messages}% of monthly limit
            </div>
          )}
        </div>

        {/* Contact Quota */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Contacts</span>
            <span className="text-sm text-gray-600">
              {formatNumber(quota.usage.totalContacts)} /{" "}
              {formatNumber(quota.limits.totalContacts)}
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full transition-all duration-500 ${getProgressBarColor(quota.usagePercent.contacts)}`}
              style={{
                width: `${Math.min(quota.usagePercent.contacts, 100)}%`,
              }}
            />
          </div>

          <div className="flex justify-between items-center">
            <span
              className={`text-xs font-medium ${getUsageColor(quota.usagePercent.contacts)}`}
            >
              {quota.usagePercent.contacts}% used
            </span>
            <span className="text-xs text-gray-500">
              {formatNumber(quota.remaining.contacts)} remaining
            </span>
          </div>

          {/* Contact Usage Warning */}
          {(quota.warnings.contactUsage80 || quota.warnings.contactUsage90) && (
            <div
              className={`text-xs px-2 py-1 rounded ${
                quota.warnings.contactUsage90
                  ? "bg-red-50 text-red-700 border border-red-200"
                  : "bg-yellow-50 text-yellow-700 border border-yellow-200"
              }`}
            >
              ⚠️ High usage: {quota.usagePercent.contacts}% of contact limit
            </div>
          )}
        </div>

        {/* Status Indicators */}
        <div className="flex items-center gap-3 pt-2 text-xs">
          <div
            className={`flex items-center gap-1 ${quota.status.canSendMessages ? "text-green-600" : "text-red-600"}`}
          >
            <div
              className={`w-2 h-2 rounded-full ${quota.status.canSendMessages ? "bg-green-500" : "bg-red-500"}`}
            />
            Messages {quota.status.canSendMessages ? "Active" : "Blocked"}
          </div>
          <div
            className={`flex items-center gap-1 ${quota.status.canAddContacts ? "text-green-600" : "text-red-600"}`}
          >
            <div
              className={`w-2 h-2 rounded-full ${quota.status.canAddContacts ? "bg-green-500" : "bg-red-500"}`}
            />
            Contacts {quota.status.canAddContacts ? "Active" : "Blocked"}
          </div>
        </div>

        {/* Limits Exceeded Warning */}
        {(quota.status.isMessageLimitExceeded ||
          quota.status.isContactLimitExceeded) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <Icon name="Warning" className="w-5 h-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">
                  {quota.status.isMessageLimitExceeded &&
                    "Message limit exceeded. "}
                  {quota.status.isContactLimitExceeded &&
                    "Contact limit exceeded. "}
                  Consider upgrading your plan.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
