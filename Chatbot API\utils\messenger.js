import axios from "axios";
import logger from "./logger.js";

/**
 * Send a text message via Messenger Platform API
 */
export async function sendMessengerTextMessage(recipientId, message, pageAccessToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/me/messages`;
    const data = {
      recipient: {
        id: recipientId,
      },
      message: {
        text: message,
      },
      messaging_type: "RESPONSE",
    };

    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${pageAccessToken}`,
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error sending Messenger text message:", error);
    throw error;
  }
}

/**
 * Download media from Messenger Platform API
 */
export async function downloadMessengerMedia(mediaId, pageAccessToken) {
  try {
    // First get the media URL
    const mediaInfoUrl = `https://graph.facebook.com/v18.0/${mediaId}`;
    const mediaInfoResponse = await axios.get(mediaInfoUrl, {
      headers: {
        Authorization: `Bearer ${pageAccessToken}`,
      },
      timeout: 30000,
    });

    const mediaUrl = mediaInfoResponse.data.url;

    // Download the actual media
    const mediaResponse = await axios.get(mediaUrl, {
      headers: {
        Authorization: `Bearer ${pageAccessToken}`,
      },
      responseType: "arraybuffer",
      timeout: 60000,
    });

    return {
      data: mediaResponse.data,
      contentType: mediaResponse.headers["content-type"],
      size: mediaResponse.data.length,
    };
  } catch (error) {
    logger.error("Error downloading Messenger media:", error);
    throw error;
  }
}

/**
 * Validate Messenger webhook signature
 */
export function validateMessengerSignature(payload, signature, appSecret) {
  try {
    const crypto = require("crypto");
    const expectedSignature = crypto
      .createHmac("sha256", appSecret)
      .update(payload, "utf8")
      .digest("hex");

    return signature === `sha256=${expectedSignature}`;
  } catch (error) {
    logger.error("Error validating Messenger signature:", error);
    return false;
  }
}

/**
 * Validate Messenger page access token and get page info
 */
export async function validateMessengerCredentials(pageAccessToken, pageId) {
  try {
    const url = `https://graph.facebook.com/v18.0/${pageId}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${pageAccessToken}`,
      },
      params: {
        fields: "id,name,access_token",
      },
      timeout: 30000,
    });

    return {
      isValid: true,
      pageInfo: response.data,
    };
  } catch (error) {
    logger.error("Error validating Messenger credentials:", error);
    return {
      isValid: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Set up Messenger webhook subscription
 */
export async function setupMessengerWebhook(pageAccessToken, pageId, webhookUrl, verifyToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/${pageId}/subscribed_apps`;
    const response = await axios.post(
      url,
      {
        subscribed_fields: ["messages", "messaging_postbacks", "messaging_optins"],
      },
      {
        headers: {
          Authorization: `Bearer ${pageAccessToken}`,
        },
        timeout: 30000,
      }
    );

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    logger.error("Error setting up Messenger webhook:", error);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message,
    };
  }
}

/**
 * Process Messenger message content based on type
 */
export function processMessengerMessage(message) {
  let messageContent = "";
  let messageType = "text";

  if (message.text) {
    messageContent = message.text;
    messageType = "text";
  } else if (message.attachments && message.attachments.length > 0) {
    const attachment = message.attachments[0];
    
    switch (attachment.type) {
      case "image":
        messageContent = attachment.payload?.url || "";
        messageType = "image";
        break;
      case "audio":
        messageContent = attachment.payload?.url || "";
        messageType = "audio";
        break;
      case "video":
        messageContent = attachment.payload?.url || "";
        messageType = "video";
        break;
      case "file":
        messageContent = attachment.payload?.url || "";
        messageType = "document";
        break;
      default:
        messageContent = `Unsupported attachment type: ${attachment.type}`;
        messageType = "text";
    }
  } else {
    messageContent = "Unsupported message type";
    messageType = "text";
  }

  return {
    content: messageContent,
    type: messageType,
  };
}

/**
 * Get Messenger user profile information
 */
export async function getMessengerUserProfile(userId, pageAccessToken) {
  try {
    const url = `https://graph.facebook.com/v18.0/${userId}`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${pageAccessToken}`,
      },
      params: {
        fields: "first_name,last_name,profile_pic",
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    logger.error("Error getting Messenger user profile:", error);
    return null;
  }
}
