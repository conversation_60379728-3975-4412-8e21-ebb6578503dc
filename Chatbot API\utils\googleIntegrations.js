import { supabase } from "./supabase.js";
import { google } from "googleapis";
import logger from "./logger.js";

/**
 * Get Google integrations for a user
 */
export async function getGoogleIntegrations(authId) {
  try {
    const { data, error } = await supabase
      .from("google_integrations")
      .select("*")
      .eq("auth_id", authId);

    if (error) {
      logger.error("Error fetching Google integrations:", error);
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error("Error in getGoogleIntegrations:", error);
    throw error;
  }
}

/**
 * Get specific Google service integration
 */
export async function getGoogleServiceIntegration(authId, serviceType) {
  try {
    const { data, error } = await supabase
      .from("google_integrations")
      .select("*")
      .eq("auth_id", authId)
      .eq("service_type", serviceType)
      .single();

    if (error && error.code !== "PGRST116") {
      logger.error(`Error fetching Google ${serviceType} integration:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error in getGoogleServiceIntegration for ${serviceType}:`, error);
    throw error;
  }
}

/**
 * Save or update Google integration
 */
export async function saveGoogleIntegration(authId, serviceType, config) {
  try {
    console.log(`📊 saveGoogleIntegration called | AuthId: ${authId} | ServiceType: ${serviceType}`);
    console.log(`📊 Config:`, config);

    const integrationData = {
      auth_id: authId,
      service_type: serviceType,
      is_enabled: config.isEnabled !== undefined ? config.isEnabled : true,
      updated_at: new Date().toISOString(),
    };

    // Add service-specific fields
    switch (serviceType) {
      case "sheets":
        integrationData.spreadsheet_id = config.spreadsheetId;
        integrationData.spreadsheet_url = config.spreadsheetUrl;
        integrationData.worksheet_name = config.worksheetName || "Data";
        integrationData.spreadsheet_columns = config.spreadsheetColumns || [
          'OrderID', 'Date','Time', 'Customer Phone', 'Customer Name', 'Address', 'Product', 'Variation', 'Quantity', 'Total Amount', 'Status'
        ];
        break;
      case "calendar":
        integrationData.calendar_id = config.calendarId;
        integrationData.calendar_name = config.calendarName;
        break;
    }

    if (config.serviceAccountEmail) {
      integrationData.service_account_email = config.serviceAccountEmail;
    }

    console.log(`📊 Integration data to save:`, integrationData);

    const { data, error } = await supabase
      .from("google_integrations")
      .upsert(integrationData, {
        onConflict: 'auth_id,service_type'
      })
      .select()
      .single();

    if (error) {
      console.error(`❌ Database error saving Google ${serviceType} integration:`, error);
      logger.error(`Error saving Google ${serviceType} integration:`, error);
      throw error;
    }

    console.log(`✅ Google ${serviceType} integration saved to database:`, data);
    return data;
  } catch (error) {
    console.error(`❌ Error in saveGoogleIntegration for ${serviceType}:`, error);
    logger.error(`Error in saveGoogleIntegration for ${serviceType}:`, error);
    throw error;
  }
}

/**
 * Toggle Google service integration on/off
 */
export async function toggleGoogleIntegration(authId, serviceType, isEnabled) {
  try {
    const { data, error } = await supabase
      .from("google_integrations")
      .update({
        is_enabled: isEnabled,
        updated_at: new Date().toISOString(),
      })
      .eq("auth_id", authId)
      .eq("service_type", serviceType)
      .select()
      .single();

    if (error) {
      logger.error(`Error toggling Google ${serviceType} integration:`, error);
      throw error;
    }

    return data;
  } catch (error) {
    logger.error(`Error in toggleGoogleIntegration for ${serviceType}:`, error);
    throw error;
  }
}

/**
 * Validate Google Sheets access
 */
export async function validateGoogleSheetsAccess(spreadsheetId, worksheetName = "Data") {
  try {
    if (!process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
      throw new Error("Google service account not configured");
    }

    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);
    const auth = new google.auth.GoogleAuth({
      credentials: credentials,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const sheets = google.sheets({ version: "v4", auth });

    // Try to read the spreadsheet metadata
    const spreadsheetResponse = await sheets.spreadsheets.get({
      spreadsheetId: spreadsheetId,
    });

    // Check if the worksheet exists
    const worksheet = spreadsheetResponse.data.sheets.find(
      sheet => sheet.properties.title === worksheetName
    );

    if (!worksheet) {
      const availableWorksheets = spreadsheetResponse.data.sheets.map(s => s.properties.title);
      return {
        isValid: false,
        error: `Worksheet "${worksheetName}" not found in spreadsheet`,
        availableWorksheets: availableWorksheets,
        suggestedWorksheet: availableWorksheets[0], // Suggest the first available worksheet
      };
    }

    // Try to read a small range to test access
    await sheets.spreadsheets.values.get({
      spreadsheetId: spreadsheetId,
      range: `${worksheetName}!A1:A1`,
    });

    return {
      isValid: true,
      spreadsheetInfo: {
        title: spreadsheetResponse.data.properties.title,
        worksheets: spreadsheetResponse.data.sheets.map(s => s.properties.title),
      },
    };
  } catch (error) {
    logger.error("Error validating Google Sheets access:", error);
    return {
      isValid: false,
      error: error.message,
    };
  }
}

/**
 * Extract spreadsheet ID from Google Sheets URL
 */
export function extractSpreadsheetIdFromUrl(url) {
  try {
    // Match various Google Sheets URL formats
    const patterns = [
      /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/,
      /\/spreadsheets\/u\/\d+\/d\/([a-zA-Z0-9-_]+)/,
      /key=([a-zA-Z0-9-_]+)/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }

    // If no pattern matches, assume the input is already a spreadsheet ID
    if (/^[a-zA-Z0-9-_]+$/.test(url)) {
      return url;
    }

    return null;
  } catch (error) {
    logger.error("Error extracting spreadsheet ID from URL:", error);
    return null;
  }
}

/**
 * Save data to Google Sheets using new integration system
 */
export async function saveToGoogleSheetsNew(authId, rowData) {
  try {
    // Get the user's Google Sheets integration
    const integration = await getGoogleServiceIntegration(authId, "sheets");
    
    if (!integration || !integration.is_enabled) {
      throw new Error("Google Sheets integration not configured or disabled");
    }

    if (!integration.spreadsheet_id) {
      throw new Error("Spreadsheet ID not configured");
    }

    if (!process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
      throw new Error("Google service account not configured");
    }

    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);
    const auth = new google.auth.GoogleAuth({
      credentials: credentials,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const sheets = google.sheets({ version: "v4", auth });

    // Append to sheet
    const result = await sheets.spreadsheets.values.append({
      spreadsheetId: integration.spreadsheet_id,
      range: `${integration.worksheet_name}!A1:Z1000`,
      valueInputOption: "RAW",
      resource: {
        values: [rowData],
      },
    });

    // Update last sync time
    await supabase
      .from("google_integrations")
      .update({
        last_sync_at: new Date().toISOString(),
        sync_status: "active",
        error_message: null,
      })
      .eq("auth_id", authId)
      .eq("service_type", "sheets");

    return result.data;
  } catch (error) {
    logger.error("Error saving to Google Sheets (new system):", error);
    
    // Update error status
    await supabase
      .from("google_integrations")
      .update({
        sync_status: "error",
        error_message: error.message,
      })
      .eq("auth_id", authId)
      .eq("service_type", "sheets")
      .catch(updateError => {
        logger.error("Error updating sync status:", updateError);
      });

    throw error;
  }
}

/**
 * Read data from Google Sheets using new integration system
 */
export async function readFromGoogleSheetsNew(authId, range = null) {
  try {
    // Get the user's Google Sheets integration
    const integration = await getGoogleServiceIntegration(authId, "sheets");
    
    if (!integration || !integration.is_enabled) {
      throw new Error("Google Sheets integration not configured or disabled");
    }

    if (!integration.spreadsheet_id) {
      throw new Error("Spreadsheet ID not configured");
    }

    if (!process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
      throw new Error("Google service account not configured");
    }

    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);
    const auth = new google.auth.GoogleAuth({
      credentials: credentials,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const sheets = google.sheets({ version: "v4", auth });

    // Use provided range or default to read all data
    const readRange = range || `${integration.worksheet_name}!A1:Z1000`;

    // Read from sheet
    const result = await sheets.spreadsheets.values.get({
      spreadsheetId: integration.spreadsheet_id,
      range: readRange,
    });

    return result.data.values || [];
  } catch (error) {
    logger.error("Error reading from Google Sheets (new system):", error);
    throw error;
  }
}
