import { createAdminApiClient, tokenManager } from "./api.js";

// Create admin API client
const api = createAdminApiClient();

// Admin service based on the latest admin API
export const adminService = {
  // ===== SUBSCRIPTION PLANS MANAGEMENT =====

  // Get all available subscription plans
  async getPlans() {
    try {
      const response = await api.get("/api/admin/plans");
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get subscription plans",
      );
    }
  },

  // Update subscription plan limits
  async updatePlan(planName, updates) {
    try {
      const response = await api.put(`/api/admin/plans/${planName}`, updates);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update subscription plan",
      );
    }
  },

  // ===== USER SUBSCRIPTIONS MANAGEMENT =====

  // Get user subscriptions with filters
  async getUsers(params = {}) {
    try {
      const queryParams = new URLSearchParams();

      // Add all supported filter parameters
      if (params.authId) queryParams.append("authId", params.authId);
      if (params.plan) queryParams.append("plan", params.plan);
      if (params.page) queryParams.append("page", params.page || "1");
      if (params.limit) queryParams.append("limit", params.limit || "50");
      if (params.sortBy)
        queryParams.append("sortBy", params.sortBy || "created_at");
      if (params.sortOrder)
        queryParams.append("sortOrder", params.sortOrder || "desc");

      const response = await api.get(
        `/api/admin/users?${queryParams.toString()}`,
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get user subscriptions",
      );
    }
  },

  // Update user subscription (simplified)
  async updateUserSubscription(authId, subscriptionData) {
    try {
      const response = await api.put(
        `/api/admin/users/${authId}/subscription`,
        subscriptionData,
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to update user subscription",
      );
    }
  },

  // Set user subscription plan (new endpoint)
  async setUserSubscription(authId, subscriptionData) {
    try {
      const response = await api.post(
        `/api/admin/users/${authId}/set-subscription`,
        subscriptionData,
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to set user subscription",
      );
    }
  },

  // Reset subscription to free plan (using cancel endpoint)
  async resetSubscription(authId, notes = "") {
    try {
      const response = await api.post(
        `/api/admin/users/${authId}/cancel-subscription`,
        {
          reason: notes || "Admin reset subscription to free plan",
        },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to reset subscription",
      );
    }
  },

  // Suspend or unsuspend user
  async suspendUser(userId, suspend) {
    try {
      const response = await api.post(
        `/api/auth/admin/user/${userId}/suspend`,
        { suspend },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to suspend/unsuspend user",
      );
    }
  },

  // Add or remove months from subscription
  async adjustSubscriptionMonths(authId, months, notes = "") {
    try {
      const response = await api.post(
        `/api/admin/users/${authId}/extend-subscription`,
        {
          months: months,
          days: 0,
          notes: notes || `Admin adjusted subscription by ${months} months`,
        },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to adjust subscription",
      );
    }
  },

  // Set subscription expiry date - now just updates plan
  async setSubscriptionExpiry(authId, endDate, notes = "") {
    try {
      // Since we don't track dates anymore, we'll just update to popular plan
      const response = await api.post(
        `/api/admin/users/${authId}/set-subscription`,
        {
          plan: "popular",
          notes: notes || "Admin set subscription (date tracking removed)",
        },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to set subscription expiry",
      );
    }
  },

  // ===== QUOTA MANAGEMENT =====

  // Get user quotas and usage
  async getUserQuotas(authId) {
    try {
      const response = await api.get(`/api/admin/users/${authId}/quotas`);
      return response.data;
    } catch (error) {
      console.error("getUserQuotas error:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error status:", error.response?.status);
      throw new Error(
        error.response?.data?.error ||
          `Failed to get user quotas (${error.response?.status || "No status"}): ${error.message}`,
      );
    }
  },

  // Get user statistics
  async getUserStats(authId, startDate = null, endDate = null) {
    try {
      const queryParams = new URLSearchParams();
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);

      const response = await api.get(
        `/api/admin/users/${authId}/stats${queryParams.toString() ? `?${queryParams.toString()}` : ""}`,
      );
      return response.data;
    } catch (error) {
      console.error("getUserStats error:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error status:", error.response?.status);
      throw new Error(
        error.response?.data?.error ||
          `Failed to get user statistics (${error.response?.status || "No status"}): ${error.message}`,
      );
    }
  },

  // Reset user monthly usage
  async resetUserUsage(authId, resetData = {}) {
    try {
      const response = await api.post(
        `/api/admin/users/${authId}/reset-usage`,
        resetData,
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to reset user usage",
      );
    }
  },

  // ===== SYSTEM MAINTENANCE =====

  // Run subscription expiry check for overdue subscriptions
  async expireSubscriptions() {
    try {
      const response = await api.post("/api/admin/maintenance/expire-overdue");
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to expire overdue subscriptions",
      );
    }
  },

  // Get system statistics
  async getSystemStats() {
    try {
      const response = await api.get("/api/admin/stats");
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get system statistics",
      );
    }
  },

  // Get subscription history
  async getSubscriptionHistory(authId, params = {}) {
    try {
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page || "1");
      if (params.limit) queryParams.append("limit", params.limit || "20");

      const response = await api.get(
        `/api/admin/users/${authId}/history?${queryParams.toString()}`,
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get subscription history",
      );
    }
  },

  // Get admin action logs
  async getAdminLogs(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page || "1");
      if (params.limit) queryParams.append("limit", params.limit || "50");
      if (params.actionType)
        queryParams.append("actionType", params.actionType);
      if (params.targetAuthId)
        queryParams.append("targetAuthId", params.targetAuthId);
      if (params.adminId) queryParams.append("adminId", params.adminId);

      const response = await api.get(
        `/api/admin/logs?${queryParams.toString()}`,
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get admin action logs",
      );
    }
  },

  // ===== WHATSAPP SYSTEM MANAGEMENT =====
  // Get WhatsApp system status and configuration
  async getWhatsAppStatus() {
    try {
      const response = await api.get("/api/admin/whatsapp-status");
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error || "Failed to get WhatsApp system status",
      );
    }
  },

  // ===== HELPER METHODS =====

  // Check if user has admin access
  hasAdminAccess() {
    const token = tokenManager.getToken();
    return !!token; // Since we're using env vars, just check if token exists
  },

  // Set admin credentials
  setAdminCredentials(adminId, adminToken) {
    tokenManager.setAdminId(adminId);
    tokenManager.setToken(adminToken);
  },

  // Clear admin credentials
  clearAdminCredentials() {
    tokenManager.removeAdminId();
    tokenManager.removeToken();
  },

  // Validate admin access before making requests
  validateAdminAccess() {
    if (!this.hasAdminAccess()) {
      throw new Error("Admin access required. Please login as admin.");
    }
  },



  // ===== USER IMPERSONATION METHODS =====

  // Start impersonating a user
  async startImpersonation(authId, notes = null) {
    this.validateAdminAccess();
    const response = await api.post(`/api/admin/impersonate/${authId}`, {
      notes,
    });
    return response.data;
  },

  // Stop impersonating a user
  async stopImpersonation(sessionId) {
    this.validateAdminAccess();
    const response = await api.post(
      `/api/admin/stop-impersonation/${sessionId}`,
    );
    return response.data;
  },

  // Get current impersonation status
  async getImpersonationStatus() {
    this.validateAdminAccess();
    const response = await api.get("/api/admin/impersonation-status");
    return response.data;
  },

  // Get impersonated user data
  async getImpersonatedUser(sessionId) {
    this.validateAdminAccess();
    const response = await api.get(`/api/admin/impersonated-user/${sessionId}`);
    return response.data;
  },

  // Get impersonation history
  async getImpersonationHistory(page = 1, limit = 50) {
    this.validateAdminAccess();
    const response = await api.get("/api/admin/impersonation-history", {
      params: { page, limit },
    });
    return response.data;
  },
};

// Export as default for compatibility
export default adminService;
