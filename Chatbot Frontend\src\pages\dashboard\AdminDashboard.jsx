import { useState, useEffect } from "react";
import { Routes, Route, Link, useLocation, Navigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import adminService from "../../services/admin";
import { Badge } from "../../components/ui/badge";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { handleApiError } from "../../utils/common";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import {
  Users,
  CreditCard,
  BarChart3,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import Icon from "../../components/ui/icon";

// Import the page components
import AdminOverview from "./AdminOverview";
import UserManagement from "./UserManagement";
import SubscriptionManagement from "./SubscriptionManagement";
import AdminLogs from "./AdminLogs";

export default function AdminDashboard() {
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);
  const { showError, showSuccess } = useAlertMigration();
  const { startImpersonation } = useAuth();

  // Data states
  const [systemOverview, setSystemOverview] = useState(null);
  const [users, setUsers] = useState([]);
  const [subscriptionUsers, setSubscriptionUsers] = useState([]);
  const [subscriptionStatistics, setSubscriptionStatistics] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState([]);
  const [subscriptionHistoryLoading, setSubscriptionHistoryLoading] =
    useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [showExpireModal, setShowExpireModal] = useState(false);
  const [subscriptionModalData, setSubscriptionModalData] = useState({
    userId: null,
    action: "set", // 'set', 'extend', 'reset'
    plan: "free",
    months: 1,
    endDate: "",
    notes: "",
  });

  // Confirmation modals state
  const [showResetQuotaModal, setShowResetQuotaModal] = useState(false);
  const [showChangePlanModal, setShowChangePlanModal] = useState(false);
  const [confirmationData, setConfirmationData] = useState({
    userId: null,
    userName: null,
    newPlan: null,
    resetData: {},
  });

  // Fetch system overview
  const fetchSystemOverview = async () => {
    try {
      const data = await adminService.getSystemStats();
      setSystemOverview(data);
    } catch (err) {
      console.error("Error fetching system overview:", err);
      showError("Failed to load system overview");
    }
  };

  // Fetch all users
  const fetchUsers = async () => {
    try {
      const response = await adminService.getUsers({ limit: 100 }); // Increase limit to see more users
      setUsers(response.data || []);
    } catch (err) {
      console.error("Error fetching users:", err);
      showError("Failed to load users");
    }
  };

  // Fetch subscription users (all users with subscription data)
  const fetchSubscriptionUsers = async () => {
    try {
      const response = await adminService.getUsers({ limit: 100 }); // Get all users
      setSubscriptionUsers(response.data || []);
    } catch (err) {
      console.error("Error fetching subscription users:", err);
      showError("Failed to load subscription users");
    }
  };

  // Fetch subscription statistics
  const fetchSubscriptionStatistics = async () => {
    try {
      const data = await adminService.getSystemStats();
      setSubscriptionStatistics(data);
    } catch (err) {
      console.error("Error fetching subscription statistics:", err);
      showError("Failed to load subscription statistics");
    }
  };

  // Fetch subscription history
  const fetchSubscriptionHistory = async (userId) => {
    try {
      setSubscriptionHistoryLoading(true);
      const response = await adminService.getSubscriptionHistory(userId, {
        limit: 10,
      });
      setSubscriptionHistory(response.data || []);
    } catch (err) {
      console.error("Error fetching subscription history:", err);
      setSubscriptionHistory([]);
    } finally {
      setSubscriptionHistoryLoading(false);
    }
  };

  // Fetch user details (using user quotas for now)
  const fetchUserDetails = async (userId) => {
    try {
      // First try to find user in existing users list
      const existingUser =
        users.find((u) => u.auth_id === userId) ||
        subscriptionUsers.find((u) => u.auth_id === userId);

      let data = null;
      let apiError = null;

      // Try to get detailed statistics from API
      try {
        const response = await adminService.getUserStats(userId);
        data = response.data;
      } catch (statsErr) {
        apiError = statsErr.message;

        // Fallback to basic user data
        if (existingUser) {
          data = {
            profile: {
              authId: existingUser.auth_id,
              displayName: existingUser.display_name,
              email: existingUser.email,
              plan: existingUser.plan,
              subscriptionEndDate: existingUser.subscription_end_date,
              isActive: !existingUser.is_suspended,
              createdAt: existingUser.created_at,
            },
            usage: {
              contacts: 0,
              knowledgeBase: 0,
              messages: {
                total: 0,
                incoming: 0,
                outgoing: 0,
                totalTokens: 0,
              },
              dailyMessageAverage: 0,
              currentMonth: {
                messages_sent: 0,
                messages_received: 0,
                total_contacts: 0,
                new_contacts: 0,
                knowledge_base_queries: 0,
              },
            },
            integrations: {
              whatsapp: null,
            },
            subscriptionHistory: [],
          };
        } else {
          throw new Error(
            `User not found and API call failed: ${statsErr.message}`,
          );
        }
      }

      // Ensure the data structure is compatible with the modal
      const userDetailsFormatted = {
        profile: {
          display_name: data.profile.displayName || "N/A",
          email: data.profile.email || "N/A",
          plan: data.profile.plan || "N/A",
          phone: data.profile.phone || "N/A", // Not provided in new API
          created_at: data.profile.createdAt || null,
          auth_id: data.profile.authId,
        },
        statistics: {
          knowledgeEntries: data.usage.knowledgeBase || 0,
          contacts: data.usage.contacts || 0,
          messages: {
            total: data.usage.messages.total || 0,
            incoming: data.usage.messages.incoming || 0,
            outgoing: data.usage.messages.outgoing || 0,
          },
          totalTokens: data.usage.messages.totalTokens || 0,
          thisMonthTokens: data.usage.currentMonth.total_tokens || 0,
          dailyMessageAverage: data.usage.dailyMessageAverage || 0,
          currentMonth: {
            messagesSent: data.usage.currentMonth.messages_sent || 0,
            messagesReceived: data.usage.currentMonth.messages_received || 0,
            totalContacts: data.usage.currentMonth.total_contacts || 0,
            newContacts: data.usage.currentMonth.new_contacts || 0,
            knowledgeBaseQueries:
              data.usage.currentMonth.knowledge_base_queries || 0,
          },
        },
        quotas: {}, // Quotas are now part of the usage data
        subscription: {
          plan: data.profile.plan || "N/A",
          endDate: data.profile.subscriptionEndDate || "N/A",
          isExpired: !data.profile.isActive,
          isSuspended: !data.profile.isActive,
        },
        integrations: {
          whatsapp: data.integrations.whatsapp,
        },
        subscriptionHistory: data.subscriptionHistory || [],
        raw: data, // Keep the raw response for debugging
        apiError: apiError, // Track if there was an API error
      };

      setUserDetails(userDetailsFormatted);
      setSelectedUser(userId);

      // Fetch subscription history after setting user details
      await fetchSubscriptionHistory(userId);

      if (apiError) {
        showSuccess(`User details loaded from cache (API error: ${apiError})`);
      }
    } catch (err) {
      handleApiError(err, showError, "Failed to load user details");
    }
  };

  // Change user plan
  const handleChangePlan = async (userId, newPlan) => {
    // Find user to get display name
    const user = users.find((u) => u.auth_id === userId);
    setConfirmationData({
      userId,
      userName: user?.display_name || user?.email || userId,
      newPlan,
      resetData: {},
    });
    setShowChangePlanModal(true);
  };

  // Confirm change plan
  const confirmChangePlan = async () => {
    const { userId, newPlan } = confirmationData;

    try {
      setLoading(true);
      await adminService.updateUserSubscription(userId, { plan: newPlan });
      showSuccess(`User plan changed to ${newPlan} successfully`);
      await fetchUsers();
      if (selectedUser === userId) {
        await fetchUserDetails(userId);
      }
      setShowChangePlanModal(false);
    } catch (err) {
      showError(`Failed to change user plan: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Suspend/Unsuspend user
  const handleSuspendUser = async (userId, suspend) => {
    try {
      setLoading(true);
      await adminService.suspendUser(userId, suspend);
      showSuccess(`User ${suspend ? "suspended" : "unsuspended"} successfully`);
      await fetchUsers();
      if (selectedUser === userId) {
        await fetchUserDetails(userId);
      }
    } catch (err) {
      showError(
        `Failed to ${suspend ? "suspend" : "unsuspend"} user: ${err.message}`,
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle subscription update
  const handleSubscriptionUpdate = async () => {
    const { userId, action, plan, months, endDate, notes } =
      subscriptionModalData;

    if (!userId) return;

    try {
      setLoading(true);
      switch (action) {
        case "set":
          if (!plan) {
            showError("Please select a plan");
            return;
          }
          await adminService.setUserSubscription(userId, {
            plan,
            endDate,
            notes,
          });
          showSuccess(`User plan set to ${plan} successfully`);
          break;

        case "extend":
          if (!months || months < 1) {
            showError("Please enter a valid number of months");
            return;
          }
          await adminService.adjustSubscriptionMonths(userId, months, notes);
          showSuccess(
            `Subscription extended by ${months} month(s) successfully`,
          );
          break;

        case "reduce":
          if (!months || months < 1) {
            showError("Please enter a valid number of months");
            return;
          }
          await adminService.adjustSubscriptionMonths(userId, -months, notes);
          showSuccess(
            `Subscription reduced by ${months} month(s) successfully`,
          );
          break;

        case "reset":
          if (
            !window.confirm(
              "Are you sure you want to reset this user to free plan?",
            )
          )
            return;
          await adminService.resetSubscription(userId, notes);
          showSuccess("User subscription reset to free plan successfully");
          break;

        case "expire":
          if (!endDate) {
            showError("Please select an expiry date");
            return;
          }
          await adminService.setSubscriptionExpiry(userId, endDate, notes);
          showSuccess("Subscription expiry date set successfully");
          break;

        default:
          showError("Invalid action");
          return;
      }

      await fetchUsers();
      await fetchSubscriptionUsers();
      await fetchSubscriptionStatistics();
      setShowSubscriptionModal(false);
    } catch (err) {
      showError(`Failed to update subscription: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Expire subscriptions manually
  const handleExpireSubscriptions = async () => {
    try {
      setLoading(true);
      const result = await adminService.expireSubscriptions();
      showSuccess(
        `Expired ${result.expiredCount || 0} subscriptions successfully`,
      );
      await fetchUsers();
      await fetchSubscriptionUsers();
      await fetchSubscriptionStatistics();
      setShowExpireModal(false);
    } catch (err) {
      showError(`Failed to expire subscriptions: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Reset user quota usage
  const handleResetUserQuota = async (userId, resetData = {}) => {
    // Find user to get display name
    const user = users.find((u) => u.auth_id === userId);
    setConfirmationData({
      userId,
      userName: user?.display_name || user?.email || userId,
      newPlan: null,
      resetData,
    });
    setShowResetQuotaModal(true);
  };

  // Confirm reset quota
  const confirmResetQuota = async () => {
    const { userId, resetData } = confirmationData;

    try {
      setLoading(true);
      await adminService.resetUserUsage(userId, {
        reason: "Admin reset via dashboard",
        resetAll: true,
        ...resetData,
      });
      showSuccess(`User quota usage reset successfully for ${userId}`);
      await fetchUsers();
      if (selectedUser === userId) {
        await fetchUserDetails(userId);
      }
      setShowResetQuotaModal(false);
    } catch (err) {
      showError(`Failed to reset user quota: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handler functions for child components
  const handleRefreshData = async () => {
    await Promise.all([
      fetchSystemOverview(),
      fetchUsers(),
      fetchSubscriptionUsers(),
      fetchSubscriptionStatistics(),
    ]);
  };

  const handleCloseUserDetails = () => {
    setUserDetails(null);
    setSelectedUser(null);
  };

  const handleOpenSubscriptionModal = (
    userId,
    action,
    currentPlan = "free",
  ) => {
    setSubscriptionModalData({
      userId,
      action,
      plan: currentPlan,
      months: 1,
      endDate: "",
      notes: "",
    });
    setShowSubscriptionModal(true);
  };

  const handleCloseSubscriptionModal = () => {
    setShowSubscriptionModal(false);
  };

  const handleOpenExpireModal = () => {
    setShowExpireModal(true);
  };

  const handleCloseExpireModal = () => {
    setShowExpireModal(false);
  };

  // Handle user impersonation
  const handleImpersonation = async (authId, userName) => {
    try {
      await startImpersonation(authId, `Impersonating user: ${userName}`);
      showSuccess(
        `Successfully started impersonating ${userName}. You can now access their dashboard.`,
      );
      // Optionally navigate to the user dashboard
      // navigate('/dashboard');
    } catch (error) {
      console.error("Failed to start impersonation:", error);
      showError(error.message || "Failed to start impersonation");
    }
  };

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchSystemOverview(),
          fetchUsers(),
          fetchSubscriptionUsers(),
          fetchSubscriptionStatistics(),
        ]);
      } catch {
        showError("Failed to load admin data");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Navigation configuration
  const navigationItems = [
    {
      id: "overview",
      label: "System Overview",
      path: "/dashboard/admin",
      icon: "BarChart3",
    },
    {
      id: "users",
      label: "User Management",
      path: "/dashboard/admin/users",
      icon: "Users",
    },
    {
      id: "subscriptions",
      label: "Subscription Management",
      path: "/dashboard/admin/subscriptions",
      icon: "CreditCard",
    },
    {
      id: "logs",
      label: "Admin Logs",
      path: "/dashboard/admin/logs",
      icon: "AlertCircle",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Admin Control Panel
            </h1>
            <p className="text-gray-600">
              Manage users, monitor system performance, and oversee operations
            </p>
          </div>
        </div>
      </div>

      {/* Alert Messages now handled by global AlertContainer */}

      {/* Navigation Links */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {navigationItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <Link
                key={item.id}
                to={item.path}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 ${
                  isActive
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <Icon name={item.icon} className="w-5 h-5" />
                {item.label}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Route Content */}
      <Routes>
        <Route
          path="/"
          element={
            <AdminOverview
              systemOverview={systemOverview}
              users={users}
              loading={showLoadingState}
              onRefreshData={handleRefreshData}
              onSetSuccess={showSuccess}
            />
          }
        />
        <Route
          path="/users"
          element={
            <UserManagement
              users={users}
              onFetchUsers={fetchUsers}
              onFetchUserDetails={fetchUserDetails}
              onChangePlan={handleChangePlan}
              onSuspendUser={handleSuspendUser}
              onManageSubscription={handleOpenSubscriptionModal}
              onResetUserQuota={handleResetUserQuota}
              onImpersonate={handleImpersonation}
              userDetails={userDetails}
              selectedUser={selectedUser}
              subscriptionHistory={subscriptionHistory}
              subscriptionHistoryLoading={subscriptionHistoryLoading}
              onCloseUserDetails={handleCloseUserDetails}
            />
          }
        />
        <Route
          path="/subscriptions"
          element={
            <SubscriptionManagement
              subscriptionUsers={subscriptionUsers}
              subscriptionStatistics={subscriptionStatistics}
              loading={loading}
              onFetchSubscriptionUsers={fetchSubscriptionUsers}
              onOpenSubscriptionModal={handleOpenSubscriptionModal}
              onOpenExpireModal={handleOpenExpireModal}
              showSubscriptionModal={showSubscriptionModal}
              showExpireModal={showExpireModal}
              subscriptionModalData={subscriptionModalData}
              onCloseSubscriptionModal={handleCloseSubscriptionModal}
              onCloseExpireModal={handleCloseExpireModal}
              onHandleSubscriptionUpdate={handleSubscriptionUpdate}
              onHandleExpireSubscriptions={handleExpireSubscriptions}
              onSetSubscriptionModalData={setSubscriptionModalData}
            />
          }
        />
        <Route path="/logs" element={<AdminLogs />} />
      </Routes>

      {/* Change Plan Confirmation Modal */}
      {showChangePlanModal && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!loading) {
                setShowChangePlanModal(false);
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-xl transform transition-all">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Icon name="CreditCard" className="h-5 w-5 text-blue-600" />
                Change User Plan
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to change{" "}
                <strong>"{confirmationData.userName}"</strong>'s plan to{" "}
                <strong>"{confirmationData.newPlan}"</strong>?
              </p>
              <div className="flex gap-3">
                <button
                  onClick={confirmChangePlan}
                  className="flex-1 bg-blue-600 text-white px-4 py-2.5 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Changing Plan...
                    </>
                  ) : (
                    <>
                      <Icon name="Check" className="h-4 w-4" />
                      Change Plan
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowChangePlanModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reset Quota Confirmation Modal */}
      {showResetQuotaModal && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div
            className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity"
            onClick={() => {
              if (!loading) {
                setShowResetQuotaModal(false);
              }
            }}
          />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-xl transform transition-all">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Icon name="AlertCircle" className="h-5 w-5 text-red-600" />
                Reset User Quota
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to reset all usage quotas for{" "}
                <strong>"{confirmationData.userName}"</strong>?
                <br />
                <br />
                <span className="text-red-600 font-medium">
                  This action cannot be undone.
                </span>
              </p>
              <div className="flex gap-3">
                <button
                  onClick={confirmResetQuota}
                  className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Resetting...
                    </>
                  ) : (
                    <>
                      <Icon name="AlertCircle" className="h-4 w-4" />
                      Reset Quota
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowResetQuotaModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
