import { google } from 'googleapis';

/**
 * Google Sheets utility functions
 * Handles reading and writing to Google Sheets
 */

// Helper function to read data from Google Sheets
export async function readFromGoogleSheets(customer, range = null) {
  try {
    if (!customer.spreadsheet_id) {
      throw new Error(
        "Google Sheets spreadsheet ID not configured for this customer",
      );
    }

    if (!process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
      throw new Error(
        "Google service account not configured in environment variables",
      );
    }

    // Parse service account credentials from environment variables
    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);

    // Initialize Google Sheets API
    const auth = new google.auth.GoogleAuth({
      credentials: credentials,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const sheets = google.sheets({ version: "v4", auth });

    // Use provided range or default to read all data
    const readRange = range || `${customer.worksheet_name}!A1:Z1000`;

    // Read from sheet
    const result = await sheets.spreadsheets.values.get({
      spreadsheetId: customer.spreadsheet_id,
      range: readRange,
    });

    return result.data.values || [];
  } catch (error) {
    console.error("Error reading from Google Sheets:", error);
    throw error;
  }
}

// Helper function to save data to Google Sheets
export async function saveToGoogleSheets(customer, rowData) {
  try {
    if (!customer.spreadsheet_id) {
      throw new Error(
        "Google Sheets spreadsheet ID not configured for this customer",
      );
    }

    if (!process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
      throw new Error(
        "Google service account not configured in environment variables",
      );
    }

    // Parse service account credentials from environment variables
    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);

    // Initialize Google Sheets API
    const auth = new google.auth.GoogleAuth({
      credentials: credentials,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const sheets = google.sheets({ version: "v4", auth });

    // Append to sheet
    const result = await sheets.spreadsheets.values.append({
      spreadsheetId: customer.spreadsheet_id,
      range: `${customer.worksheet_name}!A1:Z1000`,
      valueInputOption: "RAW",
      resource: {
        values: [rowData],
      },
    });

    return result.data;
  } catch (error) {
    console.error("Error saving to Google Sheets:", error);
    throw error;
  }
}

// Helper function to find order/booking in Google Sheets by phone number
export async function findOrderInGoogleSheets(customer, phoneNumber, orderId = null) {
  try {
    const sheetData = await readFromGoogleSheets(customer);
    
    if (!sheetData || sheetData.length === 0) {
      return null;
    }

    // First row should contain headers
    const headers = sheetData[0];
    const rows = sheetData.slice(1);

    // Find phone number column index
    const phoneColumnIndex = headers.findIndex(header => 
      header.toLowerCase().includes('phone') || 
      header.toLowerCase().includes('number') ||
      header.toLowerCase().includes('contact')
    );

    // Find order ID column index (if exists)
    const orderIdColumnIndex = headers.findIndex(header => 
      header.toLowerCase().includes('order') && header.toLowerCase().includes('id') ||
      header.toLowerCase().includes('orderid') ||
      header.toLowerCase().includes('order_id')
    );

    if (phoneColumnIndex === -1) {
      console.warn("Phone number column not found in Google Sheets");
      return null;
    }

    // Search for matching orders/bookings
    const matchingRows = rows.filter((row, index) => {
      const rowPhoneNumber = row[phoneColumnIndex];
      const rowOrderId = orderIdColumnIndex !== -1 ? row[orderIdColumnIndex] : null;
      
      // Clean phone numbers for comparison
      const cleanRowPhone = rowPhoneNumber ? rowPhoneNumber.replace(/\D/g, '') : '';
      const cleanSearchPhone = phoneNumber.replace(/\D/g, '');
      
      // Match by phone number and optionally by order ID
      const phoneMatch = cleanRowPhone.includes(cleanSearchPhone) || cleanSearchPhone.includes(cleanRowPhone);
      const orderMatch = !orderId || !rowOrderId || rowOrderId.toString() === orderId.toString();
      
      return phoneMatch && orderMatch;
    });

    // Convert matching rows to objects with headers
    const results = matchingRows.map((row, index) => {
      const rowData = {};
      headers.forEach((header, headerIndex) => {
        rowData[header] = row[headerIndex] || '';
      });
      rowData._rowIndex = rows.indexOf(row) + 2; // +2 because we removed header and sheets are 1-indexed
      return rowData;
    });

    return results;
  } catch (error) {
    console.error("Error finding order in Google Sheets:", error);
    return null;
  }
}

// Helper function to get order status from Google Sheets
export async function getOrderStatusFromGoogleSheets(customer, phoneNumber, orderId = null) {
  try {
    const orders = await findOrderInGoogleSheets(customer, phoneNumber, orderId);
    
    if (!orders || orders.length === 0) {
      return {
        found: false,
        message: "No orders found for this phone number."
      };
    }

    // Format the response
    const orderSummaries = orders.map(order => {
      const summary = {
        rowIndex: order._rowIndex,
        details: {}
      };

      // Extract common fields
      Object.keys(order).forEach(key => {
        if (key !== '_rowIndex' && order[key]) {
          summary.details[key] = order[key];
        }
      });

      // Generate Order ID: use existing order ID if available, otherwise use row ID as fallback
      const existingOrderId = summary.details['Order ID'] || summary.details['OrderID'] || summary.details['order_id'];
      if (!existingOrderId || existingOrderId.trim() === '') {
        summary.details['Order ID'] = `ROW${order._rowIndex}`;
        console.log(`📊 No Order ID found in Google Sheets row ${order._rowIndex}, using fallback: ROW${order._rowIndex}`);
      }

      // Verify phone number matches for security
      const rowPhone = summary.details['Customer Phone'] || summary.details['Phone'] || summary.details['phone'];
      const cleanRowPhone = rowPhone ? rowPhone.replace(/\D/g, '') : '';
      const cleanSearchPhone = phoneNumber.replace(/\D/g, '');

      if (!cleanRowPhone.includes(cleanSearchPhone) && !cleanSearchPhone.includes(cleanRowPhone)) {
        console.warn(`📊 Phone number mismatch for row ${order._rowIndex}: ${rowPhone} vs ${phoneNumber}`);
        return null; // Don't return orders that don't match phone number
      }

      return summary;
    }).filter(Boolean); // Remove null entries

    return {
      found: true,
      count: orders.length,
      orders: orderSummaries,
      message: `Found ${orders.length} order(s) for this phone number.`
    };
  } catch (error) {
    console.error("Error getting order status from Google Sheets:", error);
    return {
      found: false,
      message: "Error retrieving order information. Please try again later."
    };
  }
}
