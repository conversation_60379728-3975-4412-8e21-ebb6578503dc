import React, { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-react';

/**
 * Mobile-optimized alert component
 * Features:
 * - Bottom positioning for better mobile UX
 * - Swipe to dismiss
 * - Touch-friendly design
 * - Smooth animations
 * - Auto-dismiss with progress bar
 */
export const MobileAlert = ({ 
  type = 'info', 
  title, 
  message, 
  duration = 4000, 
  onDismiss,
  persistent = false,
  position = 'top' // 'top', 'bottom', 'center'
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);
  const [progress, setProgress] = useState(100);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  // Auto-dismiss timer
  useEffect(() => {
    if (!persistent && duration > 0) {
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev - (100 / (duration / 100));
          return newProgress <= 0 ? 0 : newProgress;
        });
      }, 100);

      const dismissTimer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => {
        clearInterval(progressInterval);
        clearTimeout(dismissTimer);
      };
    }
  }, [duration, persistent]);

  const handleDismiss = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, 300);
  };

  // Touch handlers for swipe to dismiss
  const handleTouchStart = (e) => {
    setTouchStart(e.targetTouches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isUpSwipe = distance > 50;
    const isDownSwipe = distance < -50;
    
    // Dismiss on swipe up (for bottom alerts) or swipe down (for top alerts)
    if ((position === 'bottom' && isUpSwipe) || (position === 'top' && isDownSwipe)) {
      handleDismiss();
    }
  };

  const getStyles = () => {
    const baseStyles = "border-l-4 shadow-lg backdrop-blur-sm";
    
    switch (type) {
      case 'success':
        return `${baseStyles} bg-green-50/95 border-green-500 text-green-800`;
      case 'error':
        return `${baseStyles} bg-red-50/95 border-red-500 text-red-800`;
      case 'warning':
        return `${baseStyles} bg-yellow-50/95 border-yellow-500 text-yellow-800`;
      case 'info':
      default:
        return `${baseStyles} bg-blue-50/95 border-blue-500 text-blue-800`;
    }
  };

  const getPositionStyles = () => {
    switch (position) {
      case 'top':
        return 'top-2 left-2 right-2';
      case 'center':
        return 'top-1/2 left-2 right-2 transform -translate-y-1/2';
      case 'bottom':
      default:
        return 'bottom-2 left-2 right-2';
    }
  };

  const getAnimationClass = () => {
    if (isExiting) {
      return position === 'bottom' ? 'mobile-alert-exit-down' : 'mobile-alert-exit-up';
    }
    return position === 'bottom' ? 'mobile-alert-enter-up' : 'mobile-alert-enter-down';
  };

  if (!isVisible) return null;

  return (
    <div 
      className={`fixed z-50 ${getPositionStyles()} ${getAnimationClass()}` }
      style={{ top: '100px'}}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div className={`rounded-lg px-3 py-2 ${getStyles()} relative overflow-hidden`}>
        {/* Progress bar */}
        {!persistent && duration > 0 && (
          <div className="absolute top-0 left-0 right-0 h-0.5 bg-black/10">
            <div
              className="h-full bg-current transition-all duration-100 ease-linear"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}

        <div className="flex items-center justify-between">
          {/* Content */}
          <div className="flex-1 min-w-0 pr-2">
            {title && (
              <h4 className="font-medium text-xs mb-0.5 leading-tight">
                {title}
              </h4>
            )}
            <p className="text-xs leading-tight">
              {message}
            </p>
          </div>

          {/* Close button */}
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 p-0.5 hover:bg-black/10 rounded transition-colors touch-manipulation"
            aria-label="Dismiss alert"
          >
            <X className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Mobile Alert Container - manages multiple alerts
 */
export const MobileAlertContainer = ({ alerts = [], onDismiss }) => {
  if (alerts.length === 0) return null;

  return (
    <>
      {alerts.map((alert) => (
        <MobileAlert
          key={alert.id}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          duration={alert.duration}
          persistent={alert.persistent}
          position={alert.position || 'bottom'}
          onDismiss={() => onDismiss(alert.id)}
        />
      ))}
    </>
  );
};

export default MobileAlert;
