/**
 * Message validation utilities
 */

/**
 * Check if the gap between the last message and now is more than 1 day
 * @param {Array} chatHistory - Array of chat messages
 * @returns {Object} - { isBlocked: boolean, lastMessageTime: Date|null, daysSinceLastMessage: number }
 */
export function checkMessageGapRestriction(chatHistory) {
  if (!chatHistory || chatHistory.length === 0) {
    return {
      isBlocked: false,
      lastMessageTime: null,
      daysSinceLastMessage: 0,
      message: null
    };
  }

  // Find the most recent message from either user or assistant
  const sortedMessages = [...chatHistory].sort((a, b) => {
    const aTime = new Date(a.timestamp || a.created_at);
    const bTime = new Date(b.timestamp || b.created_at);
    return bTime - aTime;
  });

  const lastMessage = sortedMessages[0];
  if (!lastMessage) {
    return {
      isBlocked: false,
      lastMessageTime: null,
      daysSinceLastMessage: 0,
      message: null
    };
  }

  const lastMessageTime = new Date(lastMessage.timestamp || lastMessage.created_at);
  const now = new Date();
  const timeDifference = now - lastMessageTime;
  const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

  const isBlocked = daysDifference > 1;

  return {
    isBlocked,
    lastMessageTime,
    daysSinceLastMessage: Math.floor(daysDifference),
    message: isBlocked 
      ? `Cannot send message. Last conversation was ${Math.floor(daysDifference)} day${Math.floor(daysDifference) > 1 ? 's' : ''} ago. WhatsApp conversations expire after 24 hours of inactivity.`
      : null
  };
}

/**
 * Format the time difference for display
 * @param {Date} lastMessageTime - The timestamp of the last message
 * @returns {string} - Formatted time difference
 */
export function formatTimeSinceLastMessage(lastMessageTime) {
  if (!lastMessageTime) return '';
  
  const now = new Date();
  const timeDifference = now - lastMessageTime;
  const daysDifference = timeDifference / (1000 * 60 * 60 * 24);
  const hoursDifference = timeDifference / (1000 * 60 * 60);
  const minutesDifference = timeDifference / (1000 * 60);

  if (daysDifference >= 1) {
    const days = Math.floor(daysDifference);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else if (hoursDifference >= 1) {
    const hours = Math.floor(hoursDifference);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (minutesDifference >= 1) {
    const minutes = Math.floor(minutesDifference);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else {
    return 'Just now';
  }
}
